# Switch Component - Quick Usage Guide

## ✅ Đã sửa các vấn đề

1. **onChange hoạt động đúng** - Callback với `(event, checked)` parameters
2. **Required indicator** - Hiển thị dấu `*` đỏ khi `required={true}`
3. **Error message** - Hiển thị dưới switch khi `invalid={true}`
4. **UI/UX cải thiện** - Layout linh hoạt, animations mượt

## 🚀 Cách sử dụng cơ bản

```jsx
import Switch from "~/components/Switch";

// Basic usage
<Switch
    label="Enable notifications"
    onChange={(event, checked) => {
        console.log('Switch changed:', checked);
    }}
/>

// With error state
<Switch
    label="Terms and Conditions"
    required={true}
    invalid={true}
    feedbackInvalid="Please accept the terms and conditions"
    color="danger"
/>

// Different layouts
<Switch
    label="Auto-save"
    direction="row"
    labelPosition="right"
/>

<Switch
    label="Dark Mode"
    direction="column"
    labelPosition="top"
    color="warning"
/>
```

## 📋 Props chính

| Prop | Type | Mô tả |
|------|------|-------|
| `label` | `string` | Text hiển thị |
| `required` | `boolean` | Hiển thị dấu `*` |
| `invalid` | `boolean` | Trạng thái lỗi |
| `feedbackInvalid` | `string` | Thông báo lỗi |
| `onChange` | `function` | `(event, checked) => void` |
| `direction` | `"row" \| "column"` | Hướng layout |
| `labelPosition` | `"left" \| "right" \| "top" \| "bottom"` | Vị trí label |
| `size` | `"sm" \| "md" \| "lg"` | Kích thước |
| `color` | `"primary" \| "success" \| "warning" \| "danger" \| "info"` | Màu sắc |

## 🎨 Layout Options

### Row Layout (Default)
```jsx
// Label bên phải (default)
<Switch label="Feature" direction="row" labelPosition="right" />

// Label bên trái
<Switch label="Feature" direction="row" labelPosition="left" />
```

### Column Layout
```jsx
// Label ở trên
<Switch label="Feature" direction="column" labelPosition="top" />

// Label ở dưới
<Switch label="Feature" direction="column" labelPosition="bottom" />
```

## ⚠️ Error States

```jsx
<Switch
    label="Required Field"
    required={true}
    invalid={true}
    feedbackInvalid="This field is required"
    color="danger"
/>
```

## 🔧 Form Integration

```jsx
import { createForm } from "@felte/solid";

const { form } = createForm({
    initialValues: {
        notifications: false
    },
    onSubmit: (values) => {
        console.log(values);
    }
});

<form use:form>
    <Switch
        name="notifications"
        label="Email Notifications"
        required={true}
    />
</form>
```

## 🎯 Controlled vs Uncontrolled

```jsx
// Controlled
const [isEnabled, setIsEnabled] = createSignal(false);
<Switch
    checked={isEnabled()}
    onChange={(e, value) => setIsEnabled(value)}
/>

// Uncontrolled
<Switch defaultChecked={true} />
```

## 📱 Responsive

Component tự động responsive - chuyển từ row sang column layout trên mobile.

## 🎨 Customization

```jsx
// Custom styling
<Switch
    class="my-custom-switch"
    color="primary"
    size="lg"
/>
```

## 🧪 Testing

Xem file `SwitchDemo.jsx` để test các tính năng.
