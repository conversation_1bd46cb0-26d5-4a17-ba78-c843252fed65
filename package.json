{"name": "base-fe", "version": "1.0.6", "description": "Buymed Common CSS & JS for Buymed Component", "sass": "scss/boostrap.scss", "style": "dist/css/boostrap.css", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "css": "npm-run-all css-compile css-prefix css-minify", "css-compile": "sass --style expanded --source-map --embed-sources --no-error-css scss/:dist/css/", "css-minify": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*.css\" \"!dist/css/*.min.css\"", "css-prefix": "postcss --config build/postcss.config.js --replace \"dist/css/*.css\" \"!dist/css/*.min.css\""}, "devDependencies": {"autoprefixer": "^10.4.13", "clean-css-cli": "^5.6.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.33", "postcss-cli": "^10.1.0", "sass": "^1.69.7"}, "keywords": ["scss", "css", "buymed-css"], "author": "buymed", "license": "ISC", "dependencies": {}}