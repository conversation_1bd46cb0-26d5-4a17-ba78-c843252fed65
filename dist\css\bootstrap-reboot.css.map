{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_root.scss", "../../scss/vendor/_rfs.scss", "bootstrap-reboot.css", "../../scss/mixins/_color-mode.scss", "../../scss/_reboot.scss", "../../scss/_variables.scss", "../../scss/mixins/_border-radius.scss"], "names": [], "mappings": "AACE;;;;EAAA;ACDF;;EASI,kBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,iBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,mBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,gBAAA;EAAA,kBAAA;EAAA,uBAAA;EAIA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAIA,qBAAA;EAAA,uBAAA;EAAA,qBAAA;EAAA,kBAAA;EAAA,qBAAA;EAAA,oBAAA;EAAA,mBAAA;EAAA,kBAAA;EAIA,2BAAA;EAAA,iCAAA;EAAA,2BAAA;EAAA,0BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,yBAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAIA,+BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,+BAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAGF,6BAAA;EACA,uBAAA;EAMA,qNAAA;EACA,yGAAA;EACA,yFAAA;EAOA,gDAAA;ECiPI,yBALI;ED1OR,0BAAA;EACA,0BAAA;EAKA,wBAAA;EACA,+BAAA;EACA,kBAAA;EACA,+BAAA;EAEA,yBAAA;EACA,gCAAA;EAEA,4CAAA;EACA,oCAAA;EACA,0BAAA;EACA,oCAAA;EAEA,0CAAA;EACA,mCAAA;EACA,yBAAA;EACA,mCAAA;EAOA,wBAAA;EACA,8BAAA;EACA,+BAAA;EAEA,8BAAA;EACA,qCAAA;EAMA,wBAAA;EACA,0BAAA;EAGA,sBAAA;EACA,wBAAA;EACA,0BAAA;EACA,mDAAA;EAEA,4BAAA;EACA,8BAAA;EACA,6BAAA;EACA,2BAAA;EACA,4BAAA;EACA,mDAAA;EACA,8BAAA;EAGA,kDAAA;EACA,2DAAA;EACA,oDAAA;EACA,2DAAA;EAIA,8BAAA;EACA,6BAAA;EACA,4CAAA;EAIA,8BAAA;EACA,qCAAA;EACA,gCAAA;EACA,uCAAA;AENF;;AC3GI;EHuHA,kBAAA;EAGA,wBAAA;EACA,kCAAA;EACA,qBAAA;EACA,4BAAA;EAEA,yBAAA;EACA,sCAAA;EAEA,+CAAA;EACA,uCAAA;EACA,0BAAA;EACA,iCAAA;EAEA,6CAAA;EACA,sCAAA;EACA,yBAAA;EACA,gCAAA;EAGE,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAIA,+BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,+BAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAOF,wBAAA;EACA,8BAAA;EACA,kCAAA;EACA,wCAAA;EAEA,wBAAA;EAEA,0BAAA;EACA,wDAAA;EAEA,8BAAA;EACA,qCAAA;EACA,gCAAA;EACA,uCAAA;AETJ;;AEhKA;;;EAGE,sBAAA;AFmKF;;AEpJI;EANJ;IAOM,uBAAA;EFwJJ;AACF;;AE3IA;EACE,SAAA;EACA,uCAAA;EHmPI,mCALI;EG5OR,uCAAA;EACA,uCAAA;EACA,2BAAA;EACA,qCAAA;EACA,mCAAA;EACA,8BAAA;EACA,6CAAA;AF8IF;;AErIA;EACE,cAAA;EACA,cCyoB4B;EDxoB5B,SAAA;EACA,wCAAA;EACA,aC+oB4B;AHvgB9B;;AE9HA;EACE,aAAA;EACA,qBCskB4B;EDnkB5B,gBCskB4B;EDrkB5B,gBCskB4B;EDrkB5B,uCAAA;AF+HF;;AE5HA;EH6MQ,iCAAA;AC7ER;ADrFI;EG3CJ;IHoNQ,iBAAA;EChFN;AACF;;AEhIA;EHwMQ,iCAAA;ACpER;AD9FI;EGtCJ;IH+MQ,eAAA;ECvEN;AACF;;AEpIA;EHmMQ,+BAAA;AC3DR;ADvGI;EGjCJ;IH0MQ,kBAAA;EC9DN;AACF;;AExIA;EH8LQ,iCAAA;AClDR;ADhHI;EG5BJ;IHqMQ,iBAAA;ECrDN;AACF;;AE5IA;EHqLM,kBALI;AChCV;;AE3IA;EHgLM,eALI;AC5BV;;AEpIA;EACE,aAAA;EACA,mBCsW0B;AH/N5B;;AE7HA;EACE,yCAAA;EAAA,iCAAA;EACA,YAAA;EACA,sCAAA;EAAA,8BAAA;AFgIF;;AE1HA;EACE,mBAAA;EACA,kBAAA;EACA,oBAAA;AF6HF;;AEvHA;;EAEE,kBAAA;AF0HF;;AEvHA;;;EAGE,aAAA;EACA,mBAAA;AF0HF;;AEvHA;;;;EAIE,gBAAA;AF0HF;;AEvHA;EACE,gBC2c4B;AHjV9B;;AErHA;EACE,qBAAA;EACA,cAAA;AFwHF;;AElHA;EACE,gBAAA;AFqHF;;AE7GA;;EAEE,mBCob4B;AHpU9B;;AExGA;EHmFM,kBALI;AC8BV;;AErGA;EACE,iBCugB4B;EDtgB5B,wCAAA;AFwGF;;AE/FA;;EAEE,kBAAA;EH+DI,iBALI;EGxDR,cAAA;EACA,wBAAA;AFkGF;;AE/FA;EAAM,eAAA;AFmGN;;AElGA;EAAM,WAAA;AFsGN;;AEjGA;EACE,gEAAA;EACA,0BCwOwC;AHpI1C;AElGE;EACE,mDAAA;AFoGJ;;AEzFE;EAEE,cAAA;EACA,qBAAA;AF2FJ;;AEpFA;;;;EAIE,qCC+V4B;EJ1UxB,cALI;ACwEV;;AEhFA;EACE,cAAA;EACA,aAAA;EACA,mBAAA;EACA,cAAA;EHSI,kBALI;ACgFV;AE/EE;EHII,kBALI;EGGN,cAAA;EACA,kBAAA;AFiFJ;;AE7EA;EHHM,kBALI;EGUR,2BAAA;EACA,qBAAA;AFgFF;AE7EE;EACE,cAAA;AF+EJ;;AE3EA;EACE,2BAAA;EHfI,kBALI;EGsBR,wBCy6CkC;EDx6ClC,sCCy6CkC;EC7sDhC,sBAAA;AJmXJ;AE5EE;EACE,UAAA;EHtBE,cALI;AC0GV;;AEpEA;EACE,gBAAA;AFuEF;;AEjEA;;EAEE,sBAAA;AFoEF;;AE5DA;EACE,oBAAA;EACA,yBAAA;AF+DF;;AE5DA;EACE,mBC8Y4B;ED7Y5B,sBC6Y4B;ED5Y5B,gCC8a4B;ED7a5B,gBAAA;AF+DF;;AExDA;EAEE,mBAAA;EACA,gCAAA;AF0DF;;AEvDA;;;;;;EAME,qBAAA;EACA,mBAAA;EACA,eAAA;AF0DF;;AElDA;EACE,qBAAA;AFqDF;;AE/CA;EAEE,gBAAA;AFiDF;;AEzCA;EACE,UAAA;AF4CF;;AEvCA;;;;;EAKE,SAAA;EACA,oBAAA;EHrHI,kBALI;EG4HR,oBAAA;AF0CF;;AEtCA;;EAEE,oBAAA;AFyCF;;AEpCA;EACE,eAAA;AFuCF;;AEpCA;EAGE,iBAAA;AFqCF;AElCE;EACE,UAAA;AFoCJ;;AE7BA;EACE,wBAAA;AFgCF;;AExBA;;;;EAIE,0BAAA;AF2BF;AExBI;;;;EACE,eAAA;AF6BN;;AEtBA;EACE,UAAA;EACA,kBAAA;AFyBF;;AEpBA;EACE,gBAAA;AFuBF;;AEbA;EACE,YAAA;EACA,UAAA;EACA,SAAA;EACA,SAAA;AFgBF;;AERA;EACE,WAAA;EACA,WAAA;EACA,UAAA;EACA,qBCsO4B;EJhbtB,iCAAA;EG6MN,oBAAA;AFUF;ADzXI;EGwWJ;IH/LQ,iBAAA;ECoNN;AACF;AEbE;EACE,WAAA;AFeJ;;AERA;;;;;;;EAOE,UAAA;AFWF;;AERA;EACE,YAAA;AFWF;;AEFA;EACE,oBAAA;EACA,6BAAA;AFKF;;AEGA;;;;;;;CAAA;AAWA;EACE,wBAAA;AFHF;;AEQA;EACE,UAAA;AFLF;;AEYA;EACE,aAAA;EACA,0BAAA;AFTF;;AEcA;EACE,qBAAA;AFXF;;AEgBA;EACE,SAAA;AFbF;;AEoBA;EACE,kBAAA;EACA,eAAA;AFjBF;;AEyBA;EACE,wBAAA;AFtBF;;AE8BA;EACE,wBAAA;AF3BF", "file": "bootstrap-reboot.css", "sourcesContent": ["@mixin bsBanner($file) {\r\n  /*!\r\n   * Bootstrap #{$file} v5.3.0-alpha1 (https://getbootstrap.com/)\r\n   * Copyright 2011-2023 The Bootstrap Authors\r\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\r\n   */\r\n}\r\n", ":root,\r\n[data-bs-theme=\"light\"] {\r\n  // Note: Custom variable values only support SassScript inside `#{}`.\r\n\r\n  // Colors\r\n  //\r\n  // Generate palettes for full colors, grays, and theme colors.\r\n\r\n  @each $color, $value in $colors {\r\n    --#{$prefix}#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $grays {\r\n    --#{$prefix}gray-#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors {\r\n    --#{$prefix}#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-rgb {\r\n    --#{$prefix}#{$color}-rgb: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-text {\r\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-bg-subtle {\r\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-border-subtle {\r\n    --#{$prefix}#{$color}-border-subtle: #{$value};\r\n  }\r\n\r\n  --#{$prefix}white-rgb: #{to-rgb($white)};\r\n  --#{$prefix}black-rgb: #{to-rgb($black)};\r\n\r\n  // Fonts\r\n\r\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\r\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\r\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\r\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\r\n  --#{$prefix}gradient: #{$gradient};\r\n\r\n  // Root and body\r\n  // scss-docs-start root-body-variables\r\n  @if $font-size-root != null {\r\n    --#{$prefix}root-font-size: #{$font-size-root};\r\n  }\r\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\r\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\r\n  --#{$prefix}body-font-weight: #{$font-weight-base};\r\n  --#{$prefix}body-line-height: #{$line-height-base};\r\n  @if $body-text-align != null {\r\n    --#{$prefix}body-text-align: #{$body-text-align};\r\n  }\r\n\r\n  --#{$prefix}body-color: #{$body-color};\r\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\r\n  --#{$prefix}body-bg: #{$body-bg};\r\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\r\n\r\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\r\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\r\n\r\n  --#{$prefix}secondary-color: #{$body-secondary-color};\r\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\r\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\r\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\r\n\r\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\r\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\r\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\r\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\r\n  // scss-docs-end root-body-variables\r\n\r\n  @if $headings-color != null {\r\n    --#{$prefix}heading-color: #{$headings-color};\r\n  }\r\n\r\n  --#{$prefix}link-color: #{$link-color};\r\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\r\n  --#{$prefix}link-decoration: #{$link-decoration};\r\n\r\n  --#{$prefix}link-hover-color: #{$link-hover-color};\r\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\r\n\r\n  @if $link-hover-decoration != null {\r\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\r\n  }\r\n\r\n  --#{$prefix}code-color: #{$code-color};\r\n  --#{$prefix}highlight-bg: #{$mark-bg};\r\n\r\n  // scss-docs-start root-border-var\r\n  --#{$prefix}border-width: #{$border-width};\r\n  --#{$prefix}border-style: #{$border-style};\r\n  --#{$prefix}border-color: #{$border-color};\r\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\r\n\r\n  --#{$prefix}border-radius: #{$border-radius};\r\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\r\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\r\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\r\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\r\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\r\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\r\n  // scss-docs-end root-border-var\r\n\r\n  --#{$prefix}box-shadow: #{$box-shadow};\r\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\r\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\r\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\r\n\r\n  // Focus styles\r\n  // scss-docs-start root-focus-variables\r\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\r\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\r\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\r\n  // scss-docs-end root-focus-variables\r\n\r\n  // scss-docs-start root-form-validation-variables\r\n  --#{$prefix}form-valid-color: #{$form-valid-color};\r\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\r\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\r\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\r\n  // scss-docs-end root-form-validation-variables\r\n}\r\n\r\n@if $enable-dark-mode {\r\n  @include color-mode(dark, true) {\r\n    color-scheme: dark;\r\n\r\n    // scss-docs-start root-dark-mode-vars\r\n    --#{$prefix}body-color: #{$body-color-dark};\r\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\r\n    --#{$prefix}body-bg: #{$body-bg-dark};\r\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\r\n\r\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\r\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\r\n\r\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\r\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\r\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\r\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\r\n\r\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\r\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\r\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\r\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\r\n\r\n    @each $color, $value in $theme-colors-text-dark {\r\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\r\n    }\r\n\r\n    @each $color, $value in $theme-colors-bg-subtle-dark {\r\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\r\n    }\r\n\r\n    @each $color, $value in $theme-colors-border-subtle-dark {\r\n      --#{$prefix}#{$color}-border-subtle: #{$value};\r\n    }\r\n\r\n    @if $headings-color-dark != null {\r\n      --#{$prefix}heading-color: #{$headings-color-dark};\r\n    }\r\n\r\n    --#{$prefix}link-color: #{$link-color-dark};\r\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\r\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\r\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\r\n\r\n    --#{$prefix}code-color: #{$code-color-dark};\r\n\r\n    --#{$prefix}border-color: #{$border-color-dark};\r\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\r\n\r\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\r\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\r\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\r\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\r\n    // scss-docs-end root-dark-mode-vars\r\n  }\r\n}\r\n", "// stylelint-disable property-disallowed-list, scss/dollar-variable-default\r\n\r\n// SCSS RFS mixin\r\n//\r\n// Automated responsive values for font sizes, paddings, margins and much more\r\n//\r\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\r\n\r\n// Configuration\r\n\r\n// Base value\r\n$rfs-base-value: 1.25rem !default;\r\n$rfs-unit: rem !default;\r\n\r\n@if $rfs-unit != rem and $rfs-unit != px {\r\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\r\n}\r\n\r\n// Breakpoint at where values start decreasing if screen width is smaller\r\n$rfs-breakpoint: 1200px !default;\r\n$rfs-breakpoint-unit: px !default;\r\n\r\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\r\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\r\n}\r\n\r\n// Resize values based on screen height and width\r\n$rfs-two-dimensional: false !default;\r\n\r\n// Factor of decrease\r\n$rfs-factor: 10 !default;\r\n\r\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\r\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\r\n}\r\n\r\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\r\n$rfs-mode: min-media-query !default;\r\n\r\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\r\n$rfs-class: false !default;\r\n\r\n// 1 rem = $rfs-rem-value px\r\n$rfs-rem-value: 16 !default;\r\n\r\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\r\n$rfs-safari-iframe-resize-bug-fix: false !default;\r\n\r\n// Disable RFS by setting $enable-rfs to false\r\n$enable-rfs: true !default;\r\n\r\n// Cache $rfs-base-value unit\r\n$rfs-base-value-unit: unit($rfs-base-value);\r\n\r\n@function divide($dividend, $divisor, $precision: 10) {\r\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\r\n  $dividend: abs($dividend);\r\n  $divisor: abs($divisor);\r\n  @if $dividend == 0 {\r\n    @return 0;\r\n  }\r\n  @if $divisor == 0 {\r\n    @error \"Cannot divide by 0\";\r\n  }\r\n  $remainder: $dividend;\r\n  $result: 0;\r\n  $factor: 10;\r\n  @while ($remainder > 0 and $precision >= 0) {\r\n    $quotient: 0;\r\n    @while ($remainder >= $divisor) {\r\n      $remainder: $remainder - $divisor;\r\n      $quotient: $quotient + 1;\r\n    }\r\n    $result: $result * 10 + $quotient;\r\n    $factor: $factor * .1;\r\n    $remainder: $remainder * 10;\r\n    $precision: $precision - 1;\r\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\r\n      $result: $result + 1;\r\n    }\r\n  }\r\n  $result: $result * $factor * $sign;\r\n  $dividend-unit: unit($dividend);\r\n  $divisor-unit: unit($divisor);\r\n  $unit-map: (\r\n    \"px\": 1px,\r\n    \"rem\": 1rem,\r\n    \"em\": 1em,\r\n    \"%\": 1%\r\n  );\r\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\r\n    $result: $result * map-get($unit-map, $dividend-unit);\r\n  }\r\n  @return $result;\r\n}\r\n\r\n// Remove px-unit from $rfs-base-value for calculations\r\n@if $rfs-base-value-unit == px {\r\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\r\n}\r\n@else if $rfs-base-value-unit == rem {\r\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\r\n}\r\n\r\n// Cache $rfs-breakpoint unit to prevent multiple calls\r\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\r\n\r\n// Remove unit from $rfs-breakpoint for calculations\r\n@if $rfs-breakpoint-unit-cache == px {\r\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\r\n}\r\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\r\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\r\n}\r\n\r\n// Calculate the media query value\r\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\r\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\r\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\r\n\r\n// Internal mixin used to determine which media query needs to be used\r\n@mixin _rfs-media-query {\r\n  @if $rfs-two-dimensional {\r\n    @if $rfs-mode == max-media-query {\r\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\r\n        @content;\r\n      }\r\n    }\r\n    @else {\r\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\r\n        @content;\r\n      }\r\n    }\r\n  }\r\n  @else {\r\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Internal mixin that adds disable classes to the selector if needed.\r\n@mixin _rfs-rule {\r\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\r\n    // Adding an extra class increases specificity, which prevents the media query to override the property\r\n    &,\r\n    .disable-rfs &,\r\n    &.disable-rfs {\r\n      @content;\r\n    }\r\n  }\r\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\r\n    .enable-rfs &,\r\n    &.enable-rfs {\r\n      @content;\r\n    }\r\n  }\r\n  @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Internal mixin that adds enable classes to the selector if needed.\r\n@mixin _rfs-media-query-rule {\r\n\r\n  @if $rfs-class == enable {\r\n    @if $rfs-mode == min-media-query {\r\n      @content;\r\n    }\r\n\r\n    @include _rfs-media-query {\r\n      .enable-rfs &,\r\n      &.enable-rfs {\r\n        @content;\r\n      }\r\n    }\r\n  }\r\n  @else {\r\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\r\n      .disable-rfs &,\r\n      &.disable-rfs {\r\n        @content;\r\n      }\r\n    }\r\n    @include _rfs-media-query {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Helper function to get the formatted non-responsive value\r\n@function rfs-value($values) {\r\n  // Convert to list\r\n  $values: if(type-of($values) != list, ($values,), $values);\r\n\r\n  $val: '';\r\n\r\n  // Loop over each value and calculate value\r\n  @each $value in $values {\r\n    @if $value == 0 {\r\n      $val: $val + ' 0';\r\n    }\r\n    @else {\r\n      // Cache $value unit\r\n      $unit: if(type-of($value) == \"number\", unit($value), false);\r\n\r\n      @if $unit == px {\r\n        // Convert to rem if needed\r\n        $val: $val + ' ' + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\r\n      }\r\n      @else if $unit == rem {\r\n        // Convert to px if needed\r\n        $val: $val + ' ' + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\r\n      }\r\n      @else {\r\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\r\n        $val: $val + ' ' + $value;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove first space\r\n  @return unquote(str-slice($val, 2));\r\n}\r\n\r\n// Helper function to get the responsive value calculated by RFS\r\n@function rfs-fluid-value($values) {\r\n  // Convert to list\r\n  $values: if(type-of($values) != list, ($values,), $values);\r\n\r\n  $val: '';\r\n\r\n  // Loop over each value and calculate value\r\n  @each $value in $values {\r\n    @if $value == 0 {\r\n      $val: $val + ' 0';\r\n    }\r\n\r\n    @else {\r\n      // Cache $value unit\r\n      $unit: if(type-of($value) == \"number\", unit($value), false);\r\n\r\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\r\n      @if not $unit or $unit != px and $unit != rem {\r\n        $val: $val + ' ' + $value;\r\n      }\r\n\r\n      @else {\r\n        // Remove unit from $value for calculations\r\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\r\n\r\n        // Only add the media query if the value is greater than the minimum value\r\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\r\n          $val: $val + ' ' +  if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\r\n        }\r\n        @else {\r\n          // Calculate the minimum value\r\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\r\n\r\n          // Calculate difference between $value and the minimum value\r\n          $value-diff: abs($value) - $value-min;\r\n\r\n          // Base value formatting\r\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\r\n\r\n          // Use negative value if needed\r\n          $min-width: if($value < 0, -$min-width, $min-width);\r\n\r\n          // Use `vmin` if two-dimensional is enabled\r\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\r\n\r\n          // Calculate the variable width between 0 and $rfs-breakpoint\r\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\r\n\r\n          // Return the calculated value\r\n          $val: $val + ' calc(' + $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove first space\r\n  @return unquote(str-slice($val, 2));\r\n}\r\n\r\n// RFS mixin\r\n@mixin rfs($values, $property: font-size) {\r\n  @if $values != null {\r\n    $val: rfs-value($values);\r\n    $fluidVal: rfs-fluid-value($values);\r\n\r\n    // Do not print the media query if responsive & non-responsive values are the same\r\n    @if $val == $fluidVal {\r\n      #{$property}: $val;\r\n    }\r\n    @else {\r\n      @include _rfs-rule {\r\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluidVal);\r\n\r\n        // Include safari iframe resize fix if needed\r\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\r\n      }\r\n\r\n      @include _rfs-media-query-rule {\r\n        #{$property}: if($rfs-mode == max-media-query, $fluidVal, $val);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Shorthand helper mixins\r\n@mixin font-size($value) {\r\n  @include rfs($value);\r\n}\r\n\r\n@mixin padding($value) {\r\n  @include rfs($value, padding);\r\n}\r\n\r\n@mixin padding-top($value) {\r\n  @include rfs($value, padding-top);\r\n}\r\n\r\n@mixin padding-right($value) {\r\n  @include rfs($value, padding-right);\r\n}\r\n\r\n@mixin padding-bottom($value) {\r\n  @include rfs($value, padding-bottom);\r\n}\r\n\r\n@mixin padding-left($value) {\r\n  @include rfs($value, padding-left);\r\n}\r\n\r\n@mixin margin($value) {\r\n  @include rfs($value, margin);\r\n}\r\n\r\n@mixin margin-top($value) {\r\n  @include rfs($value, margin-top);\r\n}\r\n\r\n@mixin margin-right($value) {\r\n  @include rfs($value, margin-right);\r\n}\r\n\r\n@mixin margin-bottom($value) {\r\n  @include rfs($value, margin-bottom);\r\n}\r\n\r\n@mixin margin-left($value) {\r\n  @include rfs($value, margin-left);\r\n}\r\n", "/*!\n * Bootstrap Reboot v5.3.0-alpha1 (https://getbootstrap.com/)\n * Copyright 2011-2023 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n:root,\n[data-bs-theme=light] {\n  --bs-blue: #1a73b8;\n  --bs-indigo: #6610f2;\n  --bs-purple: #6f42c1;\n  --bs-pink: #d63384;\n  --bs-red: #ea0b16;\n  --bs-orange: #fd7e14;\n  --bs-yellow: #f0a205;\n  --bs-green: #005c29;\n  --bs-teal: #20c997;\n  --bs-cyan: #175cd3;\n  --bs-black: #000;\n  --bs-white: #fff;\n  --bs-gray: #6c757d;\n  --bs-gray-dark: #343a40;\n  --bs-gray-100: #ececec;\n  --bs-gray-200: #e9ecef;\n  --bs-gray-300: #dee2e6;\n  --bs-gray-400: #ced4da;\n  --bs-gray-500: #adb5bd;\n  --bs-gray-600: #6c757d;\n  --bs-gray-700: #495057;\n  --bs-gray-800: #343a40;\n  --bs-gray-900: #393939;\n  --bs-primary: #005c29;\n  --bs-secondary: #d1e7bf;\n  --bs-success: #005c29;\n  --bs-info: #175cd3;\n  --bs-warning: #f0a205;\n  --bs-danger: #ea0b16;\n  --bs-light: #ececec;\n  --bs-dark: #393939;\n  --bs-primary-rgb: 0, 92, 41;\n  --bs-secondary-rgb: 209, 231, 191;\n  --bs-success-rgb: 0, 92, 41;\n  --bs-info-rgb: 23, 92, 211;\n  --bs-warning-rgb: 240, 162, 5;\n  --bs-danger-rgb: 234, 11, 22;\n  --bs-light-rgb: 236, 236, 236;\n  --bs-dark-rgb: 57, 57, 57;\n  --bs-primary-text-emphasis: #002510;\n  --bs-secondary-text-emphasis: #545c4c;\n  --bs-success-text-emphasis: #002510;\n  --bs-info-text-emphasis: #092554;\n  --bs-warning-text-emphasis: #604102;\n  --bs-danger-text-emphasis: #5e0409;\n  --bs-light-text-emphasis: #495057;\n  --bs-dark-text-emphasis: #495057;\n  --bs-primary-bg-subtle: #ccded4;\n  --bs-secondary-bg-subtle: #f6faf2;\n  --bs-success-bg-subtle: #ccded4;\n  --bs-info-bg-subtle: #d1def6;\n  --bs-warning-bg-subtle: #fceccd;\n  --bs-danger-bg-subtle: #fbced0;\n  --bs-light-bg-subtle: #f6f6f6;\n  --bs-dark-bg-subtle: #ced4da;\n  --bs-primary-border-subtle: #99bea9;\n  --bs-secondary-border-subtle: #edf5e5;\n  --bs-success-border-subtle: #99bea9;\n  --bs-info-border-subtle: #a2beed;\n  --bs-warning-border-subtle: #f9da9b;\n  --bs-danger-border-subtle: #f79da2;\n  --bs-light-border-subtle: #e9ecef;\n  --bs-dark-border-subtle: #adb5bd;\n  --bs-white-rgb: 255, 255, 255;\n  --bs-black-rgb: 0, 0, 0;\n  --bs-font-sans-serif: system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));\n  --bs-body-font-family: var(--bs-font-sans-serif);\n  --bs-body-font-size: 1rem;\n  --bs-body-font-weight: 400;\n  --bs-body-line-height: 1.5;\n  --bs-body-color: #393939;\n  --bs-body-color-rgb: 57, 57, 57;\n  --bs-body-bg: #fff;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-emphasis-color: #000;\n  --bs-emphasis-color-rgb: 0, 0, 0;\n  --bs-secondary-color: rgba(57, 57, 57, 0.75);\n  --bs-secondary-color-rgb: 57, 57, 57;\n  --bs-secondary-bg: #e9ecef;\n  --bs-secondary-bg-rgb: 233, 236, 239;\n  --bs-tertiary-color: rgba(57, 57, 57, 0.5);\n  --bs-tertiary-color-rgb: 57, 57, 57;\n  --bs-tertiary-bg: #ececec;\n  --bs-tertiary-bg-rgb: 236, 236, 236;\n  --bs-link-color: #005c29;\n  --bs-link-color-rgb: 0, 92, 41;\n  --bs-link-decoration: underline;\n  --bs-link-hover-color: #0f280b;\n  --bs-link-hover-color-rgb: 15, 40, 11;\n  --bs-code-color: #d63384;\n  --bs-highlight-bg: #fceccd;\n  --bs-border-width: 1px;\n  --bs-border-style: solid;\n  --bs-border-color: #dee2e6;\n  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);\n  --bs-border-radius: 0.375rem;\n  --bs-border-radius-sm: 0.25rem;\n  --bs-border-radius-lg: 0.5rem;\n  --bs-border-radius-xl: 1rem;\n  --bs-border-radius-xxl: 2rem;\n  --bs-border-radius-2xl: var(--bs-border-radius-xxl);\n  --bs-border-radius-pill: 50rem;\n  --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n  --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n  --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);\n  --bs-focus-ring-width: 0.25rem;\n  --bs-focus-ring-opacity: 0.25;\n  --bs-focus-ring-color: rgba(0, 92, 41, 0.25);\n  --bs-form-valid-color: #005c29;\n  --bs-form-valid-border-color: #005c29;\n  --bs-form-invalid-color: #ea0b16;\n  --bs-form-invalid-border-color: #ea0b16;\n}\n\n[data-bs-theme=dark] {\n  color-scheme: dark;\n  --bs-body-color: #adb5bd;\n  --bs-body-color-rgb: 173, 181, 189;\n  --bs-body-bg: #393939;\n  --bs-body-bg-rgb: 57, 57, 57;\n  --bs-emphasis-color: #fff;\n  --bs-emphasis-color-rgb: 255, 255, 255;\n  --bs-secondary-color: rgba(173, 181, 189, 0.75);\n  --bs-secondary-color-rgb: 173, 181, 189;\n  --bs-secondary-bg: #343a40;\n  --bs-secondary-bg-rgb: 52, 58, 64;\n  --bs-tertiary-color: rgba(173, 181, 189, 0.5);\n  --bs-tertiary-color-rgb: 173, 181, 189;\n  --bs-tertiary-bg: #373a3d;\n  --bs-tertiary-bg-rgb: 55, 58, 61;\n  --bs-primary-text-emphasis: #669d7f;\n  --bs-secondary-text-emphasis: #e3f1d9;\n  --bs-success-text-emphasis: #669d7f;\n  --bs-info-text-emphasis: #749de5;\n  --bs-warning-text-emphasis: #f6c769;\n  --bs-danger-text-emphasis: #f26d73;\n  --bs-light-text-emphasis: #ececec;\n  --bs-dark-text-emphasis: #dee2e6;\n  --bs-primary-bg-subtle: #001208;\n  --bs-secondary-bg-subtle: #2a2e26;\n  --bs-success-bg-subtle: #001208;\n  --bs-info-bg-subtle: #05122a;\n  --bs-warning-bg-subtle: #302001;\n  --bs-danger-bg-subtle: #2f0204;\n  --bs-light-bg-subtle: #343a40;\n  --bs-dark-bg-subtle: #1a1d20;\n  --bs-primary-border-subtle: #003719;\n  --bs-secondary-border-subtle: #7d8b73;\n  --bs-success-border-subtle: #003719;\n  --bs-info-border-subtle: #0e377f;\n  --bs-warning-border-subtle: #906103;\n  --bs-danger-border-subtle: #8c070d;\n  --bs-light-border-subtle: #495057;\n  --bs-dark-border-subtle: #343a40;\n  --bs-link-color: #669d7f;\n  --bs-link-hover-color: #85b199;\n  --bs-link-color-rgb: 102, 157, 127;\n  --bs-link-hover-color-rgb: 133, 177, 153;\n  --bs-code-color: #e685b5;\n  --bs-border-color: #495057;\n  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);\n  --bs-form-valid-color: #669d7f;\n  --bs-form-valid-border-color: #669d7f;\n  --bs-form-invalid-color: #f26d73;\n  --bs-form-invalid-border-color: #f26d73;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  :root {\n    scroll-behavior: smooth;\n  }\n}\n\nbody {\n  margin: 0;\n  font-family: var(--bs-body-font-family);\n  font-size: var(--bs-body-font-size);\n  font-weight: var(--bs-body-font-weight);\n  line-height: var(--bs-body-line-height);\n  color: var(--bs-body-color);\n  text-align: var(--bs-body-text-align);\n  background-color: var(--bs-body-bg);\n  -webkit-text-size-adjust: 100%;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n\nhr {\n  margin: 1rem 0;\n  color: inherit;\n  border: 0;\n  border-top: var(--bs-border-width) solid;\n  opacity: 0.25;\n}\n\nh6, h5, h4, h3, h2, h1 {\n  margin-top: 0;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  line-height: 1.2;\n  color: var(--bs-heading-color, inherit);\n}\n\nh1 {\n  font-size: calc(1.375rem + 1.5vw);\n}\n@media (min-width: 1200px) {\n  h1 {\n    font-size: 2.5rem;\n  }\n}\n\nh2 {\n  font-size: calc(1.325rem + 0.9vw);\n}\n@media (min-width: 1200px) {\n  h2 {\n    font-size: 2rem;\n  }\n}\n\nh3 {\n  font-size: calc(1.3rem + 0.6vw);\n}\n@media (min-width: 1200px) {\n  h3 {\n    font-size: 1.75rem;\n  }\n}\n\nh4 {\n  font-size: calc(1.275rem + 0.3vw);\n}\n@media (min-width: 1200px) {\n  h4 {\n    font-size: 1.5rem;\n  }\n}\n\nh5 {\n  font-size: 1.25rem;\n}\n\nh6 {\n  font-size: 1rem;\n}\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nabbr[title] {\n  text-decoration: underline dotted;\n  cursor: help;\n  text-decoration-skip-ink: none;\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: 700;\n}\n\ndd {\n  margin-bottom: 0.5rem;\n  margin-left: 0;\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\nsmall {\n  font-size: 0.875em;\n}\n\nmark {\n  padding: 0.1875em;\n  background-color: var(--bs-highlight-bg);\n}\n\nsub,\nsup {\n  position: relative;\n  font-size: 0.75em;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\na {\n  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));\n  text-decoration: underline;\n}\na:hover {\n  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);\n}\n\na:not([href]):not([class]), a:not([href]):not([class]):hover {\n  color: inherit;\n  text-decoration: none;\n}\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: var(--bs-font-monospace);\n  font-size: 1em;\n}\n\npre {\n  display: block;\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto;\n  font-size: 0.875em;\n}\npre code {\n  font-size: inherit;\n  color: inherit;\n  word-break: normal;\n}\n\ncode {\n  font-size: 0.875em;\n  color: var(--bs-code-color);\n  word-wrap: break-word;\n}\na > code {\n  color: inherit;\n}\n\nkbd {\n  padding: 0.1875rem 0.375rem;\n  font-size: 0.875em;\n  color: var(--bs-body-bg);\n  background-color: var(--bs-body-color);\n  border-radius: 0.25rem;\n}\nkbd kbd {\n  padding: 0;\n  font-size: 1em;\n}\n\nfigure {\n  margin: 0 0 1rem;\n}\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\ntable {\n  caption-side: bottom;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  color: var(--bs-secondary-color);\n  text-align: left;\n}\n\nth {\n  text-align: inherit;\n  text-align: -webkit-match-parent;\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\nlabel {\n  display: inline-block;\n}\n\nbutton {\n  border-radius: 0;\n}\n\nbutton:focus:not(:focus-visible) {\n  outline: 0;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n[role=button] {\n  cursor: pointer;\n}\n\nselect {\n  word-wrap: normal;\n}\nselect:disabled {\n  opacity: 1;\n}\n\n[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  -webkit-appearance: button;\n}\nbutton:not(:disabled),\n[type=button]:not(:disabled),\n[type=reset]:not(:disabled),\n[type=submit]:not(:disabled) {\n  cursor: pointer;\n}\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ntextarea {\n  resize: vertical;\n}\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nlegend {\n  float: left;\n  width: 100%;\n  padding: 0;\n  margin-bottom: 0.5rem;\n  font-size: calc(1.275rem + 0.3vw);\n  line-height: inherit;\n}\n@media (min-width: 1200px) {\n  legend {\n    font-size: 1.5rem;\n  }\n}\nlegend + * {\n  clear: left;\n}\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n[type=search] {\n  outline-offset: -2px;\n  -webkit-appearance: textfield;\n}\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n::file-selector-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\noutput {\n  display: inline-block;\n}\n\niframe {\n  border: 0;\n}\n\nsummary {\n  display: list-item;\n  cursor: pointer;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\n[hidden] {\n  display: none !important;\n}\n\n/*# sourceMappingURL=bootstrap-reboot.css.map */\n", "// scss-docs-start color-mode-mixin\r\n@mixin color-mode($mode: light, $root: false) {\r\n  @if $color-mode-type == \"media-query\" {\r\n    @if $root == true {\r\n      @media (prefers-color-scheme: $mode) {\r\n        :root {\r\n          @content;\r\n        }\r\n      }\r\n    } @else {\r\n      @media (prefers-color-scheme: $mode) {\r\n        @content;\r\n      }\r\n    }\r\n  } @else {\r\n    [data-bs-theme=\"#{$mode}\"] {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n// scss-docs-end color-mode-mixin\r\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\r\n\r\n\r\n// Reboot\r\n//\r\n// Normalization of HTML elements, manually forked from Normalize.css to remove\r\n// styles targeting irrelevant browsers while applying new styles.\r\n//\r\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\r\n\r\n\r\n// Document\r\n//\r\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\r\n\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box;\r\n}\r\n\r\n\r\n// Root\r\n//\r\n// Ability to the value of the root font sizes, affecting the value of `rem`.\r\n// null by default, thus nothing is generated.\r\n\r\n:root {\r\n  @if $font-size-root != null {\r\n    @include font-size(var(--#{$prefix}root-font-size));\r\n  }\r\n\r\n  @if $enable-smooth-scroll {\r\n    @media (prefers-reduced-motion: no-preference) {\r\n      scroll-behavior: smooth;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Body\r\n//\r\n// 1. Remove the margin in all browsers.\r\n// 2. As a best practice, apply a default `background-color`.\r\n// 3. Prevent adjustments of font size after orientation changes in iOS.\r\n// 4. Change the default tap highlight to be completely transparent in iOS.\r\n\r\n// scss-docs-start reboot-body-rules\r\nbody {\r\n  margin: 0; // 1\r\n  font-family: var(--#{$prefix}body-font-family);\r\n  @include font-size(var(--#{$prefix}body-font-size));\r\n  font-weight: var(--#{$prefix}body-font-weight);\r\n  line-height: var(--#{$prefix}body-line-height);\r\n  color: var(--#{$prefix}body-color);\r\n  text-align: var(--#{$prefix}body-text-align);\r\n  background-color: var(--#{$prefix}body-bg); // 2\r\n  -webkit-text-size-adjust: 100%; // 3\r\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\r\n}\r\n// scss-docs-end reboot-body-rules\r\n\r\n\r\n// Content grouping\r\n//\r\n// 1. Reset Firefox's gray color\r\n\r\nhr {\r\n  margin: $hr-margin-y 0;\r\n  color: $hr-color; // 1\r\n  border: 0;\r\n  border-top: $hr-border-width solid $hr-border-color;\r\n  opacity: $hr-opacity;\r\n}\r\n\r\n\r\n// Typography\r\n//\r\n// 1. Remove top margins from headings\r\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\r\n//    margin for easier control within type scales as it avoids margin collapsing.\r\n\r\n%heading {\r\n  margin-top: 0; // 1\r\n  margin-bottom: $headings-margin-bottom;\r\n  font-family: $headings-font-family;\r\n  font-style: $headings-font-style;\r\n  font-weight: $headings-font-weight;\r\n  line-height: $headings-line-height;\r\n  color: var(--#{$prefix}heading-color, inherit);\r\n}\r\n\r\nh1 {\r\n  @extend %heading;\r\n  @include font-size($h1-font-size);\r\n}\r\n\r\nh2 {\r\n  @extend %heading;\r\n  @include font-size($h2-font-size);\r\n}\r\n\r\nh3 {\r\n  @extend %heading;\r\n  @include font-size($h3-font-size);\r\n}\r\n\r\nh4 {\r\n  @extend %heading;\r\n  @include font-size($h4-font-size);\r\n}\r\n\r\nh5 {\r\n  @extend %heading;\r\n  @include font-size($h5-font-size);\r\n}\r\n\r\nh6 {\r\n  @extend %heading;\r\n  @include font-size($h6-font-size);\r\n}\r\n\r\n\r\n// Reset margins on paragraphs\r\n//\r\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\r\n// bottom margin to use `rem` units instead of `em`.\r\n\r\np {\r\n  margin-top: 0;\r\n  margin-bottom: $paragraph-margin-bottom;\r\n}\r\n\r\n\r\n// Abbreviations\r\n//\r\n// 1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\r\n// 2. Add explicit cursor to indicate changed behavior.\r\n// 3. Prevent the text-decoration to be skipped.\r\n\r\nabbr[title] {\r\n  text-decoration: underline dotted; // 1\r\n  cursor: help; // 2\r\n  text-decoration-skip-ink: none; // 3\r\n}\r\n\r\n\r\n// Address\r\n\r\naddress {\r\n  margin-bottom: 1rem;\r\n  font-style: normal;\r\n  line-height: inherit;\r\n}\r\n\r\n\r\n// Lists\r\n\r\nol,\r\nul {\r\n  padding-left: 2rem;\r\n}\r\n\r\nol,\r\nul,\r\ndl {\r\n  margin-top: 0;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\nol ol,\r\nul ul,\r\nol ul,\r\nul ol {\r\n  margin-bottom: 0;\r\n}\r\n\r\ndt {\r\n  font-weight: $dt-font-weight;\r\n}\r\n\r\n// 1. Undo browser default\r\n\r\ndd {\r\n  margin-bottom: .5rem;\r\n  margin-left: 0; // 1\r\n}\r\n\r\n\r\n// Blockquote\r\n\r\nblockquote {\r\n  margin: 0 0 1rem;\r\n}\r\n\r\n\r\n// Strong\r\n//\r\n// Add the correct font weight in Chrome, Edge, and Safari\r\n\r\nb,\r\nstrong {\r\n  font-weight: $font-weight-bolder;\r\n}\r\n\r\n\r\n// Small\r\n//\r\n// Add the correct font size in all browsers\r\n\r\nsmall {\r\n  @include font-size($small-font-size);\r\n}\r\n\r\n\r\n// Mark\r\n\r\nmark {\r\n  padding: $mark-padding;\r\n  background-color: var(--#{$prefix}highlight-bg);\r\n}\r\n\r\n\r\n// Sub and Sup\r\n//\r\n// Prevent `sub` and `sup` elements from affecting the line height in\r\n// all browsers.\r\n\r\nsub,\r\nsup {\r\n  position: relative;\r\n  @include font-size($sub-sup-font-size);\r\n  line-height: 0;\r\n  vertical-align: baseline;\r\n}\r\n\r\nsub { bottom: -.25em; }\r\nsup { top: -.5em; }\r\n\r\n\r\n// Links\r\n\r\na {\r\n  color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, 1));\r\n  text-decoration: $link-decoration;\r\n\r\n  &:hover {\r\n    --#{$prefix}link-color-rgb: var(--#{$prefix}link-hover-color-rgb);\r\n    text-decoration: $link-hover-decoration;\r\n  }\r\n}\r\n\r\n// And undo these styles for placeholder links/named anchors (without href).\r\n// It would be more straightforward to just use a[href] in previous block, but that\r\n// causes specificity issues in many other styles that are too complex to fix.\r\n// See https://github.com/twbs/bootstrap/issues/19402\r\n\r\na:not([href]):not([class]) {\r\n  &,\r\n  &:hover {\r\n    color: inherit;\r\n    text-decoration: none;\r\n  }\r\n}\r\n\r\n\r\n// Code\r\n\r\npre,\r\ncode,\r\nkbd,\r\nsamp {\r\n  font-family: $font-family-code;\r\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\r\n}\r\n\r\n// 1. Remove browser default top margin\r\n// 2. Reset browser default of `1em` to use `rem`s\r\n// 3. Don't allow content to break outside\r\n\r\npre {\r\n  display: block;\r\n  margin-top: 0; // 1\r\n  margin-bottom: 1rem; // 2\r\n  overflow: auto; // 3\r\n  @include font-size($code-font-size);\r\n  color: $pre-color;\r\n\r\n  // Account for some code outputs that place code tags in pre tags\r\n  code {\r\n    @include font-size(inherit);\r\n    color: inherit;\r\n    word-break: normal;\r\n  }\r\n}\r\n\r\ncode {\r\n  @include font-size($code-font-size);\r\n  color: var(--#{$prefix}code-color);\r\n  word-wrap: break-word;\r\n\r\n  // Streamline the style when inside anchors to avoid broken underline and more\r\n  a > & {\r\n    color: inherit;\r\n  }\r\n}\r\n\r\nkbd {\r\n  padding: $kbd-padding-y $kbd-padding-x;\r\n  @include font-size($kbd-font-size);\r\n  color: $kbd-color;\r\n  background-color: $kbd-bg;\r\n  @include border-radius($border-radius-sm);\r\n\r\n  kbd {\r\n    padding: 0;\r\n    @include font-size(1em);\r\n    font-weight: $nested-kbd-font-weight;\r\n  }\r\n}\r\n\r\n\r\n// Figures\r\n//\r\n// Apply a consistent margin strategy (matches our type styles).\r\n\r\nfigure {\r\n  margin: 0 0 1rem;\r\n}\r\n\r\n\r\n// Images and content\r\n\r\nimg,\r\nsvg {\r\n  vertical-align: middle;\r\n}\r\n\r\n\r\n// Tables\r\n//\r\n// Prevent double borders\r\n\r\ntable {\r\n  caption-side: bottom;\r\n  border-collapse: collapse;\r\n}\r\n\r\ncaption {\r\n  padding-top: $table-cell-padding-y;\r\n  padding-bottom: $table-cell-padding-y;\r\n  color: $table-caption-color;\r\n  text-align: left;\r\n}\r\n\r\n// 1. Removes font-weight bold by inheriting\r\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\r\n// 3. Fix alignment for Safari\r\n\r\nth {\r\n  font-weight: $table-th-font-weight; // 1\r\n  text-align: inherit; // 2\r\n  text-align: -webkit-match-parent; // 3\r\n}\r\n\r\nthead,\r\ntbody,\r\ntfoot,\r\ntr,\r\ntd,\r\nth {\r\n  border-color: inherit;\r\n  border-style: solid;\r\n  border-width: 0;\r\n}\r\n\r\n\r\n// Forms\r\n//\r\n// 1. Allow labels to use `margin` for spacing.\r\n\r\nlabel {\r\n  display: inline-block; // 1\r\n}\r\n\r\n// Remove the default `border-radius` that macOS Chrome adds.\r\n// See https://github.com/twbs/bootstrap/issues/24093\r\n\r\nbutton {\r\n  // stylelint-disable-next-line property-disallowed-list\r\n  border-radius: 0;\r\n}\r\n\r\n// Explicitly remove focus outline in Chromium when it shouldn't be\r\n// visible (e.g. as result of mouse click or touch tap). It already\r\n// should be doing this automatically, but seems to currently be\r\n// confused and applies its very visible two-tone outline anyway.\r\n\r\nbutton:focus:not(:focus-visible) {\r\n  outline: 0;\r\n}\r\n\r\n// 1. Remove the margin in Firefox and Safari\r\n\r\ninput,\r\nbutton,\r\nselect,\r\noptgroup,\r\ntextarea {\r\n  margin: 0; // 1\r\n  font-family: inherit;\r\n  @include font-size(inherit);\r\n  line-height: inherit;\r\n}\r\n\r\n// Remove the inheritance of text transform in Firefox\r\nbutton,\r\nselect {\r\n  text-transform: none;\r\n}\r\n// Set the cursor for non-`<button>` buttons\r\n//\r\n// Details at https://github.com/twbs/bootstrap/pull/30562\r\n[role=\"button\"] {\r\n  cursor: pointer;\r\n}\r\n\r\nselect {\r\n  // Remove the inheritance of word-wrap in Safari.\r\n  // See https://github.com/twbs/bootstrap/issues/24990\r\n  word-wrap: normal;\r\n\r\n  // Undo the opacity change from Chrome\r\n  &:disabled {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n// Remove the dropdown arrow only from text type inputs built with datalists in Chrome.\r\n// See https://stackoverflow.com/a/54997118\r\n\r\n[list]:not([type=\"date\"]):not([type=\"datetime-local\"]):not([type=\"month\"]):not([type=\"week\"]):not([type=\"time\"])::-webkit-calendar-picker-indicator {\r\n  display: none !important;\r\n}\r\n\r\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\r\n//    controls in Android 4.\r\n// 2. Correct the inability to style clickable types in iOS and Safari.\r\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\r\n\r\nbutton,\r\n[type=\"button\"], // 1\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n  -webkit-appearance: button; // 2\r\n\r\n  @if $enable-button-pointers {\r\n    &:not(:disabled) {\r\n      cursor: pointer; // 3\r\n    }\r\n  }\r\n}\r\n\r\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\r\n\r\n::-moz-focus-inner {\r\n  padding: 0;\r\n  border-style: none;\r\n}\r\n\r\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\r\n\r\ntextarea {\r\n  resize: vertical; // 1\r\n}\r\n\r\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\r\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\r\n//    So we reset that to ensure fieldsets behave more like a standard block element.\r\n//    See https://github.com/twbs/bootstrap/issues/12359\r\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\r\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\r\n\r\nfieldset {\r\n  min-width: 0; // 1\r\n  padding: 0; // 2\r\n  margin: 0; // 2\r\n  border: 0; // 2\r\n}\r\n\r\n// 1. By using `float: left`, the legend will behave like a block element.\r\n//    This way the border of a fieldset wraps around the legend if present.\r\n// 2. Fix wrapping bug.\r\n//    See https://github.com/twbs/bootstrap/issues/29712\r\n\r\nlegend {\r\n  float: left; // 1\r\n  width: 100%;\r\n  padding: 0;\r\n  margin-bottom: $legend-margin-bottom;\r\n  @include font-size($legend-font-size);\r\n  font-weight: $legend-font-weight;\r\n  line-height: inherit;\r\n\r\n  + * {\r\n    clear: left; // 2\r\n  }\r\n}\r\n\r\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\r\n// See https://github.com/twbs/bootstrap/issues/18842\r\n\r\n::-webkit-datetime-edit-fields-wrapper,\r\n::-webkit-datetime-edit-text,\r\n::-webkit-datetime-edit-minute,\r\n::-webkit-datetime-edit-hour-field,\r\n::-webkit-datetime-edit-day-field,\r\n::-webkit-datetime-edit-month-field,\r\n::-webkit-datetime-edit-year-field {\r\n  padding: 0;\r\n}\r\n\r\n::-webkit-inner-spin-button {\r\n  height: auto;\r\n}\r\n\r\n// 1. Correct the outline style in Safari.\r\n// 2. This overrides the extra rounded corners on search inputs in iOS so that our\r\n//    `.form-control` class can properly style them. Note that this cannot simply\r\n//    be added to `.form-control` as it's not specific enough. For details, see\r\n//    https://github.com/twbs/bootstrap/issues/11586.\r\n\r\n[type=\"search\"] {\r\n  outline-offset: -2px; // 1\r\n  -webkit-appearance: textfield; // 2\r\n}\r\n\r\n// 1. A few input types should stay LTR\r\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\r\n// 2. RTL only output\r\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\r\n\r\n/* rtl:raw:\r\n[type=\"tel\"],\r\n[type=\"url\"],\r\n[type=\"email\"],\r\n[type=\"number\"] {\r\n  direction: ltr;\r\n}\r\n*/\r\n\r\n// Remove the inner padding in Chrome and Safari on macOS.\r\n\r\n::-webkit-search-decoration {\r\n  -webkit-appearance: none;\r\n}\r\n\r\n// Remove padding around color pickers in webkit browsers\r\n\r\n::-webkit-color-swatch-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n\r\n// 1. Inherit font family and line height for file input buttons\r\n// 2. Correct the inability to style clickable types in iOS and Safari.\r\n\r\n::file-selector-button {\r\n  font: inherit; // 1\r\n  -webkit-appearance: button; // 2\r\n}\r\n\r\n// Correct element displays\r\n\r\noutput {\r\n  display: inline-block;\r\n}\r\n\r\n// Remove border from iframe\r\n\r\niframe {\r\n  border: 0;\r\n}\r\n\r\n// Summary\r\n//\r\n// 1. Add the correct display in all browsers\r\n\r\nsummary {\r\n  display: list-item; // 1\r\n  cursor: pointer;\r\n}\r\n\r\n\r\n// Progress\r\n//\r\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\r\n\r\nprogress {\r\n  vertical-align: baseline;\r\n}\r\n\r\n\r\n// Hidden attribute\r\n//\r\n// Always hide an element with the `hidden` HTML attribute.\r\n\r\n[hidden] {\r\n  display: none !important;\r\n}\r\n", "// Variables\r\n//\r\n// Variables should follow the `$component-state-property-size` formula for\r\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\r\n\r\n// Color system\r\n\r\n// scss-docs-start gray-color-variables\r\n$white:    #fff !default;\r\n$gray-base: #3c4b64 !default;\r\n$gray-100: #ececec !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #393939 !default;\r\n$black:    #000 !default;\r\n// scss-docs-end gray-color-variables\r\n\r\n// fusv-disable\r\n// scss-docs-start gray-colors-map\r\n$grays: (\r\n  \"100\": $gray-100,\r\n  \"200\": $gray-200,\r\n  \"300\": $gray-300,\r\n  \"400\": $gray-400,\r\n  \"500\": $gray-500,\r\n  \"600\": $gray-600,\r\n  \"700\": $gray-700,\r\n  \"800\": $gray-800,\r\n  \"900\": $gray-900\r\n) !default;\r\n// scss-docs-end gray-colors-map\r\n// fusv-enable\r\n\r\n$high-emphasis:            rgba(shift-color($gray-base, +26%), .95) !default;\r\n$medium-emphasis:          rgba(shift-color($gray-base, +26%), .681) !default;\r\n$disabled:                 rgba(shift-color($gray-base, +26%), .38) !default;\r\n\r\n$high-emphasis-inverse-new:    rgba($white, .87) !default;\r\n$medium-emphasis-inverse-new:  rgba($white, .6) !default;\r\n$disabled-inverse-new:         rgba($white, .38) !default;\r\n\r\n// scss-docs-start color-variables\r\n$blue:    #1a73b8 !default;\r\n$indigo:  #6610f2 !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #d63384 !default;\r\n$red:     #ea0b16 !default;\r\n$orange:  #fd7e14 !default;\r\n$yellow:  #f0a205 !default;\r\n$green:   #005c29 !default; // buymed color\r\n$green-bold:  #00B453 !default; // buymed color\r\n$green-light: #d1e7bf !default; // buymed color\r\n$green-btn-hover: #0f280b !default; // buymed color\r\n$teal:    #20c997 !default;\r\n$cyan:    #175cd3 !default;\r\n\r\n// scss-docs-end color-variables\r\n\r\n// scss-docs-start colors-map\r\n$colors: (\r\n  \"blue\":       $blue,\r\n  \"indigo\":     $indigo,\r\n  \"purple\":     $purple,\r\n  \"pink\":       $pink,\r\n  \"red\":        $red,\r\n  \"orange\":     $orange,\r\n  \"yellow\":     $yellow,\r\n  \"green\":      $green,\r\n  \"teal\":       $teal,\r\n  \"cyan\":       $cyan,\r\n  \"black\":      $black,\r\n  \"white\":      $white,\r\n  \"gray\":       $gray-600,\r\n  \"gray-dark\":  $gray-800\r\n) !default;\r\n// scss-docs-end colors-map\r\n\r\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\r\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\r\n$min-contrast-ratio:   4.5 !default;\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark:      $black !default;\r\n$color-contrast-light:     $white !default;\r\n\r\n// fusv-disable\r\n$blue-100: tint-color($blue, 80%) !default;\r\n$blue-200: tint-color($blue, 60%) !default;\r\n$blue-300: tint-color($blue, 40%) !default;\r\n$blue-400: tint-color($blue, 20%) !default;\r\n$blue-500: $blue !default;\r\n$blue-600: shade-color($blue, 20%) !default;\r\n$blue-700: shade-color($blue, 40%) !default;\r\n$blue-800: shade-color($blue, 60%) !default;\r\n$blue-900: shade-color($blue, 80%) !default;\r\n\r\n$indigo-100: tint-color($indigo, 80%) !default;\r\n$indigo-200: tint-color($indigo, 60%) !default;\r\n$indigo-300: tint-color($indigo, 40%) !default;\r\n$indigo-400: tint-color($indigo, 20%) !default;\r\n$indigo-500: $indigo !default;\r\n$indigo-600: shade-color($indigo, 20%) !default;\r\n$indigo-700: shade-color($indigo, 40%) !default;\r\n$indigo-800: shade-color($indigo, 60%) !default;\r\n$indigo-900: shade-color($indigo, 80%) !default;\r\n\r\n$purple-100: tint-color($purple, 80%) !default;\r\n$purple-200: tint-color($purple, 60%) !default;\r\n$purple-300: tint-color($purple, 40%) !default;\r\n$purple-400: tint-color($purple, 20%) !default;\r\n$purple-500: $purple !default;\r\n$purple-600: shade-color($purple, 20%) !default;\r\n$purple-700: shade-color($purple, 40%) !default;\r\n$purple-800: shade-color($purple, 60%) !default;\r\n$purple-900: shade-color($purple, 80%) !default;\r\n\r\n$pink-100: tint-color($pink, 80%) !default;\r\n$pink-200: tint-color($pink, 60%) !default;\r\n$pink-300: tint-color($pink, 40%) !default;\r\n$pink-400: tint-color($pink, 20%) !default;\r\n$pink-500: $pink !default;\r\n$pink-600: shade-color($pink, 20%) !default;\r\n$pink-700: shade-color($pink, 40%) !default;\r\n$pink-800: shade-color($pink, 60%) !default;\r\n$pink-900: shade-color($pink, 80%) !default;\r\n\r\n$red-100: tint-color($red, 80%) !default;\r\n$red-200: tint-color($red, 60%) !default;\r\n$red-300: tint-color($red, 40%) !default;\r\n$red-400: tint-color($red, 20%) !default;\r\n$red-500: $red !default;\r\n$red-600: shade-color($red, 20%) !default;\r\n$red-700: shade-color($red, 40%) !default;\r\n$red-800: shade-color($red, 60%) !default;\r\n$red-900: shade-color($red, 80%) !default;\r\n\r\n$orange-100: tint-color($orange, 80%) !default;\r\n$orange-200: tint-color($orange, 60%) !default;\r\n$orange-300: tint-color($orange, 40%) !default;\r\n$orange-400: tint-color($orange, 20%) !default;\r\n$orange-500: $orange !default;\r\n$orange-600: shade-color($orange, 20%) !default;\r\n$orange-700: shade-color($orange, 40%) !default;\r\n$orange-800: shade-color($orange, 60%) !default;\r\n$orange-900: shade-color($orange, 80%) !default;\r\n\r\n$yellow-100: tint-color($yellow, 80%) !default;\r\n$yellow-200: tint-color($yellow, 60%) !default;\r\n$yellow-300: tint-color($yellow, 40%) !default;\r\n$yellow-400: tint-color($yellow, 20%) !default;\r\n$yellow-500: $yellow !default;\r\n$yellow-600: shade-color($yellow, 20%) !default;\r\n$yellow-700: shade-color($yellow, 40%) !default;\r\n$yellow-800: shade-color($yellow, 60%) !default;\r\n$yellow-900: shade-color($yellow, 80%) !default;\r\n\r\n$green-100: tint-color($green, 80%) !default;\r\n$green-200: tint-color($green, 60%) !default;\r\n$green-300: tint-color($green, 40%) !default;\r\n$green-400: tint-color($green, 20%) !default;\r\n$green-500: $green !default;\r\n$green-600: shade-color($green, 20%) !default;\r\n$green-700: shade-color($green, 40%) !default;\r\n$green-800: shade-color($green, 60%) !default;\r\n$green-900: shade-color($green, 80%) !default;\r\n\r\n$teal-100: tint-color($teal, 80%) !default;\r\n$teal-200: tint-color($teal, 60%) !default;\r\n$teal-300: tint-color($teal, 40%) !default;\r\n$teal-400: tint-color($teal, 20%) !default;\r\n$teal-500: $teal !default;\r\n$teal-600: shade-color($teal, 20%) !default;\r\n$teal-700: shade-color($teal, 40%) !default;\r\n$teal-800: shade-color($teal, 60%) !default;\r\n$teal-900: shade-color($teal, 80%) !default;\r\n\r\n$cyan-100: tint-color($cyan, 80%) !default;\r\n$cyan-200: tint-color($cyan, 60%) !default;\r\n$cyan-300: tint-color($cyan, 40%) !default;\r\n$cyan-400: tint-color($cyan, 20%) !default;\r\n$cyan-500: $cyan !default;\r\n$cyan-600: shade-color($cyan, 20%) !default;\r\n$cyan-700: shade-color($cyan, 40%) !default;\r\n$cyan-800: shade-color($cyan, 60%) !default;\r\n$cyan-900: shade-color($cyan, 80%) !default;\r\n\r\n$blues: (\r\n  \"blue-100\": $blue-100,\r\n  \"blue-200\": $blue-200,\r\n  \"blue-300\": $blue-300,\r\n  \"blue-400\": $blue-400,\r\n  \"blue-500\": $blue-500,\r\n  \"blue-600\": $blue-600,\r\n  \"blue-700\": $blue-700,\r\n  \"blue-800\": $blue-800,\r\n  \"blue-900\": $blue-900\r\n) !default;\r\n\r\n$indigos: (\r\n  \"indigo-100\": $indigo-100,\r\n  \"indigo-200\": $indigo-200,\r\n  \"indigo-300\": $indigo-300,\r\n  \"indigo-400\": $indigo-400,\r\n  \"indigo-500\": $indigo-500,\r\n  \"indigo-600\": $indigo-600,\r\n  \"indigo-700\": $indigo-700,\r\n  \"indigo-800\": $indigo-800,\r\n  \"indigo-900\": $indigo-900\r\n) !default;\r\n\r\n$purples: (\r\n  \"purple-100\": $purple-100,\r\n  \"purple-200\": $purple-200,\r\n  \"purple-300\": $purple-300,\r\n  \"purple-400\": $purple-400,\r\n  \"purple-500\": $purple-500,\r\n  \"purple-600\": $purple-600,\r\n  \"purple-700\": $purple-700,\r\n  \"purple-800\": $purple-800,\r\n  \"purple-900\": $purple-900\r\n) !default;\r\n\r\n$pinks: (\r\n  \"pink-100\": $pink-100,\r\n  \"pink-200\": $pink-200,\r\n  \"pink-300\": $pink-300,\r\n  \"pink-400\": $pink-400,\r\n  \"pink-500\": $pink-500,\r\n  \"pink-600\": $pink-600,\r\n  \"pink-700\": $pink-700,\r\n  \"pink-800\": $pink-800,\r\n  \"pink-900\": $pink-900\r\n) !default;\r\n\r\n$reds: (\r\n  \"red-100\": $red-100,\r\n  \"red-200\": $red-200,\r\n  \"red-300\": $red-300,\r\n  \"red-400\": $red-400,\r\n  \"red-500\": $red-500,\r\n  \"red-600\": $red-600,\r\n  \"red-700\": $red-700,\r\n  \"red-800\": $red-800,\r\n  \"red-900\": $red-900\r\n) !default;\r\n\r\n$oranges: (\r\n  \"orange-100\": $orange-100,\r\n  \"orange-200\": $orange-200,\r\n  \"orange-300\": $orange-300,\r\n  \"orange-400\": $orange-400,\r\n  \"orange-500\": $orange-500,\r\n  \"orange-600\": $orange-600,\r\n  \"orange-700\": $orange-700,\r\n  \"orange-800\": $orange-800,\r\n  \"orange-900\": $orange-900\r\n) !default;\r\n\r\n$yellows: (\r\n  \"yellow-100\": $yellow-100,\r\n  \"yellow-200\": $yellow-200,\r\n  \"yellow-300\": $yellow-300,\r\n  \"yellow-400\": $yellow-400,\r\n  \"yellow-500\": $yellow-500,\r\n  \"yellow-600\": $yellow-600,\r\n  \"yellow-700\": $yellow-700,\r\n  \"yellow-800\": $yellow-800,\r\n  \"yellow-900\": $yellow-900\r\n) !default;\r\n\r\n$greens: (\r\n  \"green-100\": $green-100,\r\n  \"green-200\": $green-200,\r\n  \"green-300\": $green-300,\r\n  \"green-400\": $green-400,\r\n  \"green-500\": $green-500,\r\n  \"green-600\": $green-600,\r\n  \"green-700\": $green-700,\r\n  \"green-800\": $green-800,\r\n  \"green-900\": $green-900\r\n) !default;\r\n\r\n$teals: (\r\n  \"teal-100\": $teal-100,\r\n  \"teal-200\": $teal-200,\r\n  \"teal-300\": $teal-300,\r\n  \"teal-400\": $teal-400,\r\n  \"teal-500\": $teal-500,\r\n  \"teal-600\": $teal-600,\r\n  \"teal-700\": $teal-700,\r\n  \"teal-800\": $teal-800,\r\n  \"teal-900\": $teal-900\r\n) !default;\r\n\r\n$cyans: (\r\n  \"cyan-100\": $cyan-100,\r\n  \"cyan-200\": $cyan-200,\r\n  \"cyan-300\": $cyan-300,\r\n  \"cyan-400\": $cyan-400,\r\n  \"cyan-500\": $cyan-500,\r\n  \"cyan-600\": $cyan-600,\r\n  \"cyan-700\": $cyan-700,\r\n  \"cyan-800\": $cyan-800,\r\n  \"cyan-900\": $cyan-900\r\n) !default;\r\n// fusv-enable\r\n\r\n// scss-docs-start theme-color-variables\r\n$primary:       $green !default;\r\n$secondary:     $green-light !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         $gray-100 !default;\r\n$dark:          $gray-900 !default;\r\n// scss-docs-end theme-color-variables\r\n\r\n// hover variables\r\n$primary-bg-hover:       #0f280b !default;\r\n$secondary-bg-hover:     #00563f !default;\r\n$success-bg-hover:       #0f280b !default;\r\n$info-bg-hover:          #1b458d !default;\r\n$warning-bg-hover:       #dd8002 !default;\r\n$danger-bg-hover:        #9a1e12 !default;\r\n$light-bg-hover:         #dcdbdb !default;\r\n$dark-bg-hover:          black !default;\r\n\r\n// scss-docs-start theme-colors-map\r\n$theme-colors: (\r\n  \"primary\":    $primary,\r\n  \"secondary\":  $secondary,\r\n  \"success\":    $success,\r\n  \"info\":       $info,\r\n  \"warning\":    $warning,\r\n  \"danger\":     $danger,\r\n  \"light\":      $light,\r\n  \"dark\":       $dark\r\n) !default;\r\n// scss-docs-end theme-colors-map\r\n\r\n// scss-docs-start theme-text-variables\r\n$primary-text-emphasis:   shade-color($primary, 60%) !default;\r\n$secondary-text-emphasis: shade-color($secondary, 60%) !default;\r\n$success-text-emphasis:   shade-color($success, 60%) !default;\r\n$info-text-emphasis:      shade-color($info, 60%) !default;\r\n$warning-text-emphasis:   shade-color($warning, 60%) !default;\r\n$danger-text-emphasis:    shade-color($danger, 60%) !default;\r\n$light-text-emphasis:     $gray-700 !default;\r\n$dark-text-emphasis:      $gray-700 !default;\r\n// scss-docs-end theme-text-variables\r\n\r\n// scss-docs-start theme-bg-subtle-variables\r\n$primary-bg-subtle:       tint-color($primary, 80%) !default;\r\n$secondary-bg-subtle:     tint-color($secondary, 80%) !default;\r\n$success-bg-subtle:       tint-color($success, 80%) !default;\r\n$info-bg-subtle:          tint-color($info, 80%) !default;\r\n$warning-bg-subtle:       tint-color($warning, 80%) !default;\r\n$danger-bg-subtle:        tint-color($danger, 80%) !default;\r\n$light-bg-subtle:         mix($gray-100, $white) !default;\r\n$dark-bg-subtle:          $gray-400 !default;\r\n// scss-docs-end theme-bg-subtle-variables\r\n\r\n// scss-docs-start theme-border-subtle-variables\r\n$primary-border-subtle:   tint-color($primary, 60%) !default;\r\n$secondary-border-subtle: tint-color($secondary, 60%) !default;\r\n$success-border-subtle:   tint-color($success, 60%) !default;\r\n$info-border-subtle:      tint-color($info, 60%) !default;\r\n$warning-border-subtle:   tint-color($warning, 60%) !default;\r\n$danger-border-subtle:    tint-color($danger, 60%) !default;\r\n$light-border-subtle:     $gray-200 !default;\r\n$dark-border-subtle:      $gray-500 !default;\r\n// scss-docs-end theme-border-subtle-variables\r\n\r\n// Characters which are escaped by the escape-svg function\r\n$escaped-characters: (\r\n  (\"<\", \"%3c\"),\r\n  (\">\", \"%3e\"),\r\n  (\"#\", \"%23\"),\r\n  (\"(\", \"%28\"),\r\n  (\")\", \"%29\"),\r\n) !default;\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-caret:                true !default;\r\n$enable-rounded:              true !default;\r\n$enable-shadows:              false !default;\r\n$enable-gradients:            false !default;\r\n$enable-transitions:          true !default;\r\n$enable-reduced-motion:       true !default;\r\n$enable-smooth-scroll:        true !default;\r\n$enable-grid-classes:         true !default;\r\n$enable-container-classes:    true !default;\r\n$enable-cssgrid:              false !default;\r\n$enable-button-pointers:      true !default;\r\n$enable-rfs:                  true !default;\r\n$enable-validation-icons:     true !default;\r\n$enable-negative-margins:     false !default;\r\n$enable-deprecation-messages: true !default;\r\n$enable-important-utilities:  true !default;\r\n\r\n$enable-dark-mode:            true !default;\r\n$color-mode-type:             data !default; // `data` or `media-query`\r\n\r\n// Prefix for :root CSS variables\r\n\r\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\r\n$prefix:                      $variable-prefix !default;\r\n\r\n// Gradient\r\n//\r\n// The gradient which is added to components if `$enable-gradients` is `true`\r\n// This gradient is also added to elements with `.bg-gradient`\r\n// scss-docs-start variable-gradient\r\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\r\n// scss-docs-end variable-gradient\r\n\r\n// Spacing\r\n//\r\n// Control the default styling of most Bootstrap elements by modifying these\r\n// variables. Mostly focused on spacing.\r\n// You can add more entries to the $spacers map, should you need more variation.\r\n\r\n// scss-docs-start spacer-variables-maps\r\n$spacer: 1rem !default;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * .25,\r\n  2: $spacer * .5,\r\n  3: $spacer,\r\n  4: $spacer * 1.5,\r\n  5: $spacer * 3,\r\n) !default;\r\n// scss-docs-end spacer-variables-maps\r\n\r\n// Position\r\n//\r\n// Define the edge positioning anchors of the position utilities.\r\n\r\n// scss-docs-start position-map\r\n$position-values: (\r\n  0: 0,\r\n  50: 50%,\r\n  100: 100%\r\n) !default;\r\n// scss-docs-end position-map\r\n\r\n// Body\r\n//\r\n// Settings for the `<body>` element.\r\n\r\n$body-text-align:           null !default;\r\n$body-color:                $gray-900 !default;\r\n$body-bg:                   $white !default;\r\n\r\n$body-secondary-color:      rgba($body-color, .75) !default;\r\n$body-secondary-bg:         $gray-200 !default;\r\n\r\n$body-tertiary-color:       rgba($body-color, .5) !default;\r\n$body-tertiary-bg:          $gray-100 !default;\r\n\r\n$body-emphasis-color:       $black !default;\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n\r\n$link-color:                              $primary !default;\r\n$link-decoration:                         underline !default;\r\n$link-shade-percentage:                   20% !default;\r\n$link-hover-color:                        shift-color-hard($link-color) !default;\r\n$link-hover-decoration:                   null !default;\r\n\r\n$stretched-link-pseudo-element:           after !default;\r\n$stretched-link-z-index:                  1 !default;\r\n\r\n// Paragraphs\r\n//\r\n// Style p element.\r\n\r\n$paragraph-margin-bottom:   1rem !default;\r\n\r\n\r\n// Grid breakpoints\r\n//\r\n// Define the minimum dimensions at which your layout will change,\r\n// adapting to different screen sizes, for use in media queries.\r\n\r\n// scss-docs-start grid-breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1400px\r\n) !default;\r\n// scss-docs-end grid-breakpoints\r\n\r\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\r\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\r\n\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px,\r\n  xxl: 1320px\r\n) !default;\r\n// scss-docs-end container-max-widths\r\n\r\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\r\n\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n\r\n$grid-columns:                12 !default;\r\n$grid-gutter-width:           1.5rem !default;\r\n$grid-row-columns:            6 !default;\r\n\r\n// Container padding\r\n\r\n$container-padding-x: $grid-gutter-width !default;\r\n\r\n\r\n// Components\r\n//\r\n// Define common padding and border radius sizes and more.\r\n\r\n// scss-docs-start border-variables\r\n$border-width:                1px !default;\r\n$border-widths: (\r\n  1: 1px,\r\n  2: 2px,\r\n  3: 3px,\r\n  4: 4px,\r\n  5: 5px\r\n) !default;\r\n$border-style:                solid !default;\r\n$border-color:                $gray-300 !default;\r\n$border-color-translucent:    rgba($black, .175) !default;\r\n// scss-docs-end border-variables\r\n\r\n// scss-docs-start border-radius-variables\r\n$border-radius:               .375rem !default;\r\n$border-radius-sm:            .25rem !default;\r\n$border-radius-lg:            .5rem !default;\r\n$border-radius-xl:            1rem !default;\r\n$border-radius-xxl:           2rem !default;\r\n$border-radius-pill:          50rem !default;\r\n// scss-docs-end border-radius-variables\r\n// fusv-disable\r\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\r\n// fusv-enable\r\n\r\n// scss-docs-start box-shadow-variables\r\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\r\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\r\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\r\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\r\n// scss-docs-end box-shadow-variables\r\n\r\n$component-active-color:      $white !default;\r\n$component-active-bg:         $success !default;\r\n\r\n// scss-docs-start focus-ring-variables\r\n$focus-ring-width:      .25rem !default;\r\n$focus-ring-opacity:    .25 !default;\r\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\r\n$focus-ring-blur:       0 !default;\r\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color !default;\r\n// scss-docs-end focus-ring-variables\r\n\r\n// scss-docs-start caret-variables\r\n$caret-width:                 .3em !default;\r\n$caret-vertical-align:        $caret-width * .85 !default;\r\n$caret-spacing:               $caret-width * .85 !default;\r\n// scss-docs-end caret-variables\r\n\r\n$transition-base:             all .2s ease-in-out !default;\r\n$transition-fade:             opacity .15s linear !default;\r\n// scss-docs-start collapse-transition\r\n$transition-collapse:         height .35s ease !default;\r\n$transition-collapse-width:   width .35s ease !default;\r\n// scss-docs-end collapse-transition\r\n\r\n// stylelint-disable function-disallowed-list\r\n// scss-docs-start aspect-ratios\r\n$aspect-ratios: (\r\n  \"1x1\": 100%,\r\n  \"4x3\": calc(3 / 4 * 100%),\r\n  \"16x9\": calc(9 / 16 * 100%),\r\n  \"21x9\": calc(9 / 21 * 100%)\r\n) !default;\r\n// scss-docs-end aspect-ratios\r\n// stylelint-enable function-disallowed-list\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// scss-docs-start font-variables\r\n// stylelint-disable value-keyword-case\r\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\r\n// stylelint-enable value-keyword-case\r\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\r\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\r\n\r\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\r\n// $font-size-base affects the font size of the body text\r\n$font-size-root:              null !default;\r\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\r\n$font-size-sm:                $font-size-base * .875 !default;\r\n$font-size-lg:                $font-size-base * 1.25 !default;\r\n\r\n$font-weight-lighter:         lighter !default;\r\n$font-weight-light:           300 !default;\r\n$font-weight-normal:          400 !default;\r\n$font-weight-medium:          500 !default;\r\n$font-weight-semibold:        600 !default;\r\n$font-weight-bold:            700 !default;\r\n$font-weight-bolder:          bolder !default;\r\n\r\n$font-weight-base:            $font-weight-normal !default;\r\n\r\n$line-height-base:            1.5 !default;\r\n$line-height-sm:              1.25 !default;\r\n$line-height-lg:              2 !default;\r\n\r\n$h1-font-size:                $font-size-base * 2.5 !default;\r\n$h2-font-size:                $font-size-base * 2 !default;\r\n$h3-font-size:                $font-size-base * 1.75 !default;\r\n$h4-font-size:                $font-size-base * 1.5 !default;\r\n$h5-font-size:                $font-size-base * 1.25 !default;\r\n$h6-font-size:                $font-size-base !default;\r\n// scss-docs-end font-variables\r\n\r\n// scss-docs-start font-sizes\r\n$font-sizes: (\r\n  1: $h1-font-size,\r\n  2: $h2-font-size,\r\n  3: $h3-font-size,\r\n  4: $h4-font-size,\r\n  5: $h5-font-size,\r\n  6: $h6-font-size\r\n) !default;\r\n// scss-docs-end font-sizes\r\n\r\n// scss-docs-start headings-variables\r\n$headings-margin-bottom:      $spacer * .5 !default;\r\n$headings-font-family:        null !default;\r\n$headings-font-style:         null !default;\r\n$headings-font-weight:        500 !default;\r\n$headings-line-height:        1.2 !default;\r\n$headings-color:              null !default;\r\n// scss-docs-end headings-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: 5rem,\r\n  2: 4.5rem,\r\n  3: 4rem,\r\n  4: 3.5rem,\r\n  5: 3rem,\r\n  6: 2.5rem\r\n) !default;\r\n\r\n$display-font-family: null !default;\r\n$display-font-style:  null !default;\r\n$display-font-weight: 300 !default;\r\n$display-line-height: $headings-line-height !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size:              $font-size-base * 1.25 !default;\r\n$lead-font-weight:            300 !default;\r\n\r\n$small-font-size:             .875em !default;\r\n\r\n$sub-sup-font-size:           .75em !default;\r\n\r\n$text-high-emphasis:            $high-emphasis !default;\r\n$text-medium-emphasis:          $medium-emphasis !default;\r\n$text-disabled:                 $disabled !default;\r\n\r\n$text-high-emphasis-inverse:    $high-emphasis-inverse-new !default;\r\n$text-medium-emphasis-inverse:  $medium-emphasis-inverse-new !default;\r\n$text-disabled-inverse:         $disabled-inverse-new !default;\r\n\r\n// fusv-disable\r\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\r\n// fusv-enable\r\n\r\n$initialism-font-size:        $small-font-size !default;\r\n\r\n$blockquote-margin-y:         $spacer !default;\r\n$blockquote-font-size:        $font-size-base * 1.25 !default;\r\n$blockquote-footer-color:     $gray-600 !default;\r\n$blockquote-footer-font-size: $small-font-size !default;\r\n\r\n$hr-margin-y:                 $spacer !default;\r\n$hr-color:                    inherit !default;\r\n\r\n// fusv-disable\r\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\r\n$hr-height:                   null !default; // Deprecated in v5.2.0\r\n// fusv-enable\r\n\r\n$hr-border-color:             null !default; // Allows for inherited colors\r\n$hr-border-width:             var(--#{$prefix}border-width) !default;\r\n$hr-opacity:                  .25 !default;\r\n\r\n$legend-margin-bottom:        .5rem !default;\r\n$legend-font-size:            1.5rem !default;\r\n$legend-font-weight:          null !default;\r\n\r\n$dt-font-weight:              $font-weight-bold !default;\r\n\r\n$list-inline-padding:         .5rem !default;\r\n\r\n$mark-padding:                .1875em !default;\r\n$mark-bg:                     $yellow-100 !default;\r\n// scss-docs-end type-variables\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n// scss-docs-start table-variables\r\n$table-cell-padding-y:        .5rem !default;\r\n$table-cell-padding-x:        .5rem !default;\r\n$table-cell-padding-y-sm:     .25rem !default;\r\n$table-cell-padding-x-sm:     .25rem !default;\r\n\r\n$table-cell-vertical-align:   top !default;\r\n\r\n$table-color:                 var(--#{$prefix}body-color) !default;\r\n$table-bg:                    transparent !default;\r\n$table-accent-bg:             transparent !default;\r\n\r\n$table-th-font-weight:        null !default;\r\n\r\n$table-striped-color:         $table-color !default;\r\n$table-striped-bg-factor:     .05 !default;\r\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\r\n\r\n$table-active-color:          $table-color !default;\r\n$table-active-bg-factor:      .1 !default;\r\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\r\n\r\n$table-hover-color:           $table-color !default;\r\n$table-hover-bg-factor:       .075 !default;\r\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\r\n\r\n$table-border-factor:         .1 !default;\r\n$table-border-width:          var(--#{$prefix}border-width) !default;\r\n$table-border-color:          var(--#{$prefix}border-color) !default;\r\n\r\n$table-striped-order:         odd !default;\r\n$table-striped-columns-order: even !default;\r\n\r\n$table-group-separator-color: currentcolor !default;\r\n\r\n$table-caption-color:         var(--#{$prefix}secondary-color) !default;\r\n\r\n$table-bg-scale:              -80% !default;\r\n// scss-docs-end table-variables\r\n\r\n// scss-docs-start table-loop\r\n$table-variants: (\r\n  \"primary\":    shift-color($primary, $table-bg-scale),\r\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\r\n  \"success\":    shift-color($success, $table-bg-scale),\r\n  \"info\":       shift-color($info, $table-bg-scale),\r\n  \"warning\":    shift-color($warning, $table-bg-scale),\r\n  \"danger\":     shift-color($danger, $table-bg-scale),\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n) !default;\r\n// scss-docs-end table-loop\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y:         .375rem !default;\r\n$input-btn-padding-x:         .75rem !default;\r\n$input-btn-font-family:       null !default;\r\n$input-btn-font-size:         $font-size-base !default;\r\n$input-btn-line-height:       $line-height-base !default;\r\n\r\n$input-btn-focus-width:         $focus-ring-width !default;\r\n$input-btn-focus-color-opacity: $focus-ring-opacity !default;\r\n$input-btn-focus-color:         $focus-ring-color !default;\r\n$input-btn-focus-blur:          $focus-ring-blur !default;\r\n$input-btn-focus-box-shadow:    $focus-ring-box-shadow !default;\r\n\r\n$input-btn-padding-y-sm:      .25rem !default;\r\n$input-btn-padding-x-sm:      .5rem !default;\r\n$input-btn-font-size-sm:      $font-size-sm !default;\r\n\r\n$input-btn-padding-y-lg:      .5rem !default;\r\n$input-btn-padding-x-lg:      1rem !default;\r\n$input-btn-font-size-lg:      $font-size-lg !default;\r\n\r\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n\r\n// Buttons\r\n//\r\n// For each of Bootstrap's buttons, define text, background, and border color.\r\n\r\n// scss-docs-start btn-variables\r\n$btn-padding-y:               $input-btn-padding-y !default;\r\n$btn-padding-x:               $input-btn-padding-x !default;\r\n$btn-font-family:             $input-btn-font-family !default;\r\n$btn-font-size:               $input-btn-font-size !default;\r\n$btn-line-height:             $input-btn-line-height !default;\r\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\r\n\r\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\r\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\r\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\r\n\r\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\r\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\r\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\r\n\r\n$btn-border-width:            $input-btn-border-width !default;\r\n\r\n$btn-font-weight:             $font-weight-normal !default;\r\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\r\n$btn-focus-width:             $input-btn-focus-width !default;\r\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\r\n$btn-disabled-opacity:        .65 !default;\r\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\r\n\r\n$btn-link-color:              var(--#{$prefix}link-color) !default;\r\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\r\n$btn-link-disabled-color:     $gray-600 !default;\r\n\r\n// Allows for customizing button radius independently from global border radius\r\n$btn-border-radius:           $border-radius !default;\r\n$btn-border-radius-sm:        $border-radius-sm !default;\r\n$btn-border-radius-lg:        $border-radius-lg !default;\r\n\r\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$btn-hover-bg-shade-amount:       15% !default;\r\n$btn-hover-bg-tint-amount:        15% !default;\r\n$btn-hover-border-shade-amount:   20% !default;\r\n$btn-hover-border-tint-amount:    10% !default;\r\n$btn-active-bg-shade-amount:      20% !default;\r\n$btn-active-bg-tint-amount:       20% !default;\r\n$btn-active-border-shade-amount:  25% !default;\r\n$btn-active-border-tint-amount:   10% !default;\r\n// scss-docs-end btn-variables\r\n\r\n\r\n// Forms\r\n\r\n// scss-docs-start form-text-variables\r\n$form-text-margin-top:                  .25rem !default;\r\n$form-text-font-size:                   $small-font-size !default;\r\n$form-text-font-style:                  null !default;\r\n$form-text-font-weight:                 null !default;\r\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end form-text-variables\r\n\r\n// scss-docs-start form-label-variables\r\n$form-label-margin-bottom:              .25rem !default;\r\n$form-label-font-size:                  null !default;\r\n$form-label-font-style:                 null !default;\r\n$form-label-font-weight:                null !default;\r\n$form-label-color:                      null !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-input-variables\r\n$input-padding-y:                       $input-btn-padding-y !default;\r\n$input-padding-x:                       $input-btn-padding-x !default;\r\n$input-font-family:                     $input-btn-font-family !default;\r\n$input-font-size:                       $input-btn-font-size !default;\r\n$input-font-weight:                     $font-weight-base !default;\r\n$input-line-height:                     $input-btn-line-height !default;\r\n\r\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\r\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\r\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\r\n\r\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\r\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\r\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\r\n\r\n$input-bg:                              var(--#{$prefix}body-bg) !default;\r\n$input-disabled-color:                  null !default;\r\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\r\n$input-disabled-border-color:           null !default;\r\n\r\n$input-color:                           var(--#{$prefix}body-color) !default;\r\n$input-border-color:                    var(--#{$prefix}border-color) !default;\r\n$input-border-width:                    $input-btn-border-width !default;\r\n$input-box-shadow:                      $box-shadow-inset !default;\r\n\r\n$input-border-radius:                   $border-radius !default;\r\n$input-border-radius-sm:                $border-radius-sm !default;\r\n$input-border-radius-lg:                $border-radius-lg !default;\r\n\r\n$input-focus-bg:                        $input-bg !default;\r\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\r\n$input-focus-color:                     $input-color !default;\r\n$input-focus-width:                     $input-btn-focus-width !default;\r\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\r\n\r\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\r\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\r\n\r\n$input-height-border:                   calc($input-border-width * 2) !default; // stylelint-disable-line function-disallowed-list\r\n\r\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\r\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\r\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\r\n\r\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\r\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\r\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\r\n\r\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$form-color-width:                      3rem !default;\r\n// scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-input-width:                  1em !default;\r\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\r\n$form-check-padding-start:                $form-check-input-width + .5em !default;\r\n$form-check-margin-bottom:                .125rem !default;\r\n$form-check-label-color:                  null !default;\r\n$form-check-label-cursor:                 null !default;\r\n$form-check-transition:                   null !default;\r\n\r\n$form-check-input-active-filter:          brightness(90%) !default;\r\n\r\n$form-check-input-bg:                     $input-bg !default;\r\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\r\n$form-check-input-border-radius:          .25em !default;\r\n$form-check-radio-border-radius:          50% !default;\r\n$form-check-input-focus-border:           $input-focus-border-color !default;\r\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\r\n\r\n$form-check-input-checked-color:          $component-active-color !default;\r\n$form-check-input-checked-bg-color:       $component-active-bg !default;\r\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\r\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\r\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\r\n\r\n$form-check-input-indeterminate-color:          $component-active-color !default;\r\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\r\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\r\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\r\n\r\n$form-check-input-disabled-opacity:        .5 !default;\r\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\r\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\r\n\r\n$form-check-inline-margin-end:    1rem !default;\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n$form-switch-color:               rgba($black, .25) !default;\r\n$form-switch-width:               2em !default;\r\n$form-switch-padding-start:       $form-switch-width + .5em !default;\r\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\r\n$form-switch-border-radius:       $form-switch-width !default;\r\n$form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n$form-switch-focus-color:         $input-focus-border-color !default;\r\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\r\n\r\n$form-switch-checked-color:       $component-active-color !default;\r\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\r\n$form-switch-checked-bg-position: right center !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start input-group-variables\r\n$input-group-addon-padding-y:           $input-padding-y !default;\r\n$input-group-addon-padding-x:           $input-padding-x !default;\r\n$input-group-addon-font-weight:         $input-font-weight !default;\r\n$input-group-addon-color:               $input-color !default;\r\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\r\n$input-group-addon-border-color:        $input-border-color !default;\r\n// scss-docs-end input-group-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-padding-y:             $input-padding-y !default;\r\n$form-select-padding-x:             $input-padding-x !default;\r\n$form-select-font-family:           $input-font-family !default;\r\n$form-select-font-size:             $input-font-size !default;\r\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\r\n$form-select-font-weight:           $input-font-weight !default;\r\n$form-select-line-height:           $input-line-height !default;\r\n$form-select-color:                 $input-color !default;\r\n$form-select-bg:                    $input-bg !default;\r\n$form-select-disabled-color:        null !default;\r\n$form-select-disabled-bg:           $input-disabled-bg !default;\r\n$form-select-disabled-border-color: $input-disabled-border-color !default;\r\n$form-select-bg-position:           right $form-select-padding-x center !default;\r\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\r\n$form-select-indicator-color:       $gray-800 !default;\r\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\r\n\r\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\r\n\r\n$form-select-border-width:        $input-border-width !default;\r\n$form-select-border-color:        $input-border-color !default;\r\n$form-select-border-radius:       $input-border-radius !default;\r\n$form-select-box-shadow:          $box-shadow-inset !default;\r\n\r\n$form-select-focus-border-color:  $input-focus-border-color !default;\r\n$form-select-focus-width:         $input-focus-width !default;\r\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\r\n\r\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\r\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\r\n$form-select-font-size-sm:        $input-font-size-sm !default;\r\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\r\n\r\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\r\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\r\n$form-select-font-size-lg:        $input-font-size-lg !default;\r\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\r\n\r\n$form-select-transition:          $input-transition !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-width:          100% !default;\r\n$form-range-track-height:         .5rem !default;\r\n$form-range-track-cursor:         pointer !default;\r\n$form-range-track-bg:             var(--#{$prefix}tertiary-bg) !default;\r\n$form-range-track-border-radius:  1rem !default;\r\n$form-range-track-box-shadow:     $box-shadow-inset !default;\r\n\r\n$form-range-thumb-width:                   1rem !default;\r\n$form-range-thumb-height:                  $form-range-thumb-width !default;\r\n$form-range-thumb-bg:                      $component-active-bg !default;\r\n$form-range-thumb-border:                  0 !default;\r\n$form-range-thumb-border-radius:           1rem !default;\r\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\r\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\r\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\r\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\r\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\r\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-range-variables\r\n\r\n// scss-docs-start form-file-variables\r\n$form-file-button-color:          $input-color !default;\r\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\r\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\r\n// scss-docs-end form-file-variables\r\n\r\n// scss-docs-start form-floating-variables\r\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\r\n$form-floating-line-height:             1.25 !default;\r\n$form-floating-padding-x:               $input-padding-x !default;\r\n$form-floating-padding-y:               1rem !default;\r\n$form-floating-input-padding-t:         1.625rem !default;\r\n$form-floating-input-padding-b:         .625rem !default;\r\n$form-floating-label-height:            1.875em !default;\r\n$form-floating-label-opacity:           .65 !default;\r\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\r\n$form-floating-label-disabled-color:    $gray-600 !default;\r\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\r\n// scss-docs-end form-floating-variables\r\n\r\n// Form validation\r\n\r\n// scss-docs-start form-feedback-variables\r\n$form-feedback-margin-top:          $form-text-margin-top !default;\r\n$form-feedback-font-size:           $form-text-font-size !default;\r\n$form-feedback-font-style:          $form-text-font-style !default;\r\n$form-feedback-valid-color:         $success !default;\r\n$form-feedback-invalid-color:       $danger !default;\r\n\r\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\r\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\r\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\r\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\r\n// scss-docs-end form-feedback-variables\r\n\r\n// scss-docs-start form-validation-colors\r\n$form-valid-color:                  $form-feedback-valid-color !default;\r\n$form-valid-border-color:           $form-feedback-valid-color !default;\r\n$form-invalid-color:                $form-feedback-invalid-color !default;\r\n$form-invalid-border-color:         $form-feedback-invalid-color !default;\r\n// scss-docs-end form-validation-colors\r\n\r\n// scss-docs-start form-validation-states\r\n$form-validation-states: (\r\n  \"valid\": (\r\n    \"color\": var(--#{$prefix}form-valid-color),\r\n    \"icon\": $form-feedback-icon-valid,\r\n    \"tooltip-color\": #fff,\r\n    \"tooltip-bg-color\": var(--#{$prefix}success),\r\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\r\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\r\n  ),\r\n  \"invalid\": (\r\n    \"color\": var(--#{$prefix}form-invalid-color),\r\n    \"icon\": $form-feedback-icon-invalid,\r\n    \"tooltip-color\": #fff,\r\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\r\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\r\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\r\n  )\r\n) !default;\r\n// scss-docs-end form-validation-states\r\n\r\n// Z-index master list\r\n//\r\n// Warning: Avoid customizing these values. They're used for a bird's eye view\r\n// of components dependent on the z-axis and are designed to all work together.\r\n\r\n// scss-docs-start zindex-stack\r\n$zindex-dropdown:                   1000 !default;\r\n$zindex-sticky:                     1020 !default;\r\n$zindex-fixed:                      1030 !default;\r\n$zindex-offcanvas-backdrop:         1040 !default;\r\n$zindex-offcanvas:                  1045 !default;\r\n$zindex-modal-backdrop:             1050 !default;\r\n$zindex-modal:                      1055 !default;\r\n$zindex-popover:                    1070 !default;\r\n$zindex-tooltip:                    1080 !default;\r\n$zindex-toast:                      1090 !default;\r\n// scss-docs-end zindex-stack\r\n\r\n// scss-docs-start zindex-levels-map\r\n$zindex-levels: (\r\n  n1: -1,\r\n  0: 0,\r\n  1: 1,\r\n  2: 2,\r\n  3: 3\r\n) !default;\r\n// scss-docs-end zindex-levels-map\r\n\r\n\r\n// Navs\r\n\r\n// scss-docs-start nav-variables\r\n$nav-link-padding-y:                .5rem !default;\r\n$nav-link-padding-x:                1rem !default;\r\n$nav-link-font-size:                null !default;\r\n$nav-link-font-weight:              null !default;\r\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\r\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\r\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\r\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\r\n$nav-link-focus-box-shadow:         $focus-ring-box-shadow !default;\r\n\r\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\r\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\r\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\r\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\r\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\r\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\r\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\r\n\r\n$nav-pills-border-radius:           $border-radius !default;\r\n$nav-pills-link-active-color:       $component-active-color !default;\r\n$nav-pills-link-active-bg:          $component-active-bg !default;\r\n\r\n$nav-underline-gap:                 1rem !default;\r\n$nav-underline-border-width:        .125rem !default;\r\n$nav-underline-link-active-color:   var(--#{$prefix}emphasis-color) !default;\r\n// scss-docs-end nav-variables\r\n\r\n\r\n// Navbar\r\n\r\n// scss-docs-start navbar-variables\r\n$navbar-padding-y:                  $spacer * .5 !default;\r\n$navbar-padding-x:                  null !default;\r\n\r\n$navbar-nav-link-padding-x:         .5rem !default;\r\n\r\n$navbar-brand-font-size:            $font-size-lg !default;\r\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\r\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\r\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\r\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\r\n$navbar-brand-margin-end:           1rem !default;\r\n\r\n$navbar-toggler-padding-y:          .25rem !default;\r\n$navbar-toggler-padding-x:          .75rem !default;\r\n$navbar-toggler-font-size:          $font-size-lg !default;\r\n$navbar-toggler-border-radius:      $btn-border-radius !default;\r\n$navbar-toggler-focus-width:        $btn-focus-width !default;\r\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\r\n\r\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\r\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\r\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\r\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\r\n$navbar-light-icon-color:           rgba($body-color, .75) !default;\r\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\r\n$navbar-light-brand-color:          $navbar-light-active-color !default;\r\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\r\n// scss-docs-end navbar-variables\r\n\r\n// scss-docs-start navbar-dark-variables\r\n$navbar-dark-color:                 rgba($white, .55) !default;\r\n$navbar-dark-hover-color:           rgba($white, .75) !default;\r\n$navbar-dark-active-color:          $white !default;\r\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\r\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\r\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\r\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\r\n// scss-docs-end navbar-dark-variables\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n// scss-docs-start dropdown-variables\r\n$dropdown-min-width:                10rem !default;\r\n$dropdown-padding-x:                0 !default;\r\n$dropdown-padding-y:                .5rem !default;\r\n$dropdown-spacer:                   .125rem !default;\r\n$dropdown-font-size:                $font-size-base !default;\r\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\r\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\r\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\r\n$dropdown-border-radius:            $border-radius !default;\r\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\r\n$dropdown-inner-border-radius:      calc($dropdown-border-radius - $dropdown-border-width) !default; // stylelint-disable-line function-disallowed-list\r\n$dropdown-divider-bg:               $dropdown-border-color !default;\r\n$dropdown-divider-margin-y:         $spacer * .5 !default;\r\n$dropdown-box-shadow:               $box-shadow !default;\r\n\r\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\r\n$dropdown-link-hover-color:         $dropdown-link-color !default;\r\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\r\n\r\n$dropdown-link-active-color:        $component-active-color !default;\r\n$dropdown-link-active-bg:           $component-active-bg !default;\r\n\r\n$dropdown-link-disabled-color:      var(--#{$prefix}tertiary-color) !default;\r\n\r\n$dropdown-item-padding-y:           $spacer * .25 !default;\r\n$dropdown-item-padding-x:           $spacer !default;\r\n\r\n$dropdown-header-color:             $gray-600 !default;\r\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\r\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\r\n// fusv-disable\r\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\r\n// fusv-enable\r\n// scss-docs-end dropdown-variables\r\n\r\n// scss-docs-start dropdown-dark-variables\r\n$dropdown-dark-color:               $gray-300 !default;\r\n$dropdown-dark-bg:                  $gray-800 !default;\r\n$dropdown-dark-border-color:        $dropdown-border-color !default;\r\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\r\n$dropdown-dark-box-shadow:          null !default;\r\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\r\n$dropdown-dark-link-hover-color:    $white !default;\r\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\r\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\r\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\r\n$dropdown-dark-link-disabled-color: $gray-500 !default;\r\n$dropdown-dark-header-color:        $gray-500 !default;\r\n// scss-docs-end dropdown-dark-variables\r\n\r\n\r\n// Pagination\r\n\r\n// scss-docs-start pagination-variables\r\n$pagination-padding-y:              .375rem !default;\r\n$pagination-padding-x:              .75rem !default;\r\n$pagination-padding-y-sm:           .25rem !default;\r\n$pagination-padding-x-sm:           .5rem !default;\r\n$pagination-padding-y-lg:           .75rem !default;\r\n$pagination-padding-x-lg:           1.5rem !default;\r\n\r\n$pagination-font-size:              $font-size-base !default;\r\n\r\n$pagination-color:                  var(--#{$prefix}link-color) !default;\r\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\r\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\r\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\r\n$pagination-margin-start:           calc($pagination-border-width * -1) !default; // stylelint-disable-line function-disallowed-list\r\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\r\n\r\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\r\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\r\n$pagination-focus-box-shadow:       $focus-ring-box-shadow !default;\r\n$pagination-focus-outline:          0 !default;\r\n\r\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\r\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\r\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\r\n\r\n$pagination-active-color:           $component-active-color !default;\r\n$pagination-active-bg:              $component-active-bg !default;\r\n$pagination-active-border-color:    $component-active-bg !default;\r\n\r\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\r\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\r\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\r\n\r\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$pagination-border-radius-sm:       $border-radius-sm !default;\r\n$pagination-border-radius-lg:       $border-radius-lg !default;\r\n// scss-docs-end pagination-variables\r\n\r\n\r\n// Placeholders\r\n\r\n// scss-docs-start placeholders\r\n$placeholder-opacity-max:           .5 !default;\r\n$placeholder-opacity-min:           .2 !default;\r\n// scss-docs-end placeholders\r\n\r\n// Cards\r\n\r\n// scss-docs-start card-variables\r\n$card-spacer-y:                     $spacer !default;\r\n$card-spacer-x:                     $spacer !default;\r\n$card-title-spacer-y:               $spacer * .5 !default;\r\n$card-title-color:                  null !default;\r\n$card-subtitle-color:               null !default;\r\n$card-border-width:                 var(--#{$prefix}border-width) !default;\r\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\r\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\r\n$card-box-shadow:                   null !default;\r\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\r\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\r\n$card-cap-padding-x:                $card-spacer-x !default;\r\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\r\n$card-cap-color:                    null !default;\r\n$card-height:                       null !default;\r\n$card-color:                        null !default;\r\n$card-bg:                           var(--#{$prefix}body-bg) !default;\r\n$card-img-overlay-padding:          $spacer !default;\r\n$card-group-margin:                 $grid-gutter-width * .5 !default;\r\n// scss-docs-end card-variables\r\n\r\n// Accordion\r\n\r\n// scss-docs-start accordion-variables\r\n$accordion-padding-y:                     1rem !default;\r\n$accordion-padding-x:                     1.25rem !default;\r\n$accordion-color:                         var(--#{$prefix}body-color) !default;\r\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\r\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\r\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\r\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\r\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\r\n\r\n$accordion-body-padding-y:                $accordion-padding-y !default;\r\n$accordion-body-padding-x:                $accordion-padding-x !default;\r\n\r\n$accordion-button-padding-y:              $accordion-padding-y !default;\r\n$accordion-button-padding-x:              $accordion-padding-x !default;\r\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\r\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\r\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\r\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\r\n$accordion-button-active-color:           var(--#{$prefix}primary-text) !default;\r\n\r\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\r\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\r\n\r\n$accordion-icon-width:                    1.25rem !default;\r\n$accordion-icon-color:                    $body-color !default;\r\n$accordion-icon-active-color:             $primary-text-emphasis !default;\r\n$accordion-icon-transition:               transform .2s ease-in-out !default;\r\n$accordion-icon-transform:                rotate(-180deg) !default;\r\n\r\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n// scss-docs-end accordion-variables\r\n\r\n// Tooltips\r\n\r\n// scss-docs-start tooltip-variables\r\n$tooltip-font-size:                 $font-size-sm !default;\r\n$tooltip-max-width:                 200px !default;\r\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\r\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\r\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\r\n$tooltip-opacity:                   .9 !default;\r\n$tooltip-padding-y:                 $spacer * .25 !default;\r\n$tooltip-padding-x:                 $spacer * .5 !default;\r\n$tooltip-margin:                    null !default; // TODO: remove this in v6\r\n\r\n$tooltip-arrow-width:               .8rem !default;\r\n$tooltip-arrow-height:              .4rem !default;\r\n// fusv-disable\r\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\r\n// fusv-enable\r\n// scss-docs-end tooltip-variables\r\n\r\n// Form tooltips must come after regular tooltips\r\n// scss-docs-start tooltip-feedback-variables\r\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\r\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\r\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\r\n$form-feedback-tooltip-line-height:   null !default;\r\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\r\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\r\n// scss-docs-end tooltip-feedback-variables\r\n\r\n\r\n// Popovers\r\n\r\n// scss-docs-start popover-variables\r\n$popover-font-size:                 $font-size-sm !default;\r\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\r\n$popover-max-width:                 276px !default;\r\n$popover-border-width:              var(--#{$prefix}border-width) !default;\r\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\r\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\r\n$popover-inner-border-radius:       calc($popover-border-radius - $popover-border-width) !default; // stylelint-disable-line function-disallowed-list\r\n$popover-box-shadow:                $box-shadow !default;\r\n\r\n$popover-header-font-size:          $font-size-base !default;\r\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\r\n$popover-header-color:              $headings-color !default;\r\n$popover-header-padding-y:          .5rem !default;\r\n$popover-header-padding-x:          $spacer !default;\r\n\r\n$popover-body-color:                var(--#{$prefix}body-color) !default;\r\n$popover-body-padding-y:            $spacer !default;\r\n$popover-body-padding-x:            $spacer !default;\r\n\r\n$popover-arrow-width:               1rem !default;\r\n$popover-arrow-height:              .5rem !default;\r\n// scss-docs-end popover-variables\r\n\r\n// fusv-disable\r\n// Deprecated in Bootstrap 5.2.0 for CSS variables\r\n$popover-arrow-color:               $popover-bg !default;\r\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\r\n// fusv-enable\r\n\r\n\r\n// Toasts\r\n\r\n// scss-docs-start toast-variables\r\n$toast-max-width:                   350px !default;\r\n$toast-padding-x:                   .75rem !default;\r\n$toast-padding-y:                   .5rem !default;\r\n$toast-font-size:                   .875rem !default;\r\n$toast-color:                       null !default;\r\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\r\n$toast-border-width:                var(--#{$prefix}border-width) !default;\r\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\r\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\r\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\r\n$toast-spacing:                     $container-padding-x !default;\r\n\r\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\r\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\r\n$toast-header-border-color:         $toast-border-color !default;\r\n// scss-docs-end toast-variables\r\n\r\n\r\n// Badges\r\n\r\n// scss-docs-start badge-variables\r\n$badge-font-size:                   .75em !default;\r\n$badge-font-weight:                 $font-weight-bold !default;\r\n$badge-color:                       $white !default;\r\n$badge-padding-y:                   .35em !default;\r\n$badge-padding-x:                   .65em !default;\r\n$badge-border-radius:               $border-radius !default;\r\n// scss-docs-end badge-variables\r\n\r\n\r\n// Modals\r\n\r\n// scss-docs-start modal-variables\r\n$modal-inner-padding:               $spacer !default;\r\n\r\n$modal-footer-margin-between:       .5rem !default;\r\n\r\n$modal-dialog-margin:               .5rem !default;\r\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\r\n\r\n$modal-title-line-height:           $line-height-base !default;\r\n\r\n$modal-content-color:               null !default;\r\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\r\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\r\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\r\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\r\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\r\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\r\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\r\n\r\n$modal-backdrop-bg:                 $black !default;\r\n$modal-backdrop-opacity:            .5 !default;\r\n\r\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\r\n$modal-header-border-width:         $modal-content-border-width !default;\r\n$modal-header-padding-y:            $modal-inner-padding !default;\r\n$modal-header-padding-x:            $modal-inner-padding !default;\r\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n\r\n$modal-footer-bg:                   null !default;\r\n$modal-footer-border-color:         $modal-header-border-color !default;\r\n$modal-footer-border-width:         $modal-header-border-width !default;\r\n\r\n$modal-sm:                          300px !default;\r\n$modal-md:                          500px !default;\r\n$modal-lg:                          800px !default;\r\n$modal-xl:                          1140px !default;\r\n\r\n$modal-fade-transform:              translate(0, -50px) !default;\r\n$modal-show-transform:              none !default;\r\n$modal-transition:                  transform .3s ease-out !default;\r\n$modal-scale-transform:             scale(1.02) !default;\r\n// scss-docs-end modal-variables\r\n\r\n\r\n// Alerts\r\n//\r\n// Define alert colors, border radius, and padding.\r\n\r\n// scss-docs-start alert-variables\r\n$alert-padding-y:               $spacer !default;\r\n$alert-padding-x:               $spacer !default;\r\n$alert-margin-bottom:           1rem !default;\r\n$alert-border-radius:           $border-radius !default;\r\n$alert-link-font-weight:        $font-weight-bold !default;\r\n$alert-border-width:            var(--#{$prefix}border-width) !default;\r\n$alert-bg-scale:                -80% !default;\r\n$alert-border-scale:            -70% !default;\r\n$alert-color-scale:             40% !default;\r\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\r\n// scss-docs-end alert-variables\r\n\r\n// fusv-disable\r\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\r\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\r\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\r\n// fusv-enable\r\n\r\n// Progress bars\r\n\r\n// scss-docs-start progress-variables\r\n$progress-height:                   1rem !default;\r\n$progress-font-size:                $font-size-base * .75 !default;\r\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\r\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\r\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\r\n$progress-bar-color:                $white !default;\r\n$progress-bar-bg:                   $primary !default;\r\n$progress-bar-animation-timing:     1s linear infinite !default;\r\n$progress-bar-transition:           width .6s ease !default;\r\n// scss-docs-end progress-variables\r\n\r\n\r\n// List group\r\n\r\n// scss-docs-start list-group-variables\r\n$list-group-color:                  var(--#{$prefix}body-color) !default;\r\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\r\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\r\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\r\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\r\n\r\n$list-group-item-padding-y:         $spacer * .5 !default;\r\n$list-group-item-padding-x:         $spacer !default;\r\n// fusv-disable\r\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\r\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\r\n// fusv-enable\r\n\r\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\r\n$list-group-active-color:           $component-active-color !default;\r\n$list-group-active-bg:              $component-active-bg !default;\r\n$list-group-active-border-color:    $list-group-active-bg !default;\r\n\r\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\r\n$list-group-disabled-bg:            $list-group-bg !default;\r\n\r\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\r\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\r\n\r\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\r\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\r\n// scss-docs-end list-group-variables\r\n\r\n\r\n// Image thumbnails\r\n\r\n// scss-docs-start thumbnail-variables\r\n$thumbnail-padding:                 .25rem !default;\r\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\r\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\r\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\r\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\r\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\r\n// scss-docs-end thumbnail-variables\r\n\r\n\r\n// Figures\r\n\r\n// scss-docs-start figure-variables\r\n$figure-caption-font-size:          $small-font-size !default;\r\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end figure-variables\r\n\r\n\r\n// Breadcrumbs\r\n\r\n// scss-docs-start breadcrumb-variables\r\n$breadcrumb-font-size:              null !default;\r\n$breadcrumb-padding-y:              0 !default;\r\n$breadcrumb-padding-x:              0 !default;\r\n$breadcrumb-item-padding-x:         .5rem !default;\r\n$breadcrumb-margin-bottom:          1rem !default;\r\n$breadcrumb-bg:                     null !default;\r\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\r\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\r\n$breadcrumb-divider:                quote(\"/\") !default;\r\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\r\n$breadcrumb-border-radius:          null !default;\r\n// scss-docs-end breadcrumb-variables\r\n\r\n// Carousel\r\n\r\n// scss-docs-start carousel-variables\r\n$carousel-control-color:             $white !default;\r\n$carousel-control-width:             15% !default;\r\n$carousel-control-opacity:           .5 !default;\r\n$carousel-control-hover-opacity:     .9 !default;\r\n$carousel-control-transition:        opacity .15s ease !default;\r\n\r\n$carousel-indicator-width:           30px !default;\r\n$carousel-indicator-height:          3px !default;\r\n$carousel-indicator-hit-area-height: 10px !default;\r\n$carousel-indicator-spacer:          3px !default;\r\n$carousel-indicator-opacity:         .5 !default;\r\n$carousel-indicator-active-bg:       $white !default;\r\n$carousel-indicator-active-opacity:  1 !default;\r\n$carousel-indicator-transition:      opacity .6s ease !default;\r\n\r\n$carousel-caption-width:             70% !default;\r\n$carousel-caption-color:             $white !default;\r\n$carousel-caption-padding-y:         1.25rem !default;\r\n$carousel-caption-spacer:            1.25rem !default;\r\n\r\n$carousel-control-icon-width:        2rem !default;\r\n\r\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\r\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n\r\n$carousel-transition-duration:       .6s !default;\r\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\r\n// scss-docs-end carousel-variables\r\n\r\n// scss-docs-start carousel-dark-variables\r\n$carousel-dark-indicator-active-bg:  $black !default;\r\n$carousel-dark-caption-color:        $black !default;\r\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\r\n// scss-docs-end carousel-dark-variables\r\n\r\n\r\n// Spinners\r\n\r\n// scss-docs-start spinner-variables\r\n$spinner-width:           2rem !default;\r\n$spinner-height:          $spinner-width !default;\r\n$spinner-vertical-align:  -.125em !default;\r\n$spinner-border-width:    .25em !default;\r\n$spinner-animation-speed: .75s !default;\r\n\r\n$spinner-width-sm:        1rem !default;\r\n$spinner-height-sm:       $spinner-width-sm !default;\r\n$spinner-border-width-sm: .2em !default;\r\n// scss-docs-end spinner-variables\r\n\r\n\r\n// Close\r\n\r\n// scss-docs-start close-variables\r\n$btn-close-width:            1em !default;\r\n$btn-close-height:           $btn-close-width !default;\r\n$btn-close-padding-x:        .25em !default;\r\n$btn-close-padding-y:        $btn-close-padding-x !default;\r\n$btn-close-color:            $black !default;\r\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\r\n$btn-close-focus-shadow:     $focus-ring-box-shadow !default;\r\n$btn-close-opacity:          .5 !default;\r\n$btn-close-hover-opacity:    .75 !default;\r\n$btn-close-focus-opacity:    1 !default;\r\n$btn-close-disabled-opacity: .25 !default;\r\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\r\n// scss-docs-end close-variables\r\n\r\n\r\n// Offcanvas\r\n\r\n// scss-docs-start offcanvas-variables\r\n$offcanvas-padding-y:               $modal-inner-padding !default;\r\n$offcanvas-padding-x:               $modal-inner-padding !default;\r\n$offcanvas-horizontal-width:        400px !default;\r\n$offcanvas-vertical-height:         30vh !default;\r\n$offcanvas-transition-duration:     .3s !default;\r\n$offcanvas-border-color:            $modal-content-border-color !default;\r\n$offcanvas-border-width:            $modal-content-border-width !default;\r\n$offcanvas-title-line-height:       $modal-title-line-height !default;\r\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\r\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\r\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\r\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\r\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\r\n// scss-docs-end offcanvas-variables\r\n\r\n// Code\r\n\r\n$code-font-size:                    $small-font-size !default;\r\n$code-color:                        $pink !default;\r\n\r\n$kbd-padding-y:                     .1875rem !default;\r\n$kbd-padding-x:                     .375rem !default;\r\n$kbd-font-size:                     $code-font-size !default;\r\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\r\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\r\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\r\n\r\n$pre-color:                         null !default;\r\n\r\n\r\n// Calendar & Date & Time Pickers\r\n\r\n// Calendar\r\n// scss-docs-start calendar-variables\r\n$calendar-table-margin:                     .5rem !default;\r\n$calendar-table-cell-size:                  2.75rem !default;\r\n\r\n$calendar-nav-padding:                      .5rem !default;\r\n$calendar-nav-border-width:                 1px !default;\r\n$calendar-nav-border-color:                 $border-color !default;\r\n$calendar-nav-date-color:                   $body-color !default;\r\n$calendar-nav-date-hover-color:             $primary !default;\r\n$calendar-nav-icon-width:                   1rem !default;\r\n$calendar-nav-icon-height:                  1rem !default;\r\n\r\n$calendar-nav-icon-double-next-color:       $gray-600 !default;\r\n$calendar-nav-icon-double-next:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-next-color}' points='95.314 447.313 72.686 424.687 245.373 252 72.686 79.313 95.314 56.687 290.627 252 95.314 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-next-color}' points='255.314 447.313 232.686 424.687 405.373 252 232.686 79.313 255.314 56.687 450.627 252 255.314 447.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-double-next-hover-color: $body-color !default;\r\n$calendar-nav-icon-double-next-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-next-hover-color}' points='95.314 447.313 72.686 424.687 245.373 252 72.686 79.313 95.314 56.687 290.627 252 95.314 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-next-hover-color}' points='255.314 447.313 232.686 424.687 405.373 252 232.686 79.313 255.314 56.687 450.627 252 255.314 447.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-double-prev-color:       $gray-600 !default;\r\n$calendar-nav-icon-double-prev:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-prev-color}' points='416.686 447.313 221.373 252 416.686 56.687 439.314 79.313 266.627 252 439.314 424.687 416.686 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-prev-color}' points='256.686 447.313 61.373 252 256.686 56.687 279.314 79.313 106.627 252 279.314 424.687 256.686 447.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-double-prev-hover-color: $body-color !default;\r\n$calendar-nav-icon-double-prev-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-prev-hover-color}' points='416.686 447.313 221.373 252 416.686 56.687 439.314 79.313 266.627 252 439.314 424.687 416.686 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-prev-hover-color}' points='256.686 447.313 61.373 252 256.686 56.687 279.314 79.313 106.627 252 279.314 424.687 256.686 447.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-next-color:              $gray-600 !default;\r\n$calendar-nav-icon-next:                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-next-color}' points='179.313 451.313 156.687 428.687 329.372 256 156.687 83.313 179.313 60.687 374.627 256 179.313 451.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-next-hover-color:        $body-color !default;\r\n$calendar-nav-icon-next-hover:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-next-hover-color}' points='179.313 451.313 156.687 428.687 329.372 256 156.687 83.313 179.313 60.687 374.627 256 179.313 451.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-prev-color:              $gray-600 !default;\r\n$calendar-nav-icon-prev:                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-prev-color}' points='324.687 451.313 129.373 256 324.687 60.687 347.313 83.313 174.628 256 347.313 428.687 324.687 451.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-prev-hover-color:        $body-color !default;\r\n$calendar-nav-icon-prev-hover:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-prev-hover-color}' points='324.687 451.313 129.373 256 324.687 60.687 347.313 83.313 174.628 256 347.313 428.687 324.687 451.313'></polygon></svg>\") !default;\r\n\r\n$calendar-cell-header-inner-color:          $text-medium-emphasis !default;\r\n\r\n$calendar-cell-hover-bg:                    $gray-200 !default;\r\n$calendar-cell-disabled-color:              $text-disabled !default;\r\n\r\n$calendar-cell-selected-color:              $white !default;\r\n$calendar-cell-selected-bg:                 $success !default;\r\n\r\n$calendar-cell-range-bg:                    rgba($success, .125) !default;\r\n$calendar-cell-range-hover-bg:              rgba($success, .25) !default;\r\n$calendar-cell-range-hover-border-color:    $success !default;\r\n\r\n$calendar-cell-today-color:                 $danger !default;\r\n// scss-docs-end calendar-variables\r\n\r\n// Picker\r\n$picker-footer-border-width:  1px !default;\r\n$picker-footer-border-color:  $border-color !default;\r\n$picker-footer-padding:       .5rem !default;\r\n\r\n// Date Picker\r\n// scss-docs-start date-picker-variables\r\n$date-picker-default-icon-color:       $gray-600 !default;\r\n$date-picker-default-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$date-picker-default-icon-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$date-picker-default-icon-color}'></rect></svg>\") !default;\r\n$date-picker-invalid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$form-feedback-invalid-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$form-feedback-invalid-color}'></rect></svg>\") !default;\r\n$date-picker-valid-icon:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$form-feedback-valid-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$form-feedback-valid-color}'></rect></svg>\") !default;\r\n\r\n$date-picker-cleaner-icon-color:       $gray-600 !default;\r\n$date-picker-cleaner-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-cleaner-icon-color}' points='306.912 214.461 256 265.373 205.088 214.461 182.461 237.088 233.373 288 182.461 338.912 205.088 361.539 256 310.627 306.912 361.539 329.539 338.912 278.627 288 329.539 237.088 306.912 214.461'></polygon><path fill='#{$date-picker-cleaner-icon-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path></svg>\") !default;\r\n$date-picker-cleaner-icon-hover-color: $body-color !default;\r\n$date-picker-cleaner-icon-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-cleaner-icon-hover-color}' points='306.912 214.461 256 265.373 205.088 214.461 182.461 237.088 233.373 288 182.461 338.912 205.088 361.539 256 310.627 306.912 361.539 329.539 338.912 278.627 288 329.539 237.088 306.912 214.461'></polygon><path fill='#{$date-picker-cleaner-icon-hover-color})' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path></svg>\") !default;\r\n\r\n$date-picker-separator-icon-color:     $gray-600 !default;\r\n$date-picker-separator-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-separator-icon-color}' points='359.873 121.377 337.246 144.004 433.243 240.001 16 240.001 16 240.002 16 272.001 16 272.002 433.24 272.002 337.246 367.996 359.873 390.623 494.498 256 359.873 121.377'></polygon></svg>\") !default;\r\n$date-picker-separator-icon-rtl:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-separator-icon-color}' points='497.333 239.999 80.092 239.999 176.087 144.004 153.46 121.377 18.837 256 153.46 390.623 176.087 367.996 80.09 271.999 497.333 271.999 497.333 239.999'></polygon></svg>\") !default;\r\n\r\n$date-picker-ranges-width:             10rem !default;\r\n$date-picker-ranges-padding:           $spacer * .5 !default;\r\n$date-picker-ranges-border-width:      1px !default;\r\n$date-picker-ranges-border-color:      $border-color !default;\r\n\r\n$date-picker-timepicker-width:         (7 * $calendar-table-cell-size) + (2 * $calendar-table-margin) !default;\r\n$date-picker-timepicker-border-width:  1px !default;\r\n$date-picker-timepicker-border-color:  $border-color !default;\r\n// scss-docs-end date-picker-variables\r\n\r\n// Time Picker\r\n// scss-docs-start time-picker-variables\r\n$time-picker-default-icon-color:       $gray-600 !default;\r\n$time-picker-default-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-default-icon-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$time-picker-default-icon-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-invalid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$form-feedback-invalid-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$form-feedback-invalid-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-valid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$form-feedback-valid-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$form-feedback-valid-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n\r\n$time-picker-cleaner-icon-color:       $gray-600 !default;\r\n$time-picker-cleaner-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-cleaner-icon-color}' points='348.071 141.302 260.308 229.065 172.545 141.302 149.917 163.929 237.681 251.692 149.917 339.456 172.545 362.083 260.308 274.32 348.071 362.083 370.699 339.456 282.935 251.692 370.699 163.929 348.071 141.302' class='ci-primary'></polygon><path fill='#{$time-picker-cleaner-icon-color}' d='M425.706,86.294A240,240,0,0,0,86.294,425.706,240,240,0,0,0,425.706,86.294ZM256,464C141.309,464,48,370.691,48,256S141.309,48,256,48s208,93.309,208,208S370.691,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-cleaner-icon-hover-color: $body-color !default;\r\n$time-picker-cleaner-icon-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-cleaner-icon-hover-color}' points='348.071 141.302 260.308 229.065 172.545 141.302 149.917 163.929 237.681 251.692 149.917 339.456 172.545 362.083 260.308 274.32 348.071 362.083 370.699 339.456 282.935 251.692 370.699 163.929 348.071 141.302' class='ci-primary'></polygon><path fill='#{$time-picker-cleaner-icon-hover-color}' d='M425.706,86.294A240,240,0,0,0,86.294,425.706,240,240,0,0,0,425.706,86.294ZM256,464C141.309,464,48,370.691,48,256S141.309,48,256,48s208,93.309,208,208S370.691,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n\r\n$time-picker-body-padding:             $spacer * .5 !default;\r\n$time-picker-roll-col-border-width:    1px !default;\r\n$time-picker-roll-col-border-color:    $border-color !default;\r\n// scss-docs-end time-picker-variables\r\n", "// stylelint-disable property-disallowed-list\r\n// Single side border-radius\r\n\r\n// Helper function to replace negative values with 0\r\n@function valid-radius($radius) {\r\n  $return: ();\r\n  @each $value in $radius {\r\n    @if type-of($value) == number {\r\n      $return: append($return, max($value, 0));\r\n    } @else {\r\n      $return: append($return, $value);\r\n    }\r\n  }\r\n  @return $return;\r\n}\r\n\r\n// scss-docs-start border-radius-mixins\r\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\r\n  @if $enable-rounded {\r\n    border-radius: valid-radius($radius);\r\n  }\r\n  @else if $fallback-border-radius != false {\r\n    border-radius: $fallback-border-radius;\r\n  }\r\n}\r\n\r\n@mixin border-top-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-top-left-radius: valid-radius($radius);\r\n    border-top-right-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-end-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-top-right-radius: valid-radius($radius);\r\n    border-bottom-right-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-bottom-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-bottom-right-radius: valid-radius($radius);\r\n    border-bottom-left-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-start-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-top-left-radius: valid-radius($radius);\r\n    border-bottom-left-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-top-start-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-top-left-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-top-end-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-top-right-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-bottom-end-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-bottom-right-radius: valid-radius($radius);\r\n  }\r\n}\r\n\r\n@mixin border-bottom-start-radius($radius: $border-radius) {\r\n  @if $enable-rounded {\r\n    border-bottom-left-radius: valid-radius($radius);\r\n  }\r\n}\r\n// scss-docs-end border-radius-mixins\r\n"]}