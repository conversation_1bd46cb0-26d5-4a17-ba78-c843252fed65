export interface Beneficiary {
    id: number;
    name: string;
    area: string;
    createdTime?: string;
	lastUpdatedTime?: string;
}

export const BENEFICIARY_STATUS = {
    ACTIVE: "ACTIVE", // Đang hoạt động
} as const;

export const BENEFICIARY_STATUS_LABEL = {
    [BENEFICIARY_STATUS.ACTIVE]: "beneficiary:status.ACTIVE"
} as const;

export const BENEFICIARY_STATUS_LABEL_COLOR = {
    [BENEFICIARY_STATUS.ACTIVE]: "success",
} as const;

