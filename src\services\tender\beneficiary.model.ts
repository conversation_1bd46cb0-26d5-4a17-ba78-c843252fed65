export interface Beneficiary {
	id: number;
	name: string;
	area: string;
	createdTime?: string;
	lastUpdatedTime?: string;
}

export const BENEFICIARY_STATUS = {
	ACTIVE: "ACTIVE", // Đang hoạt động
	INACTIVE: "INACTIVE", // Không hoạt động
} as const;

export const BENEFICIARY_STATUS_LABEL = {
	[BENEFICIARY_STATUS.ACTIVE]: "beneficiary:status.ACTIVE",
	[BENEFICIARY_STATUS.INACTIVE]: "beneficiary:status.INACTIVE",
} as const;

export const BENEFICIARY_STATUS_LABEL_COLOR = {
	[BENEFICIARY_STATUS.ACTIVE]: "success",
	[BENEFICIARY_STATUS.INACTIVE]: "danger",
} as const;
