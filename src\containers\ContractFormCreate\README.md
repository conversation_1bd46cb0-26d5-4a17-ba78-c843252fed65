# ContractFormCreate - Auto-populate Beneficiary Data

## Overview

The `ContractFormCreate` component has been enhanced to automatically fetch and populate beneficiary data when in edit mode.

## How it works

### 1. **Automatic Data Fetching**
When the component is in edit mode (has a `contract` prop) and there's a `beneficiaryName` in the form data, the component automatically:

- Calls `handleFetchData` with the beneficiary name
- Fetches matching beneficiary records from the API
- Auto-populates related form fields

### 2. **Enhanced handleFetchData Function**
The `handleFetchData` function now accepts both `search` and `beneficiaryName` parameters:

```javascript
const handleFetchData = async (params = {}) => {
    const { search, beneficiaryName } = params;
    const searchTerm = search || beneficiaryName;
    
    if (!searchTerm) {
        return [];
    }

    const res = await getBeneficiaryList({
        search: searchTerm,
        limit: DEFAULT_LIMIT,
        offset: 0,
    });

    if (res.status !== API_STATUS.OK) {
        console.error("[Error] fetch beneficiaryOptions", res);
        return [];
    }
    return res.data;
};
```

### 3. **Reactive Auto-population**
Using SolidJS's `createEffect`, the component watches for:

- Edit mode state (`props.contract` exists)
- Beneficiary name in form data
- Whether beneficiary data has already been fetched

When these conditions are met, it automatically:

1. Fetches beneficiary data using the beneficiary name
2. Populates the following form fields:
   - `phoneNumber`
   - `taxCode` 
   - `address`
   - `email`
   - `budgetUnitCode`

### 4. **State Management**
- Uses `createSignal` to track `beneficiaryData` state
- Prevents duplicate API calls by checking if data is already fetched
- Logs successful auto-population for debugging

## Usage

### Edit Mode (Automatic)
When editing an existing contract, the component automatically detects the beneficiary name and fetches related data:

```jsx
<ContractFormCreate 
    hookForm={hookForm} 
    annexes={annexList}
    contract={contractInfo}  // Edit mode - triggers auto-population
/>
```

### Create Mode (Manual)
In create mode, users manually search and select beneficiaries through the AutoCompleteTender component:

```jsx
<ContractFormCreate 
    hookForm={hookForm} 
    // No contract prop - create mode
/>
```

## Benefits

1. **Improved UX**: Users don't need to manually re-enter beneficiary details when editing contracts
2. **Data Consistency**: Ensures beneficiary information is accurate and up-to-date
3. **Time Saving**: Reduces manual data entry in edit scenarios
4. **Error Reduction**: Minimizes human errors in data entry

## Technical Details

- **Reactive**: Uses SolidJS's `createEffect` for automatic triggering
- **Async Safe**: Properly handles async operations within reactive contexts
- **Error Handling**: Includes try-catch blocks and error logging
- **Performance**: Only fetches data when necessary (edit mode + beneficiary name present)
- **Non-blocking**: Doesn't interfere with existing form functionality

## Debugging

The component logs successful auto-population:
```
console.log("Auto-populated beneficiary data:", beneficiary);
```

And errors during the process:
```
console.error("[Error] Auto-populating beneficiary data:", error);
```
