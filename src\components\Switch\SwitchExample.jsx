import { createSignal } from "solid-js";
import { Card, CardBody, Col, Row } from "@buymed/solidjs-component/components";
import Switch from "./index";

/**
 * Example usage of the Switch component
 * This file demonstrates various configurations and use cases
 */
export function SwitchExample() {
	const [basicSwitch, setBasicSwitch] = createSignal(false);
	console.log(basicSwitch());
	const [notificationSwitch, setNotificationSwitch] = createSignal(true);
	const [privacySwitch, setPrivacySwitch] = createSignal(false);
	const [formData, setFormData] = createSignal({
		emailNotifications: true,
		smsNotifications: false,
		pushNotifications: true,
		darkMode: false,
		autoSave: true,
	});

	function handleFormChange(field, value) {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	}

	return (
		<div class="p-4">
			<h2 class="mb-4">Switch Component Examples</h2>

			{/* Basic Examples */}
			<Card class="mb-4">
				<CardBody>
					<h5 class="mb-3">Basic Switch Examples</h5>
					<Row class="g-4">
						<Col md={6}>
							<h6>Default Switch (Row Layout)</h6>
							<Switch
								label="Enable notifications"
								checked={basicSwitch()}
								onChange={(e, value) => {
									setBasicSwitch(value);
									console.log(value);
								}}
							/>
						</Col>

						<Col md={6}>
							<h6>Switch with Description</h6>
							<Switch
								label="Privacy Mode"
								description="Hide your online status from other users"
								checked={privacySwitch()}
								onChange={(e, value) => setPrivacySwitch(value)}
								color="success"
							/>
						</Col>
					</Row>
				</CardBody>
			</Card>

			{/* Layout Variations */}
			<Card class="mb-4">
				<CardBody>
					<h5 class="mb-3">Layout Variations</h5>
					<Row class="g-4">
						<Col md={3}>
							<h6>Row - Label Right (Default)</h6>
							<Switch
								label="Auto-save"
								direction="row"
								labelPosition="right"
								defaultChecked={true}
							/>
						</Col>

						<Col md={3}>
							<h6>Row - Label Left</h6>
							<Switch
								label="Dark Mode"
								direction="row"
								labelPosition="left"
								color="warning"
							/>
						</Col>

						<Col md={3}>
							<h6>Column - Label Top</h6>
							<Switch
								label="Sync Data"
								direction="column"
								labelPosition="top"
								color="info"
							/>
						</Col>

						<Col md={3}>
							<h6>Column - Label Bottom</h6>
							<Switch
								label="Backup"
								direction="column"
								labelPosition="bottom"
								color="danger"
							/>
						</Col>
					</Row>
				</CardBody>
			</Card>

			{/* Size Variations */}
			<Card class="mb-4">
				<CardBody>
					<h5 class="mb-3">Size Variations</h5>
					<Row class="g-4">
						<Col md={4}>
							<h6>Small Size</h6>
							<Switch label="Small switch" size="sm" defaultChecked={true} />
						</Col>

						<Col md={4}>
							<h6>Medium Size (Default)</h6>
							<Switch label="Medium switch" size="md" defaultChecked={true} />
						</Col>

						<Col md={4}>
							<h6>Large Size</h6>
							<Switch label="Large switch" size="lg" defaultChecked={true} />
						</Col>
					</Row>
				</CardBody>
			</Card>

			{/* Color Variations */}
			<Card class="mb-4">
				<CardBody>
					<h5 class="mb-3">Color Variations</h5>
					<Row class="g-4">
						<Col md={6}>
							<div class="d-flex flex-column gap-3">
								<Switch
									label="Primary (Default)"
									color="primary"
									defaultChecked={true}
								/>
								<Switch label="Success" color="success" defaultChecked={true} />
								<Switch label="Warning" color="warning" defaultChecked={true} />
							</div>
						</Col>

						<Col md={6}>
							<div class="d-flex flex-column gap-3">
								<Switch label="Danger" color="danger" defaultChecked={true} />
								<Switch label="Info" color="info" defaultChecked={true} />
								<Switch label="Disabled" disabled={true} defaultChecked={true} />
							</div>
						</Col>
					</Row>
				</CardBody>
			</Card>

			{/* Form Integration Example */}
			<Card class="mb-4">
				<CardBody>
					<h5 class="mb-3">Form Integration Example</h5>
					<form>
						<Row class="g-3">
							<Col md={6}>
								<Switch
									name="emailNotifications"
									label="Email Notifications"
									description="Receive updates via email"
									checked={formData().emailNotifications}
									onChange={(e, value) =>
										handleFormChange("emailNotifications", value)
									}
									required
								/>
							</Col>

							<Col md={6}>
								<Switch
									name="smsNotifications"
									label="SMS Notifications"
									description="Receive updates via SMS"
									checked={formData().smsNotifications}
									onChange={(e, value) =>
										handleFormChange("smsNotifications", value)
									}
									color="success"
								/>
							</Col>

							<Col md={6}>
								<Switch
									name="pushNotifications"
									label="Push Notifications"
									description="Receive push notifications on your device"
									checked={formData().pushNotifications}
									onChange={(e, value) =>
										handleFormChange("pushNotifications", value)
									}
									color="info"
								/>
							</Col>

							<Col md={6}>
								<Switch
									name="autoSave"
									label="Auto Save"
									description="Automatically save your work"
									checked={formData().autoSave}
									onChange={(e, value) => handleFormChange("autoSave", value)}
									color="warning"
								/>
							</Col>
						</Row>

						<div class="mt-4">
							<h6>Form Data:</h6>
							<pre class="bg-light p-3 rounded">
								{JSON.stringify(formData(), null, 2)}
							</pre>
						</div>
					</form>
				</CardBody>
			</Card>

			{/* Error State Example */}
			<Card>
				<CardBody>
					<h5 class="mb-3">Error State Example</h5>
					<Switch
						label="Terms and Conditions"
						description="You must agree to the terms and conditions"
						required
						invalid={true}
						feedbackInvalid="Please accept the terms and conditions to continue"
						color="danger"
					/>
				</CardBody>
			</Card>
		</div>
	);
}

export default SwitchExample;
