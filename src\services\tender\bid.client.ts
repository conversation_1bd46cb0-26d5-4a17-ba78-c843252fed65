import { APIResponse, QueryInput } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { ResetPasswordInfo } from "../iam/iam.model";
import { QueryOption, Bid, BidQuery } from "./bid.model";
import { Lot, LotQuery } from "./lot.model";

const URL = "/tender/core-tender/v1";

/** Return a list of bidding packages  */
export async function getBidList(
  input?: QueryInput<BidQuery, QueryOption>
): Promise<APIResponse<Bid>> {
  return callAPI(HTTP_METHOD.QUERY, `${URL}/bid/list`, {
    ...input,
    search: input.search ? String(input.search) : undefined,
  });
}

export async function getBid({
  bidID,
  option,
}: {
  bidID?: number;
  option?: QueryOption;
}): Promise<APIResponse<Bid>> {
  return callAPI(HTTP_METHOD.GET, `${URL}/bid/info`, {
    bidID,
    option: option ? Object.keys(option).join(",") : undefined,
  });
}

export async function getBidLotList(
  input?: QueryInput<LotQuery, QueryOption>
): Promise<APIResponse<Lot>> {
  return callAPI(HTTP_METHOD.QUERY, `${URL}/lot/list`, {
    ...input,
    search: input.search ? String(input.search) : undefined,
  });
}

/** Create bid */
export async function createBid(data: Bid): Promise<APIResponse<Bid>> {
  return callAPI(HTTP_METHOD.POST, `${URL}/bid`, data);
}

/** Update bid information */
export async function updateBid(data: Bid): Promise<APIResponse<Bid>> {
  return callAPI(HTTP_METHOD.PUT, `${URL}/bid/info`, data);
}
