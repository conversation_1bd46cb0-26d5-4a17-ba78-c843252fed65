import {
	<PERSON><PERSON>,
	<PERSON>,
	DEFAULT_LIMIT,
	DEFAULT_PAGE,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
	Tooltip,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { createEffect, createSignal, Index } from "solid-js";
import { useSearchParams } from "solid-start";
import BMTablePagination from "~/components/table/BMTablePagination";
import {
	BENEFICIARY_STATUS_LABEL,
	BENEFICIARY_STATUS_LABEL_COLOR,
} from "~/services/tender/beneficiary.model";
import EditIcon from "~icons/mdi/square-edit-outline";

export function BeneficiaryTable(props) {
	const { t } = useTranslate();
	const [searchParams] = useSearchParams();
	const [page, setPage] = createSignal(+searchParams.page || DEFAULT_PAGE);
	const [limit, setLimit] = createSignal(+searchParams.limit || DEFAULT_LIMIT);
	createEffect(() => {
		setPage(+searchParams.page || 1);
		setLimit(+searchParams.limit || 10);
	});

	return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>#</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:no`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:name`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:phone_number`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:email`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:tax_code`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:address`}</TableHeaderCell>
						<TableHeaderCell class="col-2">
							<div class="d-flex justify-content-center align-items-center gap-1">
								{t`beneficiary:beneficiary_status`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.beneficiaries}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`beneficiary:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(beneficiary, index) => (
							<BeneficiaryTableRow
								item={beneficiary()}
								index={index + 1 + (page() - 1) * limit()}
							/>
						)}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}

function BeneficiaryTableRow(props) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.index}</TableCell>
			<TableCell>{props.item.beneficiaryID}</TableCell>
			<TableCell>{props.item.name}</TableCell>
			<TableCell>{props.item.phoneNumber}</TableCell>
			<TableCell>{props.item.email}</TableCell>
			<TableCell>{props.item.taxCode}</TableCell>
			<TableCell>{props.item.address}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Button
						color={BENEFICIARY_STATUS_LABEL_COLOR[props.item.status]}
						class="m-3 "
						style={{
							"pointer-events": "none",
						}}
					>
						{t(BENEFICIARY_STATUS_LABEL[props.item.status])}
					</Button>
				</div>
			</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Tooltip content={"Xem thông tin khách hàng"}>
						<Button
							class="p-2"
							variant="outline"
							color="primary"
							href={`/beneficiary/${props.item.code}/info`}
							startIcon={<EditIcon />}
						/>
					</Tooltip>
				</div>
			</TableCell>
		</TableRow>
	);
}
