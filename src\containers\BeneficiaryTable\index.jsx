import {
	<PERSON><PERSON>,
	Card,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeaderCell,
	TableRow,
	Tooltip,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { formatDatetime, formatNumber } from "@buymed/solidjs-component/utils";
import { Index, Show } from "solid-js";
import { A } from "solid-start";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import BMTablePagination from "~/components/table/BMTablePagination";
import { BENEFICIARY_STATUS_LABEL, BENEFICIARY_STATUS_LABEL_COLOR } from "~/services/tender/beneficiary.model";
import { BID_STATUS_LABEL_COLOR } from "~/services/tender/bid.model";
import { CONTRACT_STATUS_LABEL, CONTRACT_STATUS_LABEL_COLOR } from "~/services/tender/contract.model";
import EditIcon from "~icons/mdi/square-edit-outline";
import MdiStickerTextOutline from '~icons/mdi/sticker-text-outline';


export function BeneficiaryTable(props) {
	const { t } = useTranslate();

	return (
		<Card>
			<Table responsive hover>
				<TableHead>
					<TableRow>
						<TableHeaderCell>{t`beneficiary:no`}</TableHeaderCell>
                        <TableHeaderCell>{t`beneficiary:name`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:phone_number`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:email`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:tax_code`}</TableHeaderCell>
						<TableHeaderCell>{t`beneficiary:address`}</TableHeaderCell>
						<TableHeaderCell class="col-2">
							<div class="d-flex justify-content-center align-items-center gap-1">
								{t`beneficiary:beneficiary_status`}
							</div>
						</TableHeaderCell>
						<TableHeaderCell class="text-center">{t`common:action`}</TableHeaderCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<Index
						each={props.beneficiaries}
						fallback={
							<TableRow>
								<TableCell colSpan={100} style={{ "text-align": "center" }}>
									{t`beneficiary:not_found`}
								</TableCell>
							</TableRow>
						}
					>
						{(beneficiary) => <BeneficiaryTableRow item={beneficiary()}/>}
					</Index>
				</TableBody>
			</Table>
			<BMTablePagination total={props.total} />
		</Card>
	);
}


function BeneficiaryTableRow(props) {
	const { t } = useTranslate();

	return (
		<TableRow>
			<TableCell>{props.item.beneficiaryID}</TableCell>
			<TableCell>{props.item.name}</TableCell>
			<TableCell>{props.item.phoneNumber}</TableCell>
			<TableCell>{props.item.email}</TableCell>
			<TableCell>{props.item.taxCode}</TableCell>
			<TableCell>{props.item.address}</TableCell>
			<TableCell>
				<div class="d-flex justify-content-center align-items-center gap-1">
					<Button color={BENEFICIARY_STATUS_LABEL_COLOR[props.item.status]} class="m-3">
						{t(BENEFICIARY_STATUS_LABEL[props.item.status])}
					</Button>
				</div>
			</TableCell>
			<TableCell>
				
			</TableCell>
		</TableRow>
	);
}
