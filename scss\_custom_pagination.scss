// $pagination-padding-y-sm-custom:  .72rem !default;


// .pagination>li>a,
// .pagination>li>span {
//     border-radius: 50% !important;
//     margin: 0 5px;
//     border: none;
// }


// // .pagination>li>a:hover {
// //     background-color: rgba(0, 0, 0, 0.04)
// // }

// .pagination-sm {
//     @include pagination-size($pagination-padding-y-sm, $pagination-padding-y-sm-custom, $font-size-sm, $pagination-border-radius-sm);
// }

.buymed-pagination{
        .page-item{
            .page-link{
                border: none;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 100%;
                background-color: unset;
                color: #000;
                &:hover{
                    background-color: rgba(0, 0, 0, 0.08);
                }
            }
            &.active{
                .page-link{
                    background-color: rgba(0, 0, 0, 0.08);
                }
            }
            &.disabled{
                .page-link{
                    background-color: unset;
                    opacity: 0.38;
                }
            }
        }
}