import { createSignal } from "solid-js";
import Switch from "./index";

/**
 * Simple demo to test Switch component functionality
 */
export function SwitchDemo() {
    const [basicSwitch, setBasicSwitch] = createSignal(false);
    const [notificationSwitch, setNotificationSwitch] = createSignal(true);

    return (
        <div class="p-4">
            <h2 class="mb-4">Switch Component Demo</h2>
            
            {/* Basic Switch */}
            <div class="mb-4">
                <h5>Basic Switch</h5>
                <Switch
                    label="Enable notifications"
                    checked={basicSwitch()}
                    onChange={(event, value) => {
                        console.log('Basic switch changed:', value);
                        setBasicSwitch(value);
                    }}
                />
                <p>Current value: {basicSwitch() ? 'ON' : 'OFF'}</p>
            </div>

            {/* Switch with Description */}
            <div class="mb-4">
                <h5>Switch with Description</h5>
                <Switch
                    label="Email Notifications"
                    description="Receive email updates about your account"
                    checked={notificationSwitch()}
                    onChange={(event, value) => {
                        console.log('Notification switch changed:', value);
                        setNotificationSwitch(value);
                    }}
                    color="success"
                />
            </div>

            {/* Required Switch with Error */}
            <div class="mb-4">
                <h5>Required Switch with Error</h5>
                <Switch
                    label="Terms and Conditions"
                    description="You must agree to the terms and conditions"
                    required={true}
                    invalid={true}
                    feedbackInvalid="Please accept the terms and conditions to continue"
                    color="danger"
                />
            </div>

            {/* Different Layouts */}
            <div class="mb-4">
                <h5>Different Layouts</h5>
                
                <div class="mb-3">
                    <h6>Row - Label Right (Default)</h6>
                    <Switch
                        label="Auto-save"
                        direction="row"
                        labelPosition="right"
                        defaultChecked={true}
                    />
                </div>
                
                <div class="mb-3">
                    <h6>Row - Label Left</h6>
                    <Switch
                        label="Dark Mode"
                        direction="row"
                        labelPosition="left"
                        color="warning"
                    />
                </div>
                
                <div class="mb-3">
                    <h6>Column - Label Top</h6>
                    <Switch
                        label="Sync Data"
                        direction="column"
                        labelPosition="top"
                        color="info"
                    />
                </div>
                
                <div class="mb-3">
                    <h6>Column - Label Bottom</h6>
                    <Switch
                        label="Backup"
                        direction="column"
                        labelPosition="bottom"
                        color="danger"
                    />
                </div>
            </div>

            {/* Different Sizes */}
            <div class="mb-4">
                <h5>Different Sizes</h5>
                
                <div class="d-flex gap-4 align-items-center">
                    <Switch
                        label="Small"
                        size="sm"
                        defaultChecked={true}
                    />
                    
                    <Switch
                        label="Medium"
                        size="md"
                        defaultChecked={true}
                    />
                    
                    <Switch
                        label="Large"
                        size="lg"
                        defaultChecked={true}
                    />
                </div>
            </div>

            {/* Disabled State */}
            <div class="mb-4">
                <h5>Disabled State</h5>
                <Switch
                    label="Disabled Switch"
                    disabled={true}
                    defaultChecked={true}
                />
            </div>

            {/* onChange Test */}
            <div class="mb-4">
                <h5>onChange Event Test</h5>
                <Switch
                    label="Test onChange"
                    description="Check console for onChange events"
                    onChange={(event, checked) => {
                        console.log('Switch changed:', { event, checked });
                        alert(`Switch is now: ${checked ? 'ON' : 'OFF'}`);
                    }}
                    color="info"
                />
            </div>
        </div>
    );
}

export default SwitchDemo;
