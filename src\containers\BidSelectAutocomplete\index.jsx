import { FormAutocomplete, useTranslate } from "@buymed/solidjs-component/components";
import { API_STATUS, debounce, sortBy } from "@buymed/solidjs-component/utils";
import { createResource, createSignal, splitProps } from "solid-js";
import { getBidList } from "~/services/tender/bid.client";

export function parseBidOption(bid) {
  return {
    value: "" + bid.bidID,
    label: `[${bid.itb}] - ${bid.bidName}`,
    data: {
      id: bid.bidID,
      code: bid.code,
      name: bid.bidName,
      contractNo: bid.contractNo,
      procuringEntity: bid.procuringEntity,
    },
  };
}

export function BidSelectAutocomplete(props) {
  const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);

  const { t } = useTranslate();
  const [search, setSearch] = createSignal(local.defaultValue || "");
  const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);

  const q = {};
  if (props.status) {
    q.status = props.status;
  }

  const [bidOptions] = createResource(
    search,
    async (search) => {
      console.log(search, "search");
      const res = await getBidList({
        q,
        search,
        offset: 0,
        limit: 500,
      });

      if (res.status !== API_STATUS.OK) {
        console.error("[Error] fetch bidOptions", res);
        return [];
      }

      let options = res.data.map((bid) => parseBidOption(bid));

      if (search === "" && lastOption()) {
        var exist = false;
        for (var i = 0; i < options.length; i++) {
          if (options[i].value === lastOption().value) {
            exist = true;
            break;
          }
        }
        if (!exist) {
          options.push(lastOption());
        }
      }

      options = sortBy(options, "label");
      console.log(options, "options");
      return options;
    },
    { initialValue: lastOption() ? [lastOption()] : [] }
  );

  function onInputChange(e) {
    setSearch(e.target.value);
  }
  const debouncedOnInputChange = debounce(onInputChange, 500);

  return (
    <FormAutocomplete
      name={local.name}
      options={bidOptions()}
      label={t`common:bid`}
      placeholder={t`common:bid_search`}
      onInputChange={debouncedOnInputChange}
      isLoading={bidOptions.loading}
      // renderOption={(props, { data }) => (
      // 	<li {...props}>
      // 		[{data.id}] - <b>{data.name}</b>
      // 	</li>
      // )}
      onChange={(e) => {
        setLastOption(e);
      }}
      {...other}
    />
  );
}
