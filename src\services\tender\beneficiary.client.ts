import { APIResponse, MasterDataOption, QueryInput } from "@buymed/solidjs-component/services";
import { HTTP_METHOD } from "@buymed/solidjs-component/utils";
import { callAPI } from "../callAPI";
import { ResetPasswordInfo } from "../iam/iam.model";
import { QueryOption, Bid, BidQuery } from "./bid.model";
import { Lot, LotQuery } from "./lot.model";
import { Contract } from "./contract.model";

const URL = "/tender/core-tender/v1";

/** Return a list of beneficiary  */
export async function getBeneficiaryList(
	input?: QueryInput<BeneficiaryQuery, QueryOption>
): Promise<APIResponse<any>> {
	return callAPI(HTTP_METHOD.QUERY, `${URL}/beneficiary/list`, {
		...input,
		search: input.search ? String(input.search) : undefined,
	});
}

// ===================================================
// For QUERY
// ===================================================
export type BeneficiaryQuery = {};

export type BeneficiaryQueryOption = MasterDataOption & {};