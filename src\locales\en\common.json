{"button": {"applyButton": "Apply", "refreshButton": "Refresh", "confirm": "Confirm", "cancel": "Cancel", "exportExcel": "Export Excel", "createNew": "Create", "save": "Save", "import": "Import", "clearFilter": "Clear Filter", "edit": "Edit", "add": "Add", "back": "Back", "reset": "Reset", "chooseFile": "Choose <PERSON>", "filter": "Filter", "importExcel": "Import from Excel file", "saveAndContinue": "Save and Continue", "close": "Close"}, "status": {"ACTIVE": "Active", "INACTIVE": "Inactive", "new": "New", "closed": "Closed"}, "notify": {"notification": "Notification", "update_success": "Updated Successfully", "update_fail": "Updated failed", "action_success": "Action successful", "create_success": "Created Successfully", "create_fail": "Created failed", "delete_success": "Deleted Successfully", "success": "Success", "action_fail": "Action failed: {{error}}", "copy_success": "Copy successfully", "notify": "Notification"}, "sidebar": {"profile": "My profile", "user": "User", "bid": "Bid"}, "breadcrumb": {"home": "Home", "user": "User", "add_user": "Add user", "edit_user": "Edit user", "history_user": "View user's history", "bid": "Bid", "add_bid": "Add bid", "edit_bid": "Edit bid"}, "table": {"labelRowsPerPage": "Item per page", "labelDisplayedRows": "<b>{{from}}</b>-<b>{{to}}</b> of <b>{{total}}</b>"}, "tooltip": {"edit": "Edit"}, "action": "Action", "logout": "Logout", "title": "BuyMed", "user": "User", "from_date": "From date", "to_date": "To date", "confirm_unsaved": "You have unsaved changes. Are you sure you want to leave?", "attachments": "<PERSON><PERSON><PERSON> li<PERSON> đ<PERSON>", "drag_and_drop": "Chọn hoặc kéo thả "}