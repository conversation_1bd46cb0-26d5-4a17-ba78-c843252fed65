{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_containers.scss", "../../scss/mixins/_container.scss", "bootstrap-grid.css", "../../scss/mixins/_breakpoints.scss", "../../scss/_variables.scss", "../../scss/_grid.scss", "../../scss/mixins/_grid.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AACE;;;;EAAA;ACKA;;;;;;;ECHA,qBAAA;EACA,gBAAA;EACA,WAAA;EACA,6CAAA;EACA,4CAAA;EACA,kBAAA;EACA,iBAAA;ACUF;;AC4CI;EH5CE;IACE,gBIgfe;EF5erB;AACF;ACsCI;EH5CE;IACE,gBIgfe;EFverB;AACF;ACiCI;EH5CE;IACE,gBIgfe;EFlerB;AACF;AC4BI;EH5CE;IACE,iBIgfe;EF7drB;AACF;ACuBI;EH5CE;IACE,iBIgfe;EFxdrB;AACF;AGzCA;EAEI,qBAAA;EAAA,yBAAA;EAAA,yBAAA;EAAA,yBAAA;EAAA,0BAAA;EAAA,2BAAA;AH+CJ;;AG1CE;ECNA,qBAAA;EACA,gBAAA;EACA,aAAA;EACA,eAAA;EAEA,yCAAA;EACA,6CAAA;EACA,4CAAA;AJmDF;AGjDI;ECGF,sBAAA;EAIA,cAAA;EACA,WAAA;EACA,eAAA;EACA,6CAAA;EACA,4CAAA;EACA,8BAAA;AJ8CF;;AICM;EACE,YAAA;AJER;;AICM;EApCJ,cAAA;EACA,WAAA;AJuCF;;AIzBE;EACE,cAAA;EACA,WAAA;AJ4BJ;;AI9BE;EACE,cAAA;EACA,UAAA;AJiCJ;;AInCE;EACE,cAAA;EACA,qBAAA;AJsCJ;;AIxCE;EACE,cAAA;EACA,UAAA;AJ2CJ;;AI7CE;EACE,cAAA;EACA,UAAA;AJgDJ;;AIlDE;EACE,cAAA;EACA,qBAAA;AJqDJ;;AItBM;EAhDJ,cAAA;EACA,WAAA;AJ0EF;;AIrBU;EAhEN,cAAA;EACA,kBAAA;AJyFJ;;AI1BU;EAhEN,cAAA;EACA,mBAAA;AJ8FJ;;AI/BU;EAhEN,cAAA;EACA,UAAA;AJmGJ;;AIpCU;EAhEN,cAAA;EACA,mBAAA;AJwGJ;;AIzCU;EAhEN,cAAA;EACA,mBAAA;AJ6GJ;;AI9CU;EAhEN,cAAA;EACA,UAAA;AJkHJ;;AInDU;EAhEN,cAAA;EACA,mBAAA;AJuHJ;;AIxDU;EAhEN,cAAA;EACA,mBAAA;AJ4HJ;;AI7DU;EAhEN,cAAA;EACA,UAAA;AJiIJ;;AIlEU;EAhEN,cAAA;EACA,mBAAA;AJsIJ;;AIvEU;EAhEN,cAAA;EACA,mBAAA;AJ2IJ;;AI5EU;EAhEN,cAAA;EACA,WAAA;AJgJJ;;AIzEY;EAxDV,wBAAA;AJqIF;;AI7EY;EAxDV,yBAAA;AJyIF;;AIjFY;EAxDV,gBAAA;AJ6IF;;AIrFY;EAxDV,yBAAA;AJiJF;;AIzFY;EAxDV,yBAAA;AJqJF;;AI7FY;EAxDV,gBAAA;AJyJF;;AIjGY;EAxDV,yBAAA;AJ6JF;;AIrGY;EAxDV,yBAAA;AJiKF;;AIzGY;EAxDV,gBAAA;AJqKF;;AI7GY;EAxDV,yBAAA;AJyKF;;AIjHY;EAxDV,yBAAA;AJ6KF;;AI1GQ;;EAEE,gBAAA;AJ6GV;;AI1GQ;;EAEE,gBAAA;AJ6GV;;AIpHQ;;EAEE,sBAAA;AJuHV;;AIpHQ;;EAEE,sBAAA;AJuHV;;AI9HQ;;EAEE,qBAAA;AJiIV;;AI9HQ;;EAEE,qBAAA;AJiIV;;AIxIQ;;EAEE,mBAAA;AJ2IV;;AIxIQ;;EAEE,mBAAA;AJ2IV;;AIlJQ;;EAEE,qBAAA;AJqJV;;AIlJQ;;EAEE,qBAAA;AJqJV;;AI5JQ;;EAEE,mBAAA;AJ+JV;;AI5JQ;;EAEE,mBAAA;AJ+JV;;ACzNI;EGUE;IACE,YAAA;EJmNN;EIhNI;IApCJ,cAAA;IACA,WAAA;EJuPA;EIzOA;IACE,cAAA;IACA,WAAA;EJ2OF;EI7OA;IACE,cAAA;IACA,UAAA;EJ+OF;EIjPA;IACE,cAAA;IACA,qBAAA;EJmPF;EIrPA;IACE,cAAA;IACA,UAAA;EJuPF;EIzPA;IACE,cAAA;IACA,UAAA;EJ2PF;EI7PA;IACE,cAAA;IACA,qBAAA;EJ+PF;EIhOI;IAhDJ,cAAA;IACA,WAAA;EJmRA;EI9NQ;IAhEN,cAAA;IACA,kBAAA;EJiSF;EIlOQ;IAhEN,cAAA;IACA,mBAAA;EJqSF;EItOQ;IAhEN,cAAA;IACA,UAAA;EJySF;EI1OQ;IAhEN,cAAA;IACA,mBAAA;EJ6SF;EI9OQ;IAhEN,cAAA;IACA,mBAAA;EJiTF;EIlPQ;IAhEN,cAAA;IACA,UAAA;EJqTF;EItPQ;IAhEN,cAAA;IACA,mBAAA;EJyTF;EI1PQ;IAhEN,cAAA;IACA,mBAAA;EJ6TF;EI9PQ;IAhEN,cAAA;IACA,UAAA;EJiUF;EIlQQ;IAhEN,cAAA;IACA,mBAAA;EJqUF;EItQQ;IAhEN,cAAA;IACA,mBAAA;EJyUF;EI1QQ;IAhEN,cAAA;IACA,WAAA;EJ6UF;EItQU;IAxDV,cAAA;EJiUA;EIzQU;IAxDV,wBAAA;EJoUA;EI5QU;IAxDV,yBAAA;EJuUA;EI/QU;IAxDV,gBAAA;EJ0UA;EIlRU;IAxDV,yBAAA;EJ6UA;EIrRU;IAxDV,yBAAA;EJgVA;EIxRU;IAxDV,gBAAA;EJmVA;EI3RU;IAxDV,yBAAA;EJsVA;EI9RU;IAxDV,yBAAA;EJyVA;EIjSU;IAxDV,gBAAA;EJ4VA;EIpSU;IAxDV,yBAAA;EJ+VA;EIvSU;IAxDV,yBAAA;EJkWA;EI/RM;;IAEE,gBAAA;EJiSR;EI9RM;;IAEE,gBAAA;EJgSR;EIvSM;;IAEE,sBAAA;EJySR;EItSM;;IAEE,sBAAA;EJwSR;EI/SM;;IAEE,qBAAA;EJiTR;EI9SM;;IAEE,qBAAA;EJgTR;EIvTM;;IAEE,mBAAA;EJyTR;EItTM;;IAEE,mBAAA;EJwTR;EI/TM;;IAEE,qBAAA;EJiUR;EI9TM;;IAEE,qBAAA;EJgUR;EIvUM;;IAEE,mBAAA;EJyUR;EItUM;;IAEE,mBAAA;EJwUR;AACF;ACnYI;EGUE;IACE,YAAA;EJ4XN;EIzXI;IApCJ,cAAA;IACA,WAAA;EJgaA;EIlZA;IACE,cAAA;IACA,WAAA;EJoZF;EItZA;IACE,cAAA;IACA,UAAA;EJwZF;EI1ZA;IACE,cAAA;IACA,qBAAA;EJ4ZF;EI9ZA;IACE,cAAA;IACA,UAAA;EJgaF;EIlaA;IACE,cAAA;IACA,UAAA;EJoaF;EItaA;IACE,cAAA;IACA,qBAAA;EJwaF;EIzYI;IAhDJ,cAAA;IACA,WAAA;EJ4bA;EIvYQ;IAhEN,cAAA;IACA,kBAAA;EJ0cF;EI3YQ;IAhEN,cAAA;IACA,mBAAA;EJ8cF;EI/YQ;IAhEN,cAAA;IACA,UAAA;EJkdF;EInZQ;IAhEN,cAAA;IACA,mBAAA;EJsdF;EIvZQ;IAhEN,cAAA;IACA,mBAAA;EJ0dF;EI3ZQ;IAhEN,cAAA;IACA,UAAA;EJ8dF;EI/ZQ;IAhEN,cAAA;IACA,mBAAA;EJkeF;EInaQ;IAhEN,cAAA;IACA,mBAAA;EJseF;EIvaQ;IAhEN,cAAA;IACA,UAAA;EJ0eF;EI3aQ;IAhEN,cAAA;IACA,mBAAA;EJ8eF;EI/aQ;IAhEN,cAAA;IACA,mBAAA;EJkfF;EInbQ;IAhEN,cAAA;IACA,WAAA;EJsfF;EI/aU;IAxDV,cAAA;EJ0eA;EIlbU;IAxDV,wBAAA;EJ6eA;EIrbU;IAxDV,yBAAA;EJgfA;EIxbU;IAxDV,gBAAA;EJmfA;EI3bU;IAxDV,yBAAA;EJsfA;EI9bU;IAxDV,yBAAA;EJyfA;EIjcU;IAxDV,gBAAA;EJ4fA;EIpcU;IAxDV,yBAAA;EJ+fA;EIvcU;IAxDV,yBAAA;EJkgBA;EI1cU;IAxDV,gBAAA;EJqgBA;EI7cU;IAxDV,yBAAA;EJwgBA;EIhdU;IAxDV,yBAAA;EJ2gBA;EIxcM;;IAEE,gBAAA;EJ0cR;EIvcM;;IAEE,gBAAA;EJycR;EIhdM;;IAEE,sBAAA;EJkdR;EI/cM;;IAEE,sBAAA;EJidR;EIxdM;;IAEE,qBAAA;EJ0dR;EIvdM;;IAEE,qBAAA;EJydR;EIheM;;IAEE,mBAAA;EJkeR;EI/dM;;IAEE,mBAAA;EJieR;EIxeM;;IAEE,qBAAA;EJ0eR;EIveM;;IAEE,qBAAA;EJyeR;EIhfM;;IAEE,mBAAA;EJkfR;EI/eM;;IAEE,mBAAA;EJifR;AACF;AC5iBI;EGUE;IACE,YAAA;EJqiBN;EIliBI;IApCJ,cAAA;IACA,WAAA;EJykBA;EI3jBA;IACE,cAAA;IACA,WAAA;EJ6jBF;EI/jBA;IACE,cAAA;IACA,UAAA;EJikBF;EInkBA;IACE,cAAA;IACA,qBAAA;EJqkBF;EIvkBA;IACE,cAAA;IACA,UAAA;EJykBF;EI3kBA;IACE,cAAA;IACA,UAAA;EJ6kBF;EI/kBA;IACE,cAAA;IACA,qBAAA;EJilBF;EIljBI;IAhDJ,cAAA;IACA,WAAA;EJqmBA;EIhjBQ;IAhEN,cAAA;IACA,kBAAA;EJmnBF;EIpjBQ;IAhEN,cAAA;IACA,mBAAA;EJunBF;EIxjBQ;IAhEN,cAAA;IACA,UAAA;EJ2nBF;EI5jBQ;IAhEN,cAAA;IACA,mBAAA;EJ+nBF;EIhkBQ;IAhEN,cAAA;IACA,mBAAA;EJmoBF;EIpkBQ;IAhEN,cAAA;IACA,UAAA;EJuoBF;EIxkBQ;IAhEN,cAAA;IACA,mBAAA;EJ2oBF;EI5kBQ;IAhEN,cAAA;IACA,mBAAA;EJ+oBF;EIhlBQ;IAhEN,cAAA;IACA,UAAA;EJmpBF;EIplBQ;IAhEN,cAAA;IACA,mBAAA;EJupBF;EIxlBQ;IAhEN,cAAA;IACA,mBAAA;EJ2pBF;EI5lBQ;IAhEN,cAAA;IACA,WAAA;EJ+pBF;EIxlBU;IAxDV,cAAA;EJmpBA;EI3lBU;IAxDV,wBAAA;EJspBA;EI9lBU;IAxDV,yBAAA;EJypBA;EIjmBU;IAxDV,gBAAA;EJ4pBA;EIpmBU;IAxDV,yBAAA;EJ+pBA;EIvmBU;IAxDV,yBAAA;EJkqBA;EI1mBU;IAxDV,gBAAA;EJqqBA;EI7mBU;IAxDV,yBAAA;EJwqBA;EIhnBU;IAxDV,yBAAA;EJ2qBA;EInnBU;IAxDV,gBAAA;EJ8qBA;EItnBU;IAxDV,yBAAA;EJirBA;EIznBU;IAxDV,yBAAA;EJorBA;EIjnBM;;IAEE,gBAAA;EJmnBR;EIhnBM;;IAEE,gBAAA;EJknBR;EIznBM;;IAEE,sBAAA;EJ2nBR;EIxnBM;;IAEE,sBAAA;EJ0nBR;EIjoBM;;IAEE,qBAAA;EJmoBR;EIhoBM;;IAEE,qBAAA;EJkoBR;EIzoBM;;IAEE,mBAAA;EJ2oBR;EIxoBM;;IAEE,mBAAA;EJ0oBR;EIjpBM;;IAEE,qBAAA;EJmpBR;EIhpBM;;IAEE,qBAAA;EJkpBR;EIzpBM;;IAEE,mBAAA;EJ2pBR;EIxpBM;;IAEE,mBAAA;EJ0pBR;AACF;ACrtBI;EGUE;IACE,YAAA;EJ8sBN;EI3sBI;IApCJ,cAAA;IACA,WAAA;EJkvBA;EIpuBA;IACE,cAAA;IACA,WAAA;EJsuBF;EIxuBA;IACE,cAAA;IACA,UAAA;EJ0uBF;EI5uBA;IACE,cAAA;IACA,qBAAA;EJ8uBF;EIhvBA;IACE,cAAA;IACA,UAAA;EJkvBF;EIpvBA;IACE,cAAA;IACA,UAAA;EJsvBF;EIxvBA;IACE,cAAA;IACA,qBAAA;EJ0vBF;EI3tBI;IAhDJ,cAAA;IACA,WAAA;EJ8wBA;EIztBQ;IAhEN,cAAA;IACA,kBAAA;EJ4xBF;EI7tBQ;IAhEN,cAAA;IACA,mBAAA;EJgyBF;EIjuBQ;IAhEN,cAAA;IACA,UAAA;EJoyBF;EIruBQ;IAhEN,cAAA;IACA,mBAAA;EJwyBF;EIzuBQ;IAhEN,cAAA;IACA,mBAAA;EJ4yBF;EI7uBQ;IAhEN,cAAA;IACA,UAAA;EJgzBF;EIjvBQ;IAhEN,cAAA;IACA,mBAAA;EJozBF;EIrvBQ;IAhEN,cAAA;IACA,mBAAA;EJwzBF;EIzvBQ;IAhEN,cAAA;IACA,UAAA;EJ4zBF;EI7vBQ;IAhEN,cAAA;IACA,mBAAA;EJg0BF;EIjwBQ;IAhEN,cAAA;IACA,mBAAA;EJo0BF;EIrwBQ;IAhEN,cAAA;IACA,WAAA;EJw0BF;EIjwBU;IAxDV,cAAA;EJ4zBA;EIpwBU;IAxDV,wBAAA;EJ+zBA;EIvwBU;IAxDV,yBAAA;EJk0BA;EI1wBU;IAxDV,gBAAA;EJq0BA;EI7wBU;IAxDV,yBAAA;EJw0BA;EIhxBU;IAxDV,yBAAA;EJ20BA;EInxBU;IAxDV,gBAAA;EJ80BA;EItxBU;IAxDV,yBAAA;EJi1BA;EIzxBU;IAxDV,yBAAA;EJo1BA;EI5xBU;IAxDV,gBAAA;EJu1BA;EI/xBU;IAxDV,yBAAA;EJ01BA;EIlyBU;IAxDV,yBAAA;EJ61BA;EI1xBM;;IAEE,gBAAA;EJ4xBR;EIzxBM;;IAEE,gBAAA;EJ2xBR;EIlyBM;;IAEE,sBAAA;EJoyBR;EIjyBM;;IAEE,sBAAA;EJmyBR;EI1yBM;;IAEE,qBAAA;EJ4yBR;EIzyBM;;IAEE,qBAAA;EJ2yBR;EIlzBM;;IAEE,mBAAA;EJozBR;EIjzBM;;IAEE,mBAAA;EJmzBR;EI1zBM;;IAEE,qBAAA;EJ4zBR;EIzzBM;;IAEE,qBAAA;EJ2zBR;EIl0BM;;IAEE,mBAAA;EJo0BR;EIj0BM;;IAEE,mBAAA;EJm0BR;AACF;AC93BI;EGUE;IACE,YAAA;EJu3BN;EIp3BI;IApCJ,cAAA;IACA,WAAA;EJ25BA;EI74BA;IACE,cAAA;IACA,WAAA;EJ+4BF;EIj5BA;IACE,cAAA;IACA,UAAA;EJm5BF;EIr5BA;IACE,cAAA;IACA,qBAAA;EJu5BF;EIz5BA;IACE,cAAA;IACA,UAAA;EJ25BF;EI75BA;IACE,cAAA;IACA,UAAA;EJ+5BF;EIj6BA;IACE,cAAA;IACA,qBAAA;EJm6BF;EIp4BI;IAhDJ,cAAA;IACA,WAAA;EJu7BA;EIl4BQ;IAhEN,cAAA;IACA,kBAAA;EJq8BF;EIt4BQ;IAhEN,cAAA;IACA,mBAAA;EJy8BF;EI14BQ;IAhEN,cAAA;IACA,UAAA;EJ68BF;EI94BQ;IAhEN,cAAA;IACA,mBAAA;EJi9BF;EIl5BQ;IAhEN,cAAA;IACA,mBAAA;EJq9BF;EIt5BQ;IAhEN,cAAA;IACA,UAAA;EJy9BF;EI15BQ;IAhEN,cAAA;IACA,mBAAA;EJ69BF;EI95BQ;IAhEN,cAAA;IACA,mBAAA;EJi+BF;EIl6BQ;IAhEN,cAAA;IACA,UAAA;EJq+BF;EIt6BQ;IAhEN,cAAA;IACA,mBAAA;EJy+BF;EI16BQ;IAhEN,cAAA;IACA,mBAAA;EJ6+BF;EI96BQ;IAhEN,cAAA;IACA,WAAA;EJi/BF;EI16BU;IAxDV,cAAA;EJq+BA;EI76BU;IAxDV,wBAAA;EJw+BA;EIh7BU;IAxDV,yBAAA;EJ2+BA;EIn7BU;IAxDV,gBAAA;EJ8+BA;EIt7BU;IAxDV,yBAAA;EJi/BA;EIz7BU;IAxDV,yBAAA;EJo/BA;EI57BU;IAxDV,gBAAA;EJu/BA;EI/7BU;IAxDV,yBAAA;EJ0/BA;EIl8BU;IAxDV,yBAAA;EJ6/BA;EIr8BU;IAxDV,gBAAA;EJggCA;EIx8BU;IAxDV,yBAAA;EJmgCA;EI38BU;IAxDV,yBAAA;EJsgCA;EIn8BM;;IAEE,gBAAA;EJq8BR;EIl8BM;;IAEE,gBAAA;EJo8BR;EI38BM;;IAEE,sBAAA;EJ68BR;EI18BM;;IAEE,sBAAA;EJ48BR;EIn9BM;;IAEE,qBAAA;EJq9BR;EIl9BM;;IAEE,qBAAA;EJo9BR;EI39BM;;IAEE,mBAAA;EJ69BR;EI19BM;;IAEE,mBAAA;EJ49BR;EIn+BM;;IAEE,qBAAA;EJq+BR;EIl+BM;;IAEE,qBAAA;EJo+BR;EI3+BM;;IAEE,mBAAA;EJ6+BR;EI1+BM;;IAEE,mBAAA;EJ4+BR;AACF;AKpiCQ;EAOI,0BAAA;ALgiCZ;;AKviCQ;EAOI,gCAAA;ALoiCZ;;AK3iCQ;EAOI,yBAAA;ALwiCZ;;AK/iCQ;EAOI,wBAAA;AL4iCZ;;AKnjCQ;EAOI,yBAAA;ALgjCZ;;AKvjCQ;EAOI,6BAAA;ALojCZ;;AK3jCQ;EAOI,8BAAA;ALwjCZ;;AK/jCQ;EAOI,wBAAA;AL4jCZ;;AKnkCQ;EAOI,+BAAA;ALgkCZ;;AKvkCQ;EAOI,wBAAA;ALokCZ;;AK3kCQ;EAOI,yBAAA;ALwkCZ;;AK/kCQ;EAOI,8BAAA;AL4kCZ;;AKnlCQ;EAOI,iCAAA;ALglCZ;;AKvlCQ;EAOI,sCAAA;ALolCZ;;AK3lCQ;EAOI,yCAAA;ALwlCZ;;AK/lCQ;EAOI,uBAAA;AL4lCZ;;AKnmCQ;EAOI,uBAAA;ALgmCZ;;AKvmCQ;EAOI,yBAAA;ALomCZ;;AK3mCQ;EAOI,yBAAA;ALwmCZ;;AK/mCQ;EAOI,0BAAA;AL4mCZ;;AKnnCQ;EAOI,4BAAA;ALgnCZ;;AKvnCQ;EAOI,kCAAA;ALonCZ;;AK3nCQ;EAOI,sCAAA;ALwnCZ;;AK/nCQ;EAOI,oCAAA;AL4nCZ;;AKnoCQ;EAOI,kCAAA;ALgoCZ;;AKvoCQ;EAOI,yCAAA;ALooCZ;;AK3oCQ;EAOI,wCAAA;ALwoCZ;;AK/oCQ;EAOI,wCAAA;AL4oCZ;;AKnpCQ;EAOI,kCAAA;ALgpCZ;;AKvpCQ;EAOI,gCAAA;ALopCZ;;AK3pCQ;EAOI,8BAAA;ALwpCZ;;AK/pCQ;EAOI,gCAAA;AL4pCZ;;AKnqCQ;EAOI,+BAAA;ALgqCZ;;AKvqCQ;EAOI,oCAAA;ALoqCZ;;AK3qCQ;EAOI,kCAAA;ALwqCZ;;AK/qCQ;EAOI,gCAAA;AL4qCZ;;AKnrCQ;EAOI,uCAAA;ALgrCZ;;AKvrCQ;EAOI,sCAAA;ALorCZ;;AK3rCQ;EAOI,iCAAA;ALwrCZ;;AK/rCQ;EAOI,2BAAA;AL4rCZ;;AKnsCQ;EAOI,iCAAA;ALgsCZ;;AKvsCQ;EAOI,+BAAA;ALosCZ;;AK3sCQ;EAOI,6BAAA;ALwsCZ;;AK/sCQ;EAOI,+BAAA;AL4sCZ;;AKntCQ;EAOI,8BAAA;ALgtCZ;;AKvtCQ;EAOI,oBAAA;ALotCZ;;AK3tCQ;EAOI,mBAAA;ALwtCZ;;AK/tCQ;EAOI,mBAAA;AL4tCZ;;AKnuCQ;EAOI,mBAAA;ALguCZ;;AKvuCQ;EAOI,mBAAA;ALouCZ;;AK3uCQ;EAOI,mBAAA;ALwuCZ;;AK/uCQ;EAOI,mBAAA;AL4uCZ;;AKnvCQ;EAOI,mBAAA;ALgvCZ;;AKvvCQ;EAOI,oBAAA;ALovCZ;;AK3vCQ;EAOI,0BAAA;ALwvCZ;;AK/vCQ;EAOI,yBAAA;AL4vCZ;;AKnwCQ;EAOI,uBAAA;ALgwCZ;;AKvwCQ;EAOI,yBAAA;ALowCZ;;AK3wCQ;EAOI,uBAAA;ALwwCZ;;AK/wCQ;EAOI,uBAAA;AL4wCZ;;AKnxCQ;EAOI,0BAAA;EAAA,yBAAA;ALixCZ;;AKxxCQ;EAOI,gCAAA;EAAA,+BAAA;ALsxCZ;;AK7xCQ;EAOI,+BAAA;EAAA,8BAAA;AL2xCZ;;AKlyCQ;EAOI,6BAAA;EAAA,4BAAA;ALgyCZ;;AKvyCQ;EAOI,+BAAA;EAAA,8BAAA;ALqyCZ;;AK5yCQ;EAOI,6BAAA;EAAA,4BAAA;AL0yCZ;;AKjzCQ;EAOI,6BAAA;EAAA,4BAAA;AL+yCZ;;AKtzCQ;EAOI,wBAAA;EAAA,2BAAA;ALozCZ;;AK3zCQ;EAOI,8BAAA;EAAA,iCAAA;ALyzCZ;;AKh0CQ;EAOI,6BAAA;EAAA,gCAAA;AL8zCZ;;AKr0CQ;EAOI,2BAAA;EAAA,8BAAA;ALm0CZ;;AK10CQ;EAOI,6BAAA;EAAA,gCAAA;ALw0CZ;;AK/0CQ;EAOI,2BAAA;EAAA,8BAAA;AL60CZ;;AKp1CQ;EAOI,2BAAA;EAAA,8BAAA;ALk1CZ;;AKz1CQ;EAOI,wBAAA;ALs1CZ;;AK71CQ;EAOI,8BAAA;AL01CZ;;AKj2CQ;EAOI,6BAAA;AL81CZ;;AKr2CQ;EAOI,2BAAA;ALk2CZ;;AKz2CQ;EAOI,6BAAA;ALs2CZ;;AK72CQ;EAOI,2BAAA;AL02CZ;;AKj3CQ;EAOI,2BAAA;AL82CZ;;AKr3CQ;EAOI,0BAAA;ALk3CZ;;AKz3CQ;EAOI,gCAAA;ALs3CZ;;AK73CQ;EAOI,+BAAA;AL03CZ;;AKj4CQ;EAOI,6BAAA;AL83CZ;;AKr4CQ;EAOI,+BAAA;ALk4CZ;;AKz4CQ;EAOI,6BAAA;ALs4CZ;;AK74CQ;EAOI,6BAAA;AL04CZ;;AKj5CQ;EAOI,2BAAA;AL84CZ;;AKr5CQ;EAOI,iCAAA;ALk5CZ;;AKz5CQ;EAOI,gCAAA;ALs5CZ;;AK75CQ;EAOI,8BAAA;AL05CZ;;AKj6CQ;EAOI,gCAAA;AL85CZ;;AKr6CQ;EAOI,8BAAA;ALk6CZ;;AKz6CQ;EAOI,8BAAA;ALs6CZ;;AK76CQ;EAOI,yBAAA;AL06CZ;;AKj7CQ;EAOI,+BAAA;AL86CZ;;AKr7CQ;EAOI,8BAAA;ALk7CZ;;AKz7CQ;EAOI,4BAAA;ALs7CZ;;AK77CQ;EAOI,8BAAA;AL07CZ;;AKj8CQ;EAOI,4BAAA;AL87CZ;;AKr8CQ;EAOI,4BAAA;ALk8CZ;;AKz8CQ;EAOI,qBAAA;ALs8CZ;;AK78CQ;EAOI,2BAAA;AL08CZ;;AKj9CQ;EAOI,0BAAA;AL88CZ;;AKr9CQ;EAOI,wBAAA;ALk9CZ;;AKz9CQ;EAOI,0BAAA;ALs9CZ;;AK79CQ;EAOI,wBAAA;AL09CZ;;AKj+CQ;EAOI,2BAAA;EAAA,0BAAA;AL+9CZ;;AKt+CQ;EAOI,iCAAA;EAAA,gCAAA;ALo+CZ;;AK3+CQ;EAOI,gCAAA;EAAA,+BAAA;ALy+CZ;;AKh/CQ;EAOI,8BAAA;EAAA,6BAAA;AL8+CZ;;AKr/CQ;EAOI,gCAAA;EAAA,+BAAA;ALm/CZ;;AK1/CQ;EAOI,8BAAA;EAAA,6BAAA;ALw/CZ;;AK//CQ;EAOI,yBAAA;EAAA,4BAAA;AL6/CZ;;AKpgDQ;EAOI,+BAAA;EAAA,kCAAA;ALkgDZ;;AKzgDQ;EAOI,8BAAA;EAAA,iCAAA;ALugDZ;;AK9gDQ;EAOI,4BAAA;EAAA,+BAAA;AL4gDZ;;AKnhDQ;EAOI,8BAAA;EAAA,iCAAA;ALihDZ;;AKxhDQ;EAOI,4BAAA;EAAA,+BAAA;ALshDZ;;AK7hDQ;EAOI,yBAAA;AL0hDZ;;AKjiDQ;EAOI,+BAAA;AL8hDZ;;AKriDQ;EAOI,8BAAA;ALkiDZ;;AKziDQ;EAOI,4BAAA;ALsiDZ;;AK7iDQ;EAOI,8BAAA;AL0iDZ;;AKjjDQ;EAOI,4BAAA;AL8iDZ;;AKrjDQ;EAOI,2BAAA;ALkjDZ;;AKzjDQ;EAOI,iCAAA;ALsjDZ;;AK7jDQ;EAOI,gCAAA;AL0jDZ;;AKjkDQ;EAOI,8BAAA;AL8jDZ;;AKrkDQ;EAOI,gCAAA;ALkkDZ;;AKzkDQ;EAOI,8BAAA;ALskDZ;;AK7kDQ;EAOI,4BAAA;AL0kDZ;;AKjlDQ;EAOI,kCAAA;AL8kDZ;;AKrlDQ;EAOI,iCAAA;ALklDZ;;AKzlDQ;EAOI,+BAAA;ALslDZ;;AK7lDQ;EAOI,iCAAA;AL0lDZ;;AKjmDQ;EAOI,+BAAA;AL8lDZ;;AKrmDQ;EAOI,0BAAA;ALkmDZ;;AKzmDQ;EAOI,gCAAA;ALsmDZ;;AK7mDQ;EAOI,+BAAA;AL0mDZ;;AKjnDQ;EAOI,6BAAA;AL8mDZ;;AKrnDQ;EAOI,+BAAA;ALknDZ;;AKznDQ;EAOI,6BAAA;ALsnDZ;;AChoDI;EIGI;IAOI,0BAAA;EL2nDV;EKloDM;IAOI,gCAAA;EL8nDV;EKroDM;IAOI,yBAAA;ELioDV;EKxoDM;IAOI,wBAAA;ELooDV;EK3oDM;IAOI,yBAAA;ELuoDV;EK9oDM;IAOI,6BAAA;EL0oDV;EKjpDM;IAOI,8BAAA;EL6oDV;EKppDM;IAOI,wBAAA;ELgpDV;EKvpDM;IAOI,+BAAA;ELmpDV;EK1pDM;IAOI,wBAAA;ELspDV;EK7pDM;IAOI,yBAAA;ELypDV;EKhqDM;IAOI,8BAAA;EL4pDV;EKnqDM;IAOI,iCAAA;EL+pDV;EKtqDM;IAOI,sCAAA;ELkqDV;EKzqDM;IAOI,yCAAA;ELqqDV;EK5qDM;IAOI,uBAAA;ELwqDV;EK/qDM;IAOI,uBAAA;EL2qDV;EKlrDM;IAOI,yBAAA;EL8qDV;EKrrDM;IAOI,yBAAA;ELirDV;EKxrDM;IAOI,0BAAA;ELorDV;EK3rDM;IAOI,4BAAA;ELurDV;EK9rDM;IAOI,kCAAA;EL0rDV;EKjsDM;IAOI,sCAAA;EL6rDV;EKpsDM;IAOI,oCAAA;ELgsDV;EKvsDM;IAOI,kCAAA;ELmsDV;EK1sDM;IAOI,yCAAA;ELssDV;EK7sDM;IAOI,wCAAA;ELysDV;EKhtDM;IAOI,wCAAA;EL4sDV;EKntDM;IAOI,kCAAA;EL+sDV;EKttDM;IAOI,gCAAA;ELktDV;EKztDM;IAOI,8BAAA;ELqtDV;EK5tDM;IAOI,gCAAA;ELwtDV;EK/tDM;IAOI,+BAAA;EL2tDV;EKluDM;IAOI,oCAAA;EL8tDV;EKruDM;IAOI,kCAAA;ELiuDV;EKxuDM;IAOI,gCAAA;ELouDV;EK3uDM;IAOI,uCAAA;ELuuDV;EK9uDM;IAOI,sCAAA;EL0uDV;EKjvDM;IAOI,iCAAA;EL6uDV;EKpvDM;IAOI,2BAAA;ELgvDV;EKvvDM;IAOI,iCAAA;ELmvDV;EK1vDM;IAOI,+BAAA;ELsvDV;EK7vDM;IAOI,6BAAA;ELyvDV;EKhwDM;IAOI,+BAAA;EL4vDV;EKnwDM;IAOI,8BAAA;EL+vDV;EKtwDM;IAOI,oBAAA;ELkwDV;EKzwDM;IAOI,mBAAA;ELqwDV;EK5wDM;IAOI,mBAAA;ELwwDV;EK/wDM;IAOI,mBAAA;EL2wDV;EKlxDM;IAOI,mBAAA;EL8wDV;EKrxDM;IAOI,mBAAA;ELixDV;EKxxDM;IAOI,mBAAA;ELoxDV;EK3xDM;IAOI,mBAAA;ELuxDV;EK9xDM;IAOI,oBAAA;EL0xDV;EKjyDM;IAOI,0BAAA;EL6xDV;EKpyDM;IAOI,yBAAA;ELgyDV;EKvyDM;IAOI,uBAAA;ELmyDV;EK1yDM;IAOI,yBAAA;ELsyDV;EK7yDM;IAOI,uBAAA;ELyyDV;EKhzDM;IAOI,uBAAA;EL4yDV;EKnzDM;IAOI,0BAAA;IAAA,yBAAA;ELgzDV;EKvzDM;IAOI,gCAAA;IAAA,+BAAA;ELozDV;EK3zDM;IAOI,+BAAA;IAAA,8BAAA;ELwzDV;EK/zDM;IAOI,6BAAA;IAAA,4BAAA;EL4zDV;EKn0DM;IAOI,+BAAA;IAAA,8BAAA;ELg0DV;EKv0DM;IAOI,6BAAA;IAAA,4BAAA;ELo0DV;EK30DM;IAOI,6BAAA;IAAA,4BAAA;ELw0DV;EK/0DM;IAOI,wBAAA;IAAA,2BAAA;EL40DV;EKn1DM;IAOI,8BAAA;IAAA,iCAAA;ELg1DV;EKv1DM;IAOI,6BAAA;IAAA,gCAAA;ELo1DV;EK31DM;IAOI,2BAAA;IAAA,8BAAA;ELw1DV;EK/1DM;IAOI,6BAAA;IAAA,gCAAA;EL41DV;EKn2DM;IAOI,2BAAA;IAAA,8BAAA;ELg2DV;EKv2DM;IAOI,2BAAA;IAAA,8BAAA;ELo2DV;EK32DM;IAOI,wBAAA;ELu2DV;EK92DM;IAOI,8BAAA;EL02DV;EKj3DM;IAOI,6BAAA;EL62DV;EKp3DM;IAOI,2BAAA;ELg3DV;EKv3DM;IAOI,6BAAA;ELm3DV;EK13DM;IAOI,2BAAA;ELs3DV;EK73DM;IAOI,2BAAA;ELy3DV;EKh4DM;IAOI,0BAAA;EL43DV;EKn4DM;IAOI,gCAAA;EL+3DV;EKt4DM;IAOI,+BAAA;ELk4DV;EKz4DM;IAOI,6BAAA;ELq4DV;EK54DM;IAOI,+BAAA;ELw4DV;EK/4DM;IAOI,6BAAA;EL24DV;EKl5DM;IAOI,6BAAA;EL84DV;EKr5DM;IAOI,2BAAA;ELi5DV;EKx5DM;IAOI,iCAAA;ELo5DV;EK35DM;IAOI,gCAAA;ELu5DV;EK95DM;IAOI,8BAAA;EL05DV;EKj6DM;IAOI,gCAAA;EL65DV;EKp6DM;IAOI,8BAAA;ELg6DV;EKv6DM;IAOI,8BAAA;ELm6DV;EK16DM;IAOI,yBAAA;ELs6DV;EK76DM;IAOI,+BAAA;ELy6DV;EKh7DM;IAOI,8BAAA;EL46DV;EKn7DM;IAOI,4BAAA;EL+6DV;EKt7DM;IAOI,8BAAA;ELk7DV;EKz7DM;IAOI,4BAAA;ELq7DV;EK57DM;IAOI,4BAAA;ELw7DV;EK/7DM;IAOI,qBAAA;EL27DV;EKl8DM;IAOI,2BAAA;EL87DV;EKr8DM;IAOI,0BAAA;ELi8DV;EKx8DM;IAOI,wBAAA;ELo8DV;EK38DM;IAOI,0BAAA;ELu8DV;EK98DM;IAOI,wBAAA;EL08DV;EKj9DM;IAOI,2BAAA;IAAA,0BAAA;EL88DV;EKr9DM;IAOI,iCAAA;IAAA,gCAAA;ELk9DV;EKz9DM;IAOI,gCAAA;IAAA,+BAAA;ELs9DV;EK79DM;IAOI,8BAAA;IAAA,6BAAA;EL09DV;EKj+DM;IAOI,gCAAA;IAAA,+BAAA;EL89DV;EKr+DM;IAOI,8BAAA;IAAA,6BAAA;ELk+DV;EKz+DM;IAOI,yBAAA;IAAA,4BAAA;ELs+DV;EK7+DM;IAOI,+BAAA;IAAA,kCAAA;EL0+DV;EKj/DM;IAOI,8BAAA;IAAA,iCAAA;EL8+DV;EKr/DM;IAOI,4BAAA;IAAA,+BAAA;ELk/DV;EKz/DM;IAOI,8BAAA;IAAA,iCAAA;ELs/DV;EK7/DM;IAOI,4BAAA;IAAA,+BAAA;EL0/DV;EKjgEM;IAOI,yBAAA;EL6/DV;EKpgEM;IAOI,+BAAA;ELggEV;EKvgEM;IAOI,8BAAA;ELmgEV;EK1gEM;IAOI,4BAAA;ELsgEV;EK7gEM;IAOI,8BAAA;ELygEV;EKhhEM;IAOI,4BAAA;EL4gEV;EKnhEM;IAOI,2BAAA;EL+gEV;EKthEM;IAOI,iCAAA;ELkhEV;EKzhEM;IAOI,gCAAA;ELqhEV;EK5hEM;IAOI,8BAAA;ELwhEV;EK/hEM;IAOI,gCAAA;EL2hEV;EKliEM;IAOI,8BAAA;EL8hEV;EKriEM;IAOI,4BAAA;ELiiEV;EKxiEM;IAOI,kCAAA;ELoiEV;EK3iEM;IAOI,iCAAA;ELuiEV;EK9iEM;IAOI,+BAAA;EL0iEV;EKjjEM;IAOI,iCAAA;EL6iEV;EKpjEM;IAOI,+BAAA;ELgjEV;EKvjEM;IAOI,0BAAA;ELmjEV;EK1jEM;IAOI,gCAAA;ELsjEV;EK7jEM;IAOI,+BAAA;ELyjEV;EKhkEM;IAOI,6BAAA;EL4jEV;EKnkEM;IAOI,+BAAA;EL+jEV;EKtkEM;IAOI,6BAAA;ELkkEV;AACF;AC7kEI;EIGI;IAOI,0BAAA;ELukEV;EK9kEM;IAOI,gCAAA;EL0kEV;EKjlEM;IAOI,yBAAA;EL6kEV;EKplEM;IAOI,wBAAA;ELglEV;EKvlEM;IAOI,yBAAA;ELmlEV;EK1lEM;IAOI,6BAAA;ELslEV;EK7lEM;IAOI,8BAAA;ELylEV;EKhmEM;IAOI,wBAAA;EL4lEV;EKnmEM;IAOI,+BAAA;EL+lEV;EKtmEM;IAOI,wBAAA;ELkmEV;EKzmEM;IAOI,yBAAA;ELqmEV;EK5mEM;IAOI,8BAAA;ELwmEV;EK/mEM;IAOI,iCAAA;EL2mEV;EKlnEM;IAOI,sCAAA;EL8mEV;EKrnEM;IAOI,yCAAA;ELinEV;EKxnEM;IAOI,uBAAA;ELonEV;EK3nEM;IAOI,uBAAA;ELunEV;EK9nEM;IAOI,yBAAA;EL0nEV;EKjoEM;IAOI,yBAAA;EL6nEV;EKpoEM;IAOI,0BAAA;ELgoEV;EKvoEM;IAOI,4BAAA;ELmoEV;EK1oEM;IAOI,kCAAA;ELsoEV;EK7oEM;IAOI,sCAAA;ELyoEV;EKhpEM;IAOI,oCAAA;EL4oEV;EKnpEM;IAOI,kCAAA;EL+oEV;EKtpEM;IAOI,yCAAA;ELkpEV;EKzpEM;IAOI,wCAAA;ELqpEV;EK5pEM;IAOI,wCAAA;ELwpEV;EK/pEM;IAOI,kCAAA;EL2pEV;EKlqEM;IAOI,gCAAA;EL8pEV;EKrqEM;IAOI,8BAAA;ELiqEV;EKxqEM;IAOI,gCAAA;ELoqEV;EK3qEM;IAOI,+BAAA;ELuqEV;EK9qEM;IAOI,oCAAA;EL0qEV;EKjrEM;IAOI,kCAAA;EL6qEV;EKprEM;IAOI,gCAAA;ELgrEV;EKvrEM;IAOI,uCAAA;ELmrEV;EK1rEM;IAOI,sCAAA;ELsrEV;EK7rEM;IAOI,iCAAA;ELyrEV;EKhsEM;IAOI,2BAAA;EL4rEV;EKnsEM;IAOI,iCAAA;EL+rEV;EKtsEM;IAOI,+BAAA;ELksEV;EKzsEM;IAOI,6BAAA;ELqsEV;EK5sEM;IAOI,+BAAA;ELwsEV;EK/sEM;IAOI,8BAAA;EL2sEV;EKltEM;IAOI,oBAAA;EL8sEV;EKrtEM;IAOI,mBAAA;ELitEV;EKxtEM;IAOI,mBAAA;ELotEV;EK3tEM;IAOI,mBAAA;ELutEV;EK9tEM;IAOI,mBAAA;EL0tEV;EKjuEM;IAOI,mBAAA;EL6tEV;EKpuEM;IAOI,mBAAA;ELguEV;EKvuEM;IAOI,mBAAA;ELmuEV;EK1uEM;IAOI,oBAAA;ELsuEV;EK7uEM;IAOI,0BAAA;ELyuEV;EKhvEM;IAOI,yBAAA;EL4uEV;EKnvEM;IAOI,uBAAA;EL+uEV;EKtvEM;IAOI,yBAAA;ELkvEV;EKzvEM;IAOI,uBAAA;ELqvEV;EK5vEM;IAOI,uBAAA;ELwvEV;EK/vEM;IAOI,0BAAA;IAAA,yBAAA;EL4vEV;EKnwEM;IAOI,gCAAA;IAAA,+BAAA;ELgwEV;EKvwEM;IAOI,+BAAA;IAAA,8BAAA;ELowEV;EK3wEM;IAOI,6BAAA;IAAA,4BAAA;ELwwEV;EK/wEM;IAOI,+BAAA;IAAA,8BAAA;EL4wEV;EKnxEM;IAOI,6BAAA;IAAA,4BAAA;ELgxEV;EKvxEM;IAOI,6BAAA;IAAA,4BAAA;ELoxEV;EK3xEM;IAOI,wBAAA;IAAA,2BAAA;ELwxEV;EK/xEM;IAOI,8BAAA;IAAA,iCAAA;EL4xEV;EKnyEM;IAOI,6BAAA;IAAA,gCAAA;ELgyEV;EKvyEM;IAOI,2BAAA;IAAA,8BAAA;ELoyEV;EK3yEM;IAOI,6BAAA;IAAA,gCAAA;ELwyEV;EK/yEM;IAOI,2BAAA;IAAA,8BAAA;EL4yEV;EKnzEM;IAOI,2BAAA;IAAA,8BAAA;ELgzEV;EKvzEM;IAOI,wBAAA;ELmzEV;EK1zEM;IAOI,8BAAA;ELszEV;EK7zEM;IAOI,6BAAA;ELyzEV;EKh0EM;IAOI,2BAAA;EL4zEV;EKn0EM;IAOI,6BAAA;EL+zEV;EKt0EM;IAOI,2BAAA;ELk0EV;EKz0EM;IAOI,2BAAA;ELq0EV;EK50EM;IAOI,0BAAA;ELw0EV;EK/0EM;IAOI,gCAAA;EL20EV;EKl1EM;IAOI,+BAAA;EL80EV;EKr1EM;IAOI,6BAAA;ELi1EV;EKx1EM;IAOI,+BAAA;ELo1EV;EK31EM;IAOI,6BAAA;ELu1EV;EK91EM;IAOI,6BAAA;EL01EV;EKj2EM;IAOI,2BAAA;EL61EV;EKp2EM;IAOI,iCAAA;ELg2EV;EKv2EM;IAOI,gCAAA;ELm2EV;EK12EM;IAOI,8BAAA;ELs2EV;EK72EM;IAOI,gCAAA;ELy2EV;EKh3EM;IAOI,8BAAA;EL42EV;EKn3EM;IAOI,8BAAA;EL+2EV;EKt3EM;IAOI,yBAAA;ELk3EV;EKz3EM;IAOI,+BAAA;ELq3EV;EK53EM;IAOI,8BAAA;ELw3EV;EK/3EM;IAOI,4BAAA;EL23EV;EKl4EM;IAOI,8BAAA;EL83EV;EKr4EM;IAOI,4BAAA;ELi4EV;EKx4EM;IAOI,4BAAA;ELo4EV;EK34EM;IAOI,qBAAA;ELu4EV;EK94EM;IAOI,2BAAA;EL04EV;EKj5EM;IAOI,0BAAA;EL64EV;EKp5EM;IAOI,wBAAA;ELg5EV;EKv5EM;IAOI,0BAAA;ELm5EV;EK15EM;IAOI,wBAAA;ELs5EV;EK75EM;IAOI,2BAAA;IAAA,0BAAA;EL05EV;EKj6EM;IAOI,iCAAA;IAAA,gCAAA;EL85EV;EKr6EM;IAOI,gCAAA;IAAA,+BAAA;ELk6EV;EKz6EM;IAOI,8BAAA;IAAA,6BAAA;ELs6EV;EK76EM;IAOI,gCAAA;IAAA,+BAAA;EL06EV;EKj7EM;IAOI,8BAAA;IAAA,6BAAA;EL86EV;EKr7EM;IAOI,yBAAA;IAAA,4BAAA;ELk7EV;EKz7EM;IAOI,+BAAA;IAAA,kCAAA;ELs7EV;EK77EM;IAOI,8BAAA;IAAA,iCAAA;EL07EV;EKj8EM;IAOI,4BAAA;IAAA,+BAAA;EL87EV;EKr8EM;IAOI,8BAAA;IAAA,iCAAA;ELk8EV;EKz8EM;IAOI,4BAAA;IAAA,+BAAA;ELs8EV;EK78EM;IAOI,yBAAA;ELy8EV;EKh9EM;IAOI,+BAAA;EL48EV;EKn9EM;IAOI,8BAAA;EL+8EV;EKt9EM;IAOI,4BAAA;ELk9EV;EKz9EM;IAOI,8BAAA;ELq9EV;EK59EM;IAOI,4BAAA;ELw9EV;EK/9EM;IAOI,2BAAA;EL29EV;EKl+EM;IAOI,iCAAA;EL89EV;EKr+EM;IAOI,gCAAA;ELi+EV;EKx+EM;IAOI,8BAAA;ELo+EV;EK3+EM;IAOI,gCAAA;ELu+EV;EK9+EM;IAOI,8BAAA;EL0+EV;EKj/EM;IAOI,4BAAA;EL6+EV;EKp/EM;IAOI,kCAAA;ELg/EV;EKv/EM;IAOI,iCAAA;ELm/EV;EK1/EM;IAOI,+BAAA;ELs/EV;EK7/EM;IAOI,iCAAA;ELy/EV;EKhgFM;IAOI,+BAAA;EL4/EV;EKngFM;IAOI,0BAAA;EL+/EV;EKtgFM;IAOI,gCAAA;ELkgFV;EKzgFM;IAOI,+BAAA;ELqgFV;EK5gFM;IAOI,6BAAA;ELwgFV;EK/gFM;IAOI,+BAAA;EL2gFV;EKlhFM;IAOI,6BAAA;EL8gFV;AACF;ACzhFI;EIGI;IAOI,0BAAA;ELmhFV;EK1hFM;IAOI,gCAAA;ELshFV;EK7hFM;IAOI,yBAAA;ELyhFV;EKhiFM;IAOI,wBAAA;EL4hFV;EKniFM;IAOI,yBAAA;EL+hFV;EKtiFM;IAOI,6BAAA;ELkiFV;EKziFM;IAOI,8BAAA;ELqiFV;EK5iFM;IAOI,wBAAA;ELwiFV;EK/iFM;IAOI,+BAAA;EL2iFV;EKljFM;IAOI,wBAAA;EL8iFV;EKrjFM;IAOI,yBAAA;ELijFV;EKxjFM;IAOI,8BAAA;ELojFV;EK3jFM;IAOI,iCAAA;ELujFV;EK9jFM;IAOI,sCAAA;EL0jFV;EKjkFM;IAOI,yCAAA;EL6jFV;EKpkFM;IAOI,uBAAA;ELgkFV;EKvkFM;IAOI,uBAAA;ELmkFV;EK1kFM;IAOI,yBAAA;ELskFV;EK7kFM;IAOI,yBAAA;ELykFV;EKhlFM;IAOI,0BAAA;EL4kFV;EKnlFM;IAOI,4BAAA;EL+kFV;EKtlFM;IAOI,kCAAA;ELklFV;EKzlFM;IAOI,sCAAA;ELqlFV;EK5lFM;IAOI,oCAAA;ELwlFV;EK/lFM;IAOI,kCAAA;EL2lFV;EKlmFM;IAOI,yCAAA;EL8lFV;EKrmFM;IAOI,wCAAA;ELimFV;EKxmFM;IAOI,wCAAA;ELomFV;EK3mFM;IAOI,kCAAA;ELumFV;EK9mFM;IAOI,gCAAA;EL0mFV;EKjnFM;IAOI,8BAAA;EL6mFV;EKpnFM;IAOI,gCAAA;ELgnFV;EKvnFM;IAOI,+BAAA;ELmnFV;EK1nFM;IAOI,oCAAA;ELsnFV;EK7nFM;IAOI,kCAAA;ELynFV;EKhoFM;IAOI,gCAAA;EL4nFV;EKnoFM;IAOI,uCAAA;EL+nFV;EKtoFM;IAOI,sCAAA;ELkoFV;EKzoFM;IAOI,iCAAA;ELqoFV;EK5oFM;IAOI,2BAAA;ELwoFV;EK/oFM;IAOI,iCAAA;EL2oFV;EKlpFM;IAOI,+BAAA;EL8oFV;EKrpFM;IAOI,6BAAA;ELipFV;EKxpFM;IAOI,+BAAA;ELopFV;EK3pFM;IAOI,8BAAA;ELupFV;EK9pFM;IAOI,oBAAA;EL0pFV;EKjqFM;IAOI,mBAAA;EL6pFV;EKpqFM;IAOI,mBAAA;ELgqFV;EKvqFM;IAOI,mBAAA;ELmqFV;EK1qFM;IAOI,mBAAA;ELsqFV;EK7qFM;IAOI,mBAAA;ELyqFV;EKhrFM;IAOI,mBAAA;EL4qFV;EKnrFM;IAOI,mBAAA;EL+qFV;EKtrFM;IAOI,oBAAA;ELkrFV;EKzrFM;IAOI,0BAAA;ELqrFV;EK5rFM;IAOI,yBAAA;ELwrFV;EK/rFM;IAOI,uBAAA;EL2rFV;EKlsFM;IAOI,yBAAA;EL8rFV;EKrsFM;IAOI,uBAAA;ELisFV;EKxsFM;IAOI,uBAAA;ELosFV;EK3sFM;IAOI,0BAAA;IAAA,yBAAA;ELwsFV;EK/sFM;IAOI,gCAAA;IAAA,+BAAA;EL4sFV;EKntFM;IAOI,+BAAA;IAAA,8BAAA;ELgtFV;EKvtFM;IAOI,6BAAA;IAAA,4BAAA;ELotFV;EK3tFM;IAOI,+BAAA;IAAA,8BAAA;ELwtFV;EK/tFM;IAOI,6BAAA;IAAA,4BAAA;EL4tFV;EKnuFM;IAOI,6BAAA;IAAA,4BAAA;ELguFV;EKvuFM;IAOI,wBAAA;IAAA,2BAAA;ELouFV;EK3uFM;IAOI,8BAAA;IAAA,iCAAA;ELwuFV;EK/uFM;IAOI,6BAAA;IAAA,gCAAA;EL4uFV;EKnvFM;IAOI,2BAAA;IAAA,8BAAA;ELgvFV;EKvvFM;IAOI,6BAAA;IAAA,gCAAA;ELovFV;EK3vFM;IAOI,2BAAA;IAAA,8BAAA;ELwvFV;EK/vFM;IAOI,2BAAA;IAAA,8BAAA;EL4vFV;EKnwFM;IAOI,wBAAA;EL+vFV;EKtwFM;IAOI,8BAAA;ELkwFV;EKzwFM;IAOI,6BAAA;ELqwFV;EK5wFM;IAOI,2BAAA;ELwwFV;EK/wFM;IAOI,6BAAA;EL2wFV;EKlxFM;IAOI,2BAAA;EL8wFV;EKrxFM;IAOI,2BAAA;ELixFV;EKxxFM;IAOI,0BAAA;ELoxFV;EK3xFM;IAOI,gCAAA;ELuxFV;EK9xFM;IAOI,+BAAA;EL0xFV;EKjyFM;IAOI,6BAAA;EL6xFV;EKpyFM;IAOI,+BAAA;ELgyFV;EKvyFM;IAOI,6BAAA;ELmyFV;EK1yFM;IAOI,6BAAA;ELsyFV;EK7yFM;IAOI,2BAAA;ELyyFV;EKhzFM;IAOI,iCAAA;EL4yFV;EKnzFM;IAOI,gCAAA;EL+yFV;EKtzFM;IAOI,8BAAA;ELkzFV;EKzzFM;IAOI,gCAAA;ELqzFV;EK5zFM;IAOI,8BAAA;ELwzFV;EK/zFM;IAOI,8BAAA;EL2zFV;EKl0FM;IAOI,yBAAA;EL8zFV;EKr0FM;IAOI,+BAAA;ELi0FV;EKx0FM;IAOI,8BAAA;ELo0FV;EK30FM;IAOI,4BAAA;ELu0FV;EK90FM;IAOI,8BAAA;EL00FV;EKj1FM;IAOI,4BAAA;EL60FV;EKp1FM;IAOI,4BAAA;ELg1FV;EKv1FM;IAOI,qBAAA;ELm1FV;EK11FM;IAOI,2BAAA;ELs1FV;EK71FM;IAOI,0BAAA;ELy1FV;EKh2FM;IAOI,wBAAA;EL41FV;EKn2FM;IAOI,0BAAA;EL+1FV;EKt2FM;IAOI,wBAAA;ELk2FV;EKz2FM;IAOI,2BAAA;IAAA,0BAAA;ELs2FV;EK72FM;IAOI,iCAAA;IAAA,gCAAA;EL02FV;EKj3FM;IAOI,gCAAA;IAAA,+BAAA;EL82FV;EKr3FM;IAOI,8BAAA;IAAA,6BAAA;ELk3FV;EKz3FM;IAOI,gCAAA;IAAA,+BAAA;ELs3FV;EK73FM;IAOI,8BAAA;IAAA,6BAAA;EL03FV;EKj4FM;IAOI,yBAAA;IAAA,4BAAA;EL83FV;EKr4FM;IAOI,+BAAA;IAAA,kCAAA;ELk4FV;EKz4FM;IAOI,8BAAA;IAAA,iCAAA;ELs4FV;EK74FM;IAOI,4BAAA;IAAA,+BAAA;EL04FV;EKj5FM;IAOI,8BAAA;IAAA,iCAAA;EL84FV;EKr5FM;IAOI,4BAAA;IAAA,+BAAA;ELk5FV;EKz5FM;IAOI,yBAAA;ELq5FV;EK55FM;IAOI,+BAAA;ELw5FV;EK/5FM;IAOI,8BAAA;EL25FV;EKl6FM;IAOI,4BAAA;EL85FV;EKr6FM;IAOI,8BAAA;ELi6FV;EKx6FM;IAOI,4BAAA;ELo6FV;EK36FM;IAOI,2BAAA;ELu6FV;EK96FM;IAOI,iCAAA;EL06FV;EKj7FM;IAOI,gCAAA;EL66FV;EKp7FM;IAOI,8BAAA;ELg7FV;EKv7FM;IAOI,gCAAA;ELm7FV;EK17FM;IAOI,8BAAA;ELs7FV;EK77FM;IAOI,4BAAA;ELy7FV;EKh8FM;IAOI,kCAAA;EL47FV;EKn8FM;IAOI,iCAAA;EL+7FV;EKt8FM;IAOI,+BAAA;ELk8FV;EKz8FM;IAOI,iCAAA;ELq8FV;EK58FM;IAOI,+BAAA;ELw8FV;EK/8FM;IAOI,0BAAA;EL28FV;EKl9FM;IAOI,gCAAA;EL88FV;EKr9FM;IAOI,+BAAA;ELi9FV;EKx9FM;IAOI,6BAAA;ELo9FV;EK39FM;IAOI,+BAAA;ELu9FV;EK99FM;IAOI,6BAAA;EL09FV;AACF;ACr+FI;EIGI;IAOI,0BAAA;EL+9FV;EKt+FM;IAOI,gCAAA;ELk+FV;EKz+FM;IAOI,yBAAA;ELq+FV;EK5+FM;IAOI,wBAAA;ELw+FV;EK/+FM;IAOI,yBAAA;EL2+FV;EKl/FM;IAOI,6BAAA;EL8+FV;EKr/FM;IAOI,8BAAA;ELi/FV;EKx/FM;IAOI,wBAAA;ELo/FV;EK3/FM;IAOI,+BAAA;ELu/FV;EK9/FM;IAOI,wBAAA;EL0/FV;EKjgGM;IAOI,yBAAA;EL6/FV;EKpgGM;IAOI,8BAAA;ELggGV;EKvgGM;IAOI,iCAAA;ELmgGV;EK1gGM;IAOI,sCAAA;ELsgGV;EK7gGM;IAOI,yCAAA;ELygGV;EKhhGM;IAOI,uBAAA;EL4gGV;EKnhGM;IAOI,uBAAA;EL+gGV;EKthGM;IAOI,yBAAA;ELkhGV;EKzhGM;IAOI,yBAAA;ELqhGV;EK5hGM;IAOI,0BAAA;ELwhGV;EK/hGM;IAOI,4BAAA;EL2hGV;EKliGM;IAOI,kCAAA;EL8hGV;EKriGM;IAOI,sCAAA;ELiiGV;EKxiGM;IAOI,oCAAA;ELoiGV;EK3iGM;IAOI,kCAAA;ELuiGV;EK9iGM;IAOI,yCAAA;EL0iGV;EKjjGM;IAOI,wCAAA;EL6iGV;EKpjGM;IAOI,wCAAA;ELgjGV;EKvjGM;IAOI,kCAAA;ELmjGV;EK1jGM;IAOI,gCAAA;ELsjGV;EK7jGM;IAOI,8BAAA;ELyjGV;EKhkGM;IAOI,gCAAA;EL4jGV;EKnkGM;IAOI,+BAAA;EL+jGV;EKtkGM;IAOI,oCAAA;ELkkGV;EKzkGM;IAOI,kCAAA;ELqkGV;EK5kGM;IAOI,gCAAA;ELwkGV;EK/kGM;IAOI,uCAAA;EL2kGV;EKllGM;IAOI,sCAAA;EL8kGV;EKrlGM;IAOI,iCAAA;ELilGV;EKxlGM;IAOI,2BAAA;ELolGV;EK3lGM;IAOI,iCAAA;ELulGV;EK9lGM;IAOI,+BAAA;EL0lGV;EKjmGM;IAOI,6BAAA;EL6lGV;EKpmGM;IAOI,+BAAA;ELgmGV;EKvmGM;IAOI,8BAAA;ELmmGV;EK1mGM;IAOI,oBAAA;ELsmGV;EK7mGM;IAOI,mBAAA;ELymGV;EKhnGM;IAOI,mBAAA;EL4mGV;EKnnGM;IAOI,mBAAA;EL+mGV;EKtnGM;IAOI,mBAAA;ELknGV;EKznGM;IAOI,mBAAA;ELqnGV;EK5nGM;IAOI,mBAAA;ELwnGV;EK/nGM;IAOI,mBAAA;EL2nGV;EKloGM;IAOI,oBAAA;EL8nGV;EKroGM;IAOI,0BAAA;ELioGV;EKxoGM;IAOI,yBAAA;ELooGV;EK3oGM;IAOI,uBAAA;ELuoGV;EK9oGM;IAOI,yBAAA;EL0oGV;EKjpGM;IAOI,uBAAA;EL6oGV;EKppGM;IAOI,uBAAA;ELgpGV;EKvpGM;IAOI,0BAAA;IAAA,yBAAA;ELopGV;EK3pGM;IAOI,gCAAA;IAAA,+BAAA;ELwpGV;EK/pGM;IAOI,+BAAA;IAAA,8BAAA;EL4pGV;EKnqGM;IAOI,6BAAA;IAAA,4BAAA;ELgqGV;EKvqGM;IAOI,+BAAA;IAAA,8BAAA;ELoqGV;EK3qGM;IAOI,6BAAA;IAAA,4BAAA;ELwqGV;EK/qGM;IAOI,6BAAA;IAAA,4BAAA;EL4qGV;EKnrGM;IAOI,wBAAA;IAAA,2BAAA;ELgrGV;EKvrGM;IAOI,8BAAA;IAAA,iCAAA;ELorGV;EK3rGM;IAOI,6BAAA;IAAA,gCAAA;ELwrGV;EK/rGM;IAOI,2BAAA;IAAA,8BAAA;EL4rGV;EKnsGM;IAOI,6BAAA;IAAA,gCAAA;ELgsGV;EKvsGM;IAOI,2BAAA;IAAA,8BAAA;ELosGV;EK3sGM;IAOI,2BAAA;IAAA,8BAAA;ELwsGV;EK/sGM;IAOI,wBAAA;EL2sGV;EKltGM;IAOI,8BAAA;EL8sGV;EKrtGM;IAOI,6BAAA;ELitGV;EKxtGM;IAOI,2BAAA;ELotGV;EK3tGM;IAOI,6BAAA;ELutGV;EK9tGM;IAOI,2BAAA;EL0tGV;EKjuGM;IAOI,2BAAA;EL6tGV;EKpuGM;IAOI,0BAAA;ELguGV;EKvuGM;IAOI,gCAAA;ELmuGV;EK1uGM;IAOI,+BAAA;ELsuGV;EK7uGM;IAOI,6BAAA;ELyuGV;EKhvGM;IAOI,+BAAA;EL4uGV;EKnvGM;IAOI,6BAAA;EL+uGV;EKtvGM;IAOI,6BAAA;ELkvGV;EKzvGM;IAOI,2BAAA;ELqvGV;EK5vGM;IAOI,iCAAA;ELwvGV;EK/vGM;IAOI,gCAAA;EL2vGV;EKlwGM;IAOI,8BAAA;EL8vGV;EKrwGM;IAOI,gCAAA;ELiwGV;EKxwGM;IAOI,8BAAA;ELowGV;EK3wGM;IAOI,8BAAA;ELuwGV;EK9wGM;IAOI,yBAAA;EL0wGV;EKjxGM;IAOI,+BAAA;EL6wGV;EKpxGM;IAOI,8BAAA;ELgxGV;EKvxGM;IAOI,4BAAA;ELmxGV;EK1xGM;IAOI,8BAAA;ELsxGV;EK7xGM;IAOI,4BAAA;ELyxGV;EKhyGM;IAOI,4BAAA;EL4xGV;EKnyGM;IAOI,qBAAA;EL+xGV;EKtyGM;IAOI,2BAAA;ELkyGV;EKzyGM;IAOI,0BAAA;ELqyGV;EK5yGM;IAOI,wBAAA;ELwyGV;EK/yGM;IAOI,0BAAA;EL2yGV;EKlzGM;IAOI,wBAAA;EL8yGV;EKrzGM;IAOI,2BAAA;IAAA,0BAAA;ELkzGV;EKzzGM;IAOI,iCAAA;IAAA,gCAAA;ELszGV;EK7zGM;IAOI,gCAAA;IAAA,+BAAA;EL0zGV;EKj0GM;IAOI,8BAAA;IAAA,6BAAA;EL8zGV;EKr0GM;IAOI,gCAAA;IAAA,+BAAA;ELk0GV;EKz0GM;IAOI,8BAAA;IAAA,6BAAA;ELs0GV;EK70GM;IAOI,yBAAA;IAAA,4BAAA;EL00GV;EKj1GM;IAOI,+BAAA;IAAA,kCAAA;EL80GV;EKr1GM;IAOI,8BAAA;IAAA,iCAAA;ELk1GV;EKz1GM;IAOI,4BAAA;IAAA,+BAAA;ELs1GV;EK71GM;IAOI,8BAAA;IAAA,iCAAA;EL01GV;EKj2GM;IAOI,4BAAA;IAAA,+BAAA;EL81GV;EKr2GM;IAOI,yBAAA;ELi2GV;EKx2GM;IAOI,+BAAA;ELo2GV;EK32GM;IAOI,8BAAA;ELu2GV;EK92GM;IAOI,4BAAA;EL02GV;EKj3GM;IAOI,8BAAA;EL62GV;EKp3GM;IAOI,4BAAA;ELg3GV;EKv3GM;IAOI,2BAAA;ELm3GV;EK13GM;IAOI,iCAAA;ELs3GV;EK73GM;IAOI,gCAAA;ELy3GV;EKh4GM;IAOI,8BAAA;EL43GV;EKn4GM;IAOI,gCAAA;EL+3GV;EKt4GM;IAOI,8BAAA;ELk4GV;EKz4GM;IAOI,4BAAA;ELq4GV;EK54GM;IAOI,kCAAA;ELw4GV;EK/4GM;IAOI,iCAAA;EL24GV;EKl5GM;IAOI,+BAAA;EL84GV;EKr5GM;IAOI,iCAAA;ELi5GV;EKx5GM;IAOI,+BAAA;ELo5GV;EK35GM;IAOI,0BAAA;ELu5GV;EK95GM;IAOI,gCAAA;EL05GV;EKj6GM;IAOI,+BAAA;EL65GV;EKp6GM;IAOI,6BAAA;ELg6GV;EKv6GM;IAOI,+BAAA;ELm6GV;EK16GM;IAOI,6BAAA;ELs6GV;AACF;ACj7GI;EIGI;IAOI,0BAAA;EL26GV;EKl7GM;IAOI,gCAAA;EL86GV;EKr7GM;IAOI,yBAAA;ELi7GV;EKx7GM;IAOI,wBAAA;ELo7GV;EK37GM;IAOI,yBAAA;ELu7GV;EK97GM;IAOI,6BAAA;EL07GV;EKj8GM;IAOI,8BAAA;EL67GV;EKp8GM;IAOI,wBAAA;ELg8GV;EKv8GM;IAOI,+BAAA;ELm8GV;EK18GM;IAOI,wBAAA;ELs8GV;EK78GM;IAOI,yBAAA;ELy8GV;EKh9GM;IAOI,8BAAA;EL48GV;EKn9GM;IAOI,iCAAA;EL+8GV;EKt9GM;IAOI,sCAAA;ELk9GV;EKz9GM;IAOI,yCAAA;ELq9GV;EK59GM;IAOI,uBAAA;ELw9GV;EK/9GM;IAOI,uBAAA;EL29GV;EKl+GM;IAOI,yBAAA;EL89GV;EKr+GM;IAOI,yBAAA;ELi+GV;EKx+GM;IAOI,0BAAA;ELo+GV;EK3+GM;IAOI,4BAAA;ELu+GV;EK9+GM;IAOI,kCAAA;EL0+GV;EKj/GM;IAOI,sCAAA;EL6+GV;EKp/GM;IAOI,oCAAA;ELg/GV;EKv/GM;IAOI,kCAAA;ELm/GV;EK1/GM;IAOI,yCAAA;ELs/GV;EK7/GM;IAOI,wCAAA;ELy/GV;EKhgHM;IAOI,wCAAA;EL4/GV;EKngHM;IAOI,kCAAA;EL+/GV;EKtgHM;IAOI,gCAAA;ELkgHV;EKzgHM;IAOI,8BAAA;ELqgHV;EK5gHM;IAOI,gCAAA;ELwgHV;EK/gHM;IAOI,+BAAA;EL2gHV;EKlhHM;IAOI,oCAAA;EL8gHV;EKrhHM;IAOI,kCAAA;ELihHV;EKxhHM;IAOI,gCAAA;ELohHV;EK3hHM;IAOI,uCAAA;ELuhHV;EK9hHM;IAOI,sCAAA;EL0hHV;EKjiHM;IAOI,iCAAA;EL6hHV;EKpiHM;IAOI,2BAAA;ELgiHV;EKviHM;IAOI,iCAAA;ELmiHV;EK1iHM;IAOI,+BAAA;ELsiHV;EK7iHM;IAOI,6BAAA;ELyiHV;EKhjHM;IAOI,+BAAA;EL4iHV;EKnjHM;IAOI,8BAAA;EL+iHV;EKtjHM;IAOI,oBAAA;ELkjHV;EKzjHM;IAOI,mBAAA;ELqjHV;EK5jHM;IAOI,mBAAA;ELwjHV;EK/jHM;IAOI,mBAAA;EL2jHV;EKlkHM;IAOI,mBAAA;EL8jHV;EKrkHM;IAOI,mBAAA;ELikHV;EKxkHM;IAOI,mBAAA;ELokHV;EK3kHM;IAOI,mBAAA;ELukHV;EK9kHM;IAOI,oBAAA;EL0kHV;EKjlHM;IAOI,0BAAA;EL6kHV;EKplHM;IAOI,yBAAA;ELglHV;EKvlHM;IAOI,uBAAA;ELmlHV;EK1lHM;IAOI,yBAAA;ELslHV;EK7lHM;IAOI,uBAAA;ELylHV;EKhmHM;IAOI,uBAAA;EL4lHV;EKnmHM;IAOI,0BAAA;IAAA,yBAAA;ELgmHV;EKvmHM;IAOI,gCAAA;IAAA,+BAAA;ELomHV;EK3mHM;IAOI,+BAAA;IAAA,8BAAA;ELwmHV;EK/mHM;IAOI,6BAAA;IAAA,4BAAA;EL4mHV;EKnnHM;IAOI,+BAAA;IAAA,8BAAA;ELgnHV;EKvnHM;IAOI,6BAAA;IAAA,4BAAA;ELonHV;EK3nHM;IAOI,6BAAA;IAAA,4BAAA;ELwnHV;EK/nHM;IAOI,wBAAA;IAAA,2BAAA;EL4nHV;EKnoHM;IAOI,8BAAA;IAAA,iCAAA;ELgoHV;EKvoHM;IAOI,6BAAA;IAAA,gCAAA;ELooHV;EK3oHM;IAOI,2BAAA;IAAA,8BAAA;ELwoHV;EK/oHM;IAOI,6BAAA;IAAA,gCAAA;EL4oHV;EKnpHM;IAOI,2BAAA;IAAA,8BAAA;ELgpHV;EKvpHM;IAOI,2BAAA;IAAA,8BAAA;ELopHV;EK3pHM;IAOI,wBAAA;ELupHV;EK9pHM;IAOI,8BAAA;EL0pHV;EKjqHM;IAOI,6BAAA;EL6pHV;EKpqHM;IAOI,2BAAA;ELgqHV;EKvqHM;IAOI,6BAAA;ELmqHV;EK1qHM;IAOI,2BAAA;ELsqHV;EK7qHM;IAOI,2BAAA;ELyqHV;EKhrHM;IAOI,0BAAA;EL4qHV;EKnrHM;IAOI,gCAAA;EL+qHV;EKtrHM;IAOI,+BAAA;ELkrHV;EKzrHM;IAOI,6BAAA;ELqrHV;EK5rHM;IAOI,+BAAA;ELwrHV;EK/rHM;IAOI,6BAAA;EL2rHV;EKlsHM;IAOI,6BAAA;EL8rHV;EKrsHM;IAOI,2BAAA;ELisHV;EKxsHM;IAOI,iCAAA;ELosHV;EK3sHM;IAOI,gCAAA;ELusHV;EK9sHM;IAOI,8BAAA;EL0sHV;EKjtHM;IAOI,gCAAA;EL6sHV;EKptHM;IAOI,8BAAA;ELgtHV;EKvtHM;IAOI,8BAAA;ELmtHV;EK1tHM;IAOI,yBAAA;ELstHV;EK7tHM;IAOI,+BAAA;ELytHV;EKhuHM;IAOI,8BAAA;EL4tHV;EKnuHM;IAOI,4BAAA;EL+tHV;EKtuHM;IAOI,8BAAA;ELkuHV;EKzuHM;IAOI,4BAAA;ELquHV;EK5uHM;IAOI,4BAAA;ELwuHV;EK/uHM;IAOI,qBAAA;EL2uHV;EKlvHM;IAOI,2BAAA;EL8uHV;EKrvHM;IAOI,0BAAA;ELivHV;EKxvHM;IAOI,wBAAA;ELovHV;EK3vHM;IAOI,0BAAA;ELuvHV;EK9vHM;IAOI,wBAAA;EL0vHV;EKjwHM;IAOI,2BAAA;IAAA,0BAAA;EL8vHV;EKrwHM;IAOI,iCAAA;IAAA,gCAAA;ELkwHV;EKzwHM;IAOI,gCAAA;IAAA,+BAAA;ELswHV;EK7wHM;IAOI,8BAAA;IAAA,6BAAA;EL0wHV;EKjxHM;IAOI,gCAAA;IAAA,+BAAA;EL8wHV;EKrxHM;IAOI,8BAAA;IAAA,6BAAA;ELkxHV;EKzxHM;IAOI,yBAAA;IAAA,4BAAA;ELsxHV;EK7xHM;IAOI,+BAAA;IAAA,kCAAA;EL0xHV;EKjyHM;IAOI,8BAAA;IAAA,iCAAA;EL8xHV;EKryHM;IAOI,4BAAA;IAAA,+BAAA;ELkyHV;EKzyHM;IAOI,8BAAA;IAAA,iCAAA;ELsyHV;EK7yHM;IAOI,4BAAA;IAAA,+BAAA;EL0yHV;EKjzHM;IAOI,yBAAA;EL6yHV;EKpzHM;IAOI,+BAAA;ELgzHV;EKvzHM;IAOI,8BAAA;ELmzHV;EK1zHM;IAOI,4BAAA;ELszHV;EK7zHM;IAOI,8BAAA;ELyzHV;EKh0HM;IAOI,4BAAA;EL4zHV;EKn0HM;IAOI,2BAAA;EL+zHV;EKt0HM;IAOI,iCAAA;ELk0HV;EKz0HM;IAOI,gCAAA;ELq0HV;EK50HM;IAOI,8BAAA;ELw0HV;EK/0HM;IAOI,gCAAA;EL20HV;EKl1HM;IAOI,8BAAA;EL80HV;EKr1HM;IAOI,4BAAA;ELi1HV;EKx1HM;IAOI,kCAAA;ELo1HV;EK31HM;IAOI,iCAAA;ELu1HV;EK91HM;IAOI,+BAAA;EL01HV;EKj2HM;IAOI,iCAAA;EL61HV;EKp2HM;IAOI,+BAAA;ELg2HV;EKv2HM;IAOI,0BAAA;ELm2HV;EK12HM;IAOI,gCAAA;ELs2HV;EK72HM;IAOI,+BAAA;ELy2HV;EKh3HM;IAOI,6BAAA;EL42HV;EKn3HM;IAOI,+BAAA;EL+2HV;EKt3HM;IAOI,6BAAA;ELk3HV;AACF;AMt5HA;ED4BQ;IAOI,0BAAA;ELu3HV;EK93HM;IAOI,gCAAA;EL03HV;EKj4HM;IAOI,yBAAA;EL63HV;EKp4HM;IAOI,wBAAA;ELg4HV;EKv4HM;IAOI,yBAAA;ELm4HV;EK14HM;IAOI,6BAAA;ELs4HV;EK74HM;IAOI,8BAAA;ELy4HV;EKh5HM;IAOI,wBAAA;EL44HV;EKn5HM;IAOI,+BAAA;EL+4HV;EKt5HM;IAOI,wBAAA;ELk5HV;AACF", "file": "bootstrap-grid.css", "sourcesContent": ["@mixin bsBanner($file) {\r\n  /*!\r\n   * Bootstrap #{$file} v5.3.0-alpha1 (https://getbootstrap.com/)\r\n   * Copyright 2011-2023 The Bootstrap Authors\r\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\r\n   */\r\n}\r\n", "// Container widths\r\n//\r\n// Set the container width, and override it for fixed navbars in media queries.\r\n\r\n@if $enable-container-classes {\r\n  // Single container class with breakpoint max-widths\r\n  .container,\r\n  // 100% wide container at all breakpoints\r\n  .container-fluid {\r\n    @include make-container();\r\n  }\r\n\r\n  // Responsive containers that are 100% wide until a breakpoint\r\n  @each $breakpoint, $container-max-width in $container-max-widths {\r\n    .container-#{$breakpoint} {\r\n      @extend .container-fluid;\r\n    }\r\n\r\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\r\n      %responsive-container-#{$breakpoint} {\r\n        max-width: $container-max-width;\r\n      }\r\n\r\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\r\n      $extend-breakpoint: true;\r\n\r\n      @each $name, $width in $grid-breakpoints {\r\n        @if ($extend-breakpoint) {\r\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\r\n            @extend %responsive-container-#{$breakpoint};\r\n          }\r\n\r\n          // Once the current breakpoint is reached, stop extending\r\n          @if ($breakpoint == $name) {\r\n            $extend-breakpoint: false;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Container mixins\r\n\r\n@mixin make-container($gutter: $container-padding-x) {\r\n  --#{$prefix}gutter-x: #{$gutter};\r\n  --#{$prefix}gutter-y: 0;\r\n  width: 100%;\r\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\r\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\r\n  margin-right: auto;\r\n  margin-left: auto;\r\n}\r\n", "/*!\n * Bootstrap Grid v5.3.0-alpha1 (https://getbootstrap.com/)\n * Copyright 2011-2023 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n.container,\n.container-fluid,\n.container-xxl,\n.container-xl,\n.container-lg,\n.container-md,\n.container-sm {\n  --bs-gutter-x: 1.5rem;\n  --bs-gutter-y: 0;\n  width: 100%;\n  padding-right: calc(var(--bs-gutter-x) * 0.5);\n  padding-left: calc(var(--bs-gutter-x) * 0.5);\n  margin-right: auto;\n  margin-left: auto;\n}\n\n@media (min-width: 576px) {\n  .container-sm, .container {\n    max-width: 540px;\n  }\n}\n@media (min-width: 768px) {\n  .container-md, .container-sm, .container {\n    max-width: 720px;\n  }\n}\n@media (min-width: 992px) {\n  .container-lg, .container-md, .container-sm, .container {\n    max-width: 960px;\n  }\n}\n@media (min-width: 1200px) {\n  .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1140px;\n  }\n}\n@media (min-width: 1400px) {\n  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1320px;\n  }\n}\n:root {\n  --bs-breakpoint-xs: 0;\n  --bs-breakpoint-sm: 576px;\n  --bs-breakpoint-md: 768px;\n  --bs-breakpoint-lg: 992px;\n  --bs-breakpoint-xl: 1200px;\n  --bs-breakpoint-xxl: 1400px;\n}\n\n.row {\n  --bs-gutter-x: 1.5rem;\n  --bs-gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(-1 * var(--bs-gutter-y));\n  margin-right: calc(-0.5 * var(--bs-gutter-x));\n  margin-left: calc(-0.5 * var(--bs-gutter-x));\n}\n.row > * {\n  box-sizing: border-box;\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%;\n  padding-right: calc(var(--bs-gutter-x) * 0.5);\n  padding-left: calc(var(--bs-gutter-x) * 0.5);\n  margin-top: var(--bs-gutter-y);\n}\n\n.col {\n  flex: 1 0 0%;\n}\n\n.row-cols-auto > * {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.row-cols-1 > * {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.row-cols-2 > * {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.row-cols-3 > * {\n  flex: 0 0 auto;\n  width: 33.3333333333%;\n}\n\n.row-cols-4 > * {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.row-cols-5 > * {\n  flex: 0 0 auto;\n  width: 20%;\n}\n\n.row-cols-6 > * {\n  flex: 0 0 auto;\n  width: 16.6666666667%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.col-1 {\n  flex: 0 0 auto;\n  width: 8.33333333%;\n}\n\n.col-2 {\n  flex: 0 0 auto;\n  width: 16.66666667%;\n}\n\n.col-3 {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 auto;\n  width: 33.33333333%;\n}\n\n.col-5 {\n  flex: 0 0 auto;\n  width: 41.66666667%;\n}\n\n.col-6 {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 auto;\n  width: 58.33333333%;\n}\n\n.col-8 {\n  flex: 0 0 auto;\n  width: 66.66666667%;\n}\n\n.col-9 {\n  flex: 0 0 auto;\n  width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 auto;\n  width: 83.33333333%;\n}\n\n.col-11 {\n  flex: 0 0 auto;\n  width: 91.66666667%;\n}\n\n.col-12 {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.offset-1 {\n  margin-left: 8.33333333%;\n}\n\n.offset-2 {\n  margin-left: 16.66666667%;\n}\n\n.offset-3 {\n  margin-left: 25%;\n}\n\n.offset-4 {\n  margin-left: 33.33333333%;\n}\n\n.offset-5 {\n  margin-left: 41.66666667%;\n}\n\n.offset-6 {\n  margin-left: 50%;\n}\n\n.offset-7 {\n  margin-left: 58.33333333%;\n}\n\n.offset-8 {\n  margin-left: 66.66666667%;\n}\n\n.offset-9 {\n  margin-left: 75%;\n}\n\n.offset-10 {\n  margin-left: 83.33333333%;\n}\n\n.offset-11 {\n  margin-left: 91.66666667%;\n}\n\n.g-0,\n.gx-0 {\n  --bs-gutter-x: 0;\n}\n\n.g-0,\n.gy-0 {\n  --bs-gutter-y: 0;\n}\n\n.g-1,\n.gx-1 {\n  --bs-gutter-x: 0.25rem;\n}\n\n.g-1,\n.gy-1 {\n  --bs-gutter-y: 0.25rem;\n}\n\n.g-2,\n.gx-2 {\n  --bs-gutter-x: 0.5rem;\n}\n\n.g-2,\n.gy-2 {\n  --bs-gutter-y: 0.5rem;\n}\n\n.g-3,\n.gx-3 {\n  --bs-gutter-x: 1rem;\n}\n\n.g-3,\n.gy-3 {\n  --bs-gutter-y: 1rem;\n}\n\n.g-4,\n.gx-4 {\n  --bs-gutter-x: 1.5rem;\n}\n\n.g-4,\n.gy-4 {\n  --bs-gutter-y: 1.5rem;\n}\n\n.g-5,\n.gx-5 {\n  --bs-gutter-x: 3rem;\n}\n\n.g-5,\n.gy-5 {\n  --bs-gutter-y: 3rem;\n}\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex: 1 0 0%;\n  }\n  .row-cols-sm-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-sm-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-sm-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-sm-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n  .row-cols-sm-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-sm-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-sm-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-sm-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-sm-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-sm-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-sm-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-sm-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-sm-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-sm-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-sm-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-sm-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-sm-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-sm-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-sm-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-sm-0 {\n    margin-left: 0;\n  }\n  .offset-sm-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-sm-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-sm-3 {\n    margin-left: 25%;\n  }\n  .offset-sm-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-sm-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-sm-6 {\n    margin-left: 50%;\n  }\n  .offset-sm-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-sm-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-sm-9 {\n    margin-left: 75%;\n  }\n  .offset-sm-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-sm-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-sm-0,\n  .gx-sm-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-sm-0,\n  .gy-sm-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-sm-1,\n  .gx-sm-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n  .g-sm-1,\n  .gy-sm-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n  .g-sm-2,\n  .gx-sm-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n  .g-sm-2,\n  .gy-sm-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n  .g-sm-3,\n  .gx-sm-3 {\n    --bs-gutter-x: 1rem;\n  }\n  .g-sm-3,\n  .gy-sm-3 {\n    --bs-gutter-y: 1rem;\n  }\n  .g-sm-4,\n  .gx-sm-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n  .g-sm-4,\n  .gy-sm-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n  .g-sm-5,\n  .gx-sm-5 {\n    --bs-gutter-x: 3rem;\n  }\n  .g-sm-5,\n  .gy-sm-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 768px) {\n  .col-md {\n    flex: 1 0 0%;\n  }\n  .row-cols-md-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-md-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-md-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-md-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n  .row-cols-md-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-md-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-md-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-md-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-md-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-md-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-md-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-md-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-md-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-md-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-md-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-md-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-md-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-md-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-md-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-md-0 {\n    margin-left: 0;\n  }\n  .offset-md-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-md-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-md-3 {\n    margin-left: 25%;\n  }\n  .offset-md-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-md-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-md-6 {\n    margin-left: 50%;\n  }\n  .offset-md-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-md-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-md-9 {\n    margin-left: 75%;\n  }\n  .offset-md-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-md-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-md-0,\n  .gx-md-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-md-0,\n  .gy-md-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-md-1,\n  .gx-md-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n  .g-md-1,\n  .gy-md-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n  .g-md-2,\n  .gx-md-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n  .g-md-2,\n  .gy-md-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n  .g-md-3,\n  .gx-md-3 {\n    --bs-gutter-x: 1rem;\n  }\n  .g-md-3,\n  .gy-md-3 {\n    --bs-gutter-y: 1rem;\n  }\n  .g-md-4,\n  .gx-md-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n  .g-md-4,\n  .gy-md-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n  .g-md-5,\n  .gx-md-5 {\n    --bs-gutter-x: 3rem;\n  }\n  .g-md-5,\n  .gy-md-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 992px) {\n  .col-lg {\n    flex: 1 0 0%;\n  }\n  .row-cols-lg-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-lg-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-lg-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-lg-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n  .row-cols-lg-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-lg-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-lg-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-lg-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-lg-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-lg-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-lg-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-lg-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-lg-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-lg-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-lg-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-lg-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-lg-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-lg-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-lg-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-lg-0 {\n    margin-left: 0;\n  }\n  .offset-lg-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-lg-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-lg-3 {\n    margin-left: 25%;\n  }\n  .offset-lg-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-lg-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-lg-6 {\n    margin-left: 50%;\n  }\n  .offset-lg-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-lg-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-lg-9 {\n    margin-left: 75%;\n  }\n  .offset-lg-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-lg-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-lg-0,\n  .gx-lg-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-lg-0,\n  .gy-lg-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-lg-1,\n  .gx-lg-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n  .g-lg-1,\n  .gy-lg-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n  .g-lg-2,\n  .gx-lg-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n  .g-lg-2,\n  .gy-lg-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n  .g-lg-3,\n  .gx-lg-3 {\n    --bs-gutter-x: 1rem;\n  }\n  .g-lg-3,\n  .gy-lg-3 {\n    --bs-gutter-y: 1rem;\n  }\n  .g-lg-4,\n  .gx-lg-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n  .g-lg-4,\n  .gy-lg-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n  .g-lg-5,\n  .gx-lg-5 {\n    --bs-gutter-x: 3rem;\n  }\n  .g-lg-5,\n  .gy-lg-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 1200px) {\n  .col-xl {\n    flex: 1 0 0%;\n  }\n  .row-cols-xl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-xl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-xl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-xl-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n  .row-cols-xl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-xl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-xl-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-xl-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-xl-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-xl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-xl-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-xl-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-xl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-xl-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-xl-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-xl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-xl-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-xl-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-xl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-xl-0 {\n    margin-left: 0;\n  }\n  .offset-xl-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-xl-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-xl-3 {\n    margin-left: 25%;\n  }\n  .offset-xl-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-xl-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-xl-6 {\n    margin-left: 50%;\n  }\n  .offset-xl-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-xl-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-xl-9 {\n    margin-left: 75%;\n  }\n  .offset-xl-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-xl-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-xl-0,\n  .gx-xl-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-xl-0,\n  .gy-xl-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-xl-1,\n  .gx-xl-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n  .g-xl-1,\n  .gy-xl-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n  .g-xl-2,\n  .gx-xl-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n  .g-xl-2,\n  .gy-xl-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n  .g-xl-3,\n  .gx-xl-3 {\n    --bs-gutter-x: 1rem;\n  }\n  .g-xl-3,\n  .gy-xl-3 {\n    --bs-gutter-y: 1rem;\n  }\n  .g-xl-4,\n  .gx-xl-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n  .g-xl-4,\n  .gy-xl-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n  .g-xl-5,\n  .gx-xl-5 {\n    --bs-gutter-x: 3rem;\n  }\n  .g-xl-5,\n  .gy-xl-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 1400px) {\n  .col-xxl {\n    flex: 1 0 0%;\n  }\n  .row-cols-xxl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-xxl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-xxl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-xxl-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n  .row-cols-xxl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-xxl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-xxl-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n  .col-xxl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-xxl-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-xxl-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-xxl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-xxl-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-xxl-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-xxl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-xxl-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-xxl-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-xxl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-xxl-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-xxl-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-xxl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-xxl-0 {\n    margin-left: 0;\n  }\n  .offset-xxl-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-xxl-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-xxl-3 {\n    margin-left: 25%;\n  }\n  .offset-xxl-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-xxl-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-xxl-6 {\n    margin-left: 50%;\n  }\n  .offset-xxl-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-xxl-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-xxl-9 {\n    margin-left: 75%;\n  }\n  .offset-xxl-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-xxl-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-xxl-0,\n  .gx-xxl-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-xxl-0,\n  .gy-xxl-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-xxl-1,\n  .gx-xxl-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n  .g-xxl-1,\n  .gy-xxl-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n  .g-xxl-2,\n  .gx-xxl-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n  .g-xxl-2,\n  .gy-xxl-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n  .g-xxl-3,\n  .gx-xxl-3 {\n    --bs-gutter-x: 1rem;\n  }\n  .g-xxl-3,\n  .gy-xxl-3 {\n    --bs-gutter-y: 1rem;\n  }\n  .g-xxl-4,\n  .gx-xxl-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n  .g-xxl-4,\n  .gy-xxl-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n  .g-xxl-5,\n  .gx-xxl-5 {\n    --bs-gutter-x: 3rem;\n  }\n  .g-xxl-5,\n  .gy-xxl-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.25rem !important;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n.me-3 {\n  margin-right: 1rem !important;\n}\n\n.me-4 {\n  margin-right: 1.5rem !important;\n}\n\n.me-5 {\n  margin-right: 3rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.25rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.5rem !important;\n}\n\n.ms-3 {\n  margin-left: 1rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.5rem !important;\n}\n\n.ms-5 {\n  margin-left: 3rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pe-3 {\n  padding-right: 1rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pe-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.25rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.5rem !important;\n}\n\n.ps-3 {\n  padding-left: 1rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.5rem !important;\n}\n\n.ps-5 {\n  padding-left: 3rem !important;\n}\n\n@media (min-width: 576px) {\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-grid {\n    display: grid !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-sm-none {\n    display: none !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n  .order-sm-first {\n    order: -1 !important;\n  }\n  .order-sm-0 {\n    order: 0 !important;\n  }\n  .order-sm-1 {\n    order: 1 !important;\n  }\n  .order-sm-2 {\n    order: 2 !important;\n  }\n  .order-sm-3 {\n    order: 3 !important;\n  }\n  .order-sm-4 {\n    order: 4 !important;\n  }\n  .order-sm-5 {\n    order: 5 !important;\n  }\n  .order-sm-last {\n    order: 6 !important;\n  }\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n  .me-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-sm-3 {\n    margin-right: 1rem !important;\n  }\n  .me-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-sm-5 {\n    margin-right: 3rem !important;\n  }\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n  .ms-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-sm-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-sm-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pe-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-sm-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-sm-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n  .ps-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-sm-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-sm-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 768px) {\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-grid {\n    display: grid !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-md-none {\n    display: none !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n  .order-md-first {\n    order: -1 !important;\n  }\n  .order-md-0 {\n    order: 0 !important;\n  }\n  .order-md-1 {\n    order: 1 !important;\n  }\n  .order-md-2 {\n    order: 2 !important;\n  }\n  .order-md-3 {\n    order: 3 !important;\n  }\n  .order-md-4 {\n    order: 4 !important;\n  }\n  .order-md-5 {\n    order: 5 !important;\n  }\n  .order-md-last {\n    order: 6 !important;\n  }\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n  .me-md-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-md-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-md-3 {\n    margin-right: 1rem !important;\n  }\n  .me-md-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-md-5 {\n    margin-right: 3rem !important;\n  }\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n  .ms-md-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-md-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-md-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-md-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-md-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n  .pe-md-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-md-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-md-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-md-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-md-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n  .ps-md-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-md-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-md-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-md-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-md-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 992px) {\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-grid {\n    display: grid !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-lg-none {\n    display: none !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n  .order-lg-first {\n    order: -1 !important;\n  }\n  .order-lg-0 {\n    order: 0 !important;\n  }\n  .order-lg-1 {\n    order: 1 !important;\n  }\n  .order-lg-2 {\n    order: 2 !important;\n  }\n  .order-lg-3 {\n    order: 3 !important;\n  }\n  .order-lg-4 {\n    order: 4 !important;\n  }\n  .order-lg-5 {\n    order: 5 !important;\n  }\n  .order-lg-last {\n    order: 6 !important;\n  }\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n  .me-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-lg-3 {\n    margin-right: 1rem !important;\n  }\n  .me-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-lg-5 {\n    margin-right: 3rem !important;\n  }\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n  .ms-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-lg-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-lg-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pe-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-lg-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-lg-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n  .ps-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-lg-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-lg-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 1200px) {\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-grid {\n    display: grid !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xl-none {\n    display: none !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xl-first {\n    order: -1 !important;\n  }\n  .order-xl-0 {\n    order: 0 !important;\n  }\n  .order-xl-1 {\n    order: 1 !important;\n  }\n  .order-xl-2 {\n    order: 2 !important;\n  }\n  .order-xl-3 {\n    order: 3 !important;\n  }\n  .order-xl-4 {\n    order: 4 !important;\n  }\n  .order-xl-5 {\n    order: 5 !important;\n  }\n  .order-xl-last {\n    order: 6 !important;\n  }\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-xl-3 {\n    margin-right: 1rem !important;\n  }\n  .me-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-xl-5 {\n    margin-right: 3rem !important;\n  }\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-xl-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-xl-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-xl-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-xl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-xl-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-xl-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 1400px) {\n  .d-xxl-inline {\n    display: inline !important;\n  }\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xxl-block {\n    display: block !important;\n  }\n  .d-xxl-grid {\n    display: grid !important;\n  }\n  .d-xxl-table {\n    display: table !important;\n  }\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xxl-flex {\n    display: flex !important;\n  }\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xxl-none {\n    display: none !important;\n  }\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xxl-first {\n    order: -1 !important;\n  }\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n  .order-xxl-last {\n    order: 6 !important;\n  }\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n  .m-xxl-1 {\n    margin: 0.25rem !important;\n  }\n  .m-xxl-2 {\n    margin: 0.5rem !important;\n  }\n  .m-xxl-3 {\n    margin: 1rem !important;\n  }\n  .m-xxl-4 {\n    margin: 1.5rem !important;\n  }\n  .m-xxl-5 {\n    margin: 3rem !important;\n  }\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xxl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-xxl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-xxl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-xxl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-xxl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xxl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-xxl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-xxl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-xxl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-xxl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xxl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-xxl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-xxl-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-xxl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-xxl-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xxl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-xxl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-xxl-3 {\n    margin-right: 1rem !important;\n  }\n  .me-xxl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-xxl-5 {\n    margin-right: 3rem !important;\n  }\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xxl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-xxl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-xxl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-xxl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-xxl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xxl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-xxl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-xxl-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-xxl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-xxl-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n  .p-xxl-1 {\n    padding: 0.25rem !important;\n  }\n  .p-xxl-2 {\n    padding: 0.5rem !important;\n  }\n  .p-xxl-3 {\n    padding: 1rem !important;\n  }\n  .p-xxl-4 {\n    padding: 1.5rem !important;\n  }\n  .p-xxl-5 {\n    padding: 3rem !important;\n  }\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xxl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-xxl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-xxl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-xxl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-xxl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xxl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-xxl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-xxl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-xxl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-xxl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xxl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-xxl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-xxl-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-xxl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-xxl-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xxl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-xxl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-xxl-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-xxl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-xxl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xxl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-xxl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-xxl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-xxl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-xxl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xxl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-xxl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-xxl-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-xxl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-xxl-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-grid {\n    display: grid !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=bootstrap-grid.css.map */\n", "// Breakpoint viewport sizes and media queries.\r\n//\r\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\r\n//\r\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\r\n//\r\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\r\n\r\n// Name of the next breakpoint, or null for the last breakpoint.\r\n//\r\n//    >> breakpoint-next(sm)\r\n//    md\r\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    md\r\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\r\n//    md\r\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\r\n  $n: index($breakpoint-names, $name);\r\n  @if not $n {\r\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\r\n  }\r\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\r\n}\r\n\r\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\r\n//\r\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    576px\r\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\r\n  $min: map-get($breakpoints, $name);\r\n  @return if($min != 0, $min, null);\r\n}\r\n\r\n// Maximum breakpoint width.\r\n// The maximum value is reduced by 0.02px to work around the limitations of\r\n// `min-` and `max-` prefixes and viewports with fractional widths.\r\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\r\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\r\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\r\n//\r\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    767.98px\r\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\r\n  $max: map-get($breakpoints, $name);\r\n  @return if($max and $max > 0, $max - .02, null);\r\n}\r\n\r\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\r\n// Useful for making responsive utilities.\r\n//\r\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"\"  (Returns a blank string)\r\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"-sm\"\r\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\r\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\r\n}\r\n\r\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\r\n// Makes the @content apply to the given breakpoint and wider.\r\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($name, $breakpoints);\r\n  @if $min {\r\n    @media (min-width: $min) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\r\n// Makes the @content apply to the given breakpoint and narrower.\r\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\r\n  $max: breakpoint-max($name, $breakpoints);\r\n  @if $max {\r\n    @media (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media that spans multiple breakpoint widths.\r\n// Makes the @content apply between the min and max breakpoints\r\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($lower, $breakpoints);\r\n  $max: breakpoint-max($upper, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($lower, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($upper, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Media between the breakpoint's minimum and maximum widths.\r\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\r\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\r\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\r\n  $min:  breakpoint-min($name, $breakpoints);\r\n  $next: breakpoint-next($name, $breakpoints);\r\n  $max:  breakpoint-max($next, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($name, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($next, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n", "// Variables\r\n//\r\n// Variables should follow the `$component-state-property-size` formula for\r\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\r\n\r\n// Color system\r\n\r\n// scss-docs-start gray-color-variables\r\n$white:    #fff !default;\r\n$gray-base: #3c4b64 !default;\r\n$gray-100: #ececec !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #393939 !default;\r\n$black:    #000 !default;\r\n// scss-docs-end gray-color-variables\r\n\r\n// fusv-disable\r\n// scss-docs-start gray-colors-map\r\n$grays: (\r\n  \"100\": $gray-100,\r\n  \"200\": $gray-200,\r\n  \"300\": $gray-300,\r\n  \"400\": $gray-400,\r\n  \"500\": $gray-500,\r\n  \"600\": $gray-600,\r\n  \"700\": $gray-700,\r\n  \"800\": $gray-800,\r\n  \"900\": $gray-900\r\n) !default;\r\n// scss-docs-end gray-colors-map\r\n// fusv-enable\r\n\r\n$high-emphasis:            rgba(shift-color($gray-base, +26%), .95) !default;\r\n$medium-emphasis:          rgba(shift-color($gray-base, +26%), .681) !default;\r\n$disabled:                 rgba(shift-color($gray-base, +26%), .38) !default;\r\n\r\n$high-emphasis-inverse-new:    rgba($white, .87) !default;\r\n$medium-emphasis-inverse-new:  rgba($white, .6) !default;\r\n$disabled-inverse-new:         rgba($white, .38) !default;\r\n\r\n// scss-docs-start color-variables\r\n$blue:    #1a73b8 !default;\r\n$indigo:  #6610f2 !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #d63384 !default;\r\n$red:     #ea0b16 !default;\r\n$orange:  #fd7e14 !default;\r\n$yellow:  #f0a205 !default;\r\n$green:   #005c29 !default; // buymed color\r\n$green-bold:  #00B453 !default; // buymed color\r\n$green-light: #d1e7bf !default; // buymed color\r\n$green-btn-hover: #0f280b !default; // buymed color\r\n$teal:    #20c997 !default;\r\n$cyan:    #175cd3 !default;\r\n\r\n// scss-docs-end color-variables\r\n\r\n// scss-docs-start colors-map\r\n$colors: (\r\n  \"blue\":       $blue,\r\n  \"indigo\":     $indigo,\r\n  \"purple\":     $purple,\r\n  \"pink\":       $pink,\r\n  \"red\":        $red,\r\n  \"orange\":     $orange,\r\n  \"yellow\":     $yellow,\r\n  \"green\":      $green,\r\n  \"teal\":       $teal,\r\n  \"cyan\":       $cyan,\r\n  \"black\":      $black,\r\n  \"white\":      $white,\r\n  \"gray\":       $gray-600,\r\n  \"gray-dark\":  $gray-800\r\n) !default;\r\n// scss-docs-end colors-map\r\n\r\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\r\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\r\n$min-contrast-ratio:   4.5 !default;\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark:      $black !default;\r\n$color-contrast-light:     $white !default;\r\n\r\n// fusv-disable\r\n$blue-100: tint-color($blue, 80%) !default;\r\n$blue-200: tint-color($blue, 60%) !default;\r\n$blue-300: tint-color($blue, 40%) !default;\r\n$blue-400: tint-color($blue, 20%) !default;\r\n$blue-500: $blue !default;\r\n$blue-600: shade-color($blue, 20%) !default;\r\n$blue-700: shade-color($blue, 40%) !default;\r\n$blue-800: shade-color($blue, 60%) !default;\r\n$blue-900: shade-color($blue, 80%) !default;\r\n\r\n$indigo-100: tint-color($indigo, 80%) !default;\r\n$indigo-200: tint-color($indigo, 60%) !default;\r\n$indigo-300: tint-color($indigo, 40%) !default;\r\n$indigo-400: tint-color($indigo, 20%) !default;\r\n$indigo-500: $indigo !default;\r\n$indigo-600: shade-color($indigo, 20%) !default;\r\n$indigo-700: shade-color($indigo, 40%) !default;\r\n$indigo-800: shade-color($indigo, 60%) !default;\r\n$indigo-900: shade-color($indigo, 80%) !default;\r\n\r\n$purple-100: tint-color($purple, 80%) !default;\r\n$purple-200: tint-color($purple, 60%) !default;\r\n$purple-300: tint-color($purple, 40%) !default;\r\n$purple-400: tint-color($purple, 20%) !default;\r\n$purple-500: $purple !default;\r\n$purple-600: shade-color($purple, 20%) !default;\r\n$purple-700: shade-color($purple, 40%) !default;\r\n$purple-800: shade-color($purple, 60%) !default;\r\n$purple-900: shade-color($purple, 80%) !default;\r\n\r\n$pink-100: tint-color($pink, 80%) !default;\r\n$pink-200: tint-color($pink, 60%) !default;\r\n$pink-300: tint-color($pink, 40%) !default;\r\n$pink-400: tint-color($pink, 20%) !default;\r\n$pink-500: $pink !default;\r\n$pink-600: shade-color($pink, 20%) !default;\r\n$pink-700: shade-color($pink, 40%) !default;\r\n$pink-800: shade-color($pink, 60%) !default;\r\n$pink-900: shade-color($pink, 80%) !default;\r\n\r\n$red-100: tint-color($red, 80%) !default;\r\n$red-200: tint-color($red, 60%) !default;\r\n$red-300: tint-color($red, 40%) !default;\r\n$red-400: tint-color($red, 20%) !default;\r\n$red-500: $red !default;\r\n$red-600: shade-color($red, 20%) !default;\r\n$red-700: shade-color($red, 40%) !default;\r\n$red-800: shade-color($red, 60%) !default;\r\n$red-900: shade-color($red, 80%) !default;\r\n\r\n$orange-100: tint-color($orange, 80%) !default;\r\n$orange-200: tint-color($orange, 60%) !default;\r\n$orange-300: tint-color($orange, 40%) !default;\r\n$orange-400: tint-color($orange, 20%) !default;\r\n$orange-500: $orange !default;\r\n$orange-600: shade-color($orange, 20%) !default;\r\n$orange-700: shade-color($orange, 40%) !default;\r\n$orange-800: shade-color($orange, 60%) !default;\r\n$orange-900: shade-color($orange, 80%) !default;\r\n\r\n$yellow-100: tint-color($yellow, 80%) !default;\r\n$yellow-200: tint-color($yellow, 60%) !default;\r\n$yellow-300: tint-color($yellow, 40%) !default;\r\n$yellow-400: tint-color($yellow, 20%) !default;\r\n$yellow-500: $yellow !default;\r\n$yellow-600: shade-color($yellow, 20%) !default;\r\n$yellow-700: shade-color($yellow, 40%) !default;\r\n$yellow-800: shade-color($yellow, 60%) !default;\r\n$yellow-900: shade-color($yellow, 80%) !default;\r\n\r\n$green-100: tint-color($green, 80%) !default;\r\n$green-200: tint-color($green, 60%) !default;\r\n$green-300: tint-color($green, 40%) !default;\r\n$green-400: tint-color($green, 20%) !default;\r\n$green-500: $green !default;\r\n$green-600: shade-color($green, 20%) !default;\r\n$green-700: shade-color($green, 40%) !default;\r\n$green-800: shade-color($green, 60%) !default;\r\n$green-900: shade-color($green, 80%) !default;\r\n\r\n$teal-100: tint-color($teal, 80%) !default;\r\n$teal-200: tint-color($teal, 60%) !default;\r\n$teal-300: tint-color($teal, 40%) !default;\r\n$teal-400: tint-color($teal, 20%) !default;\r\n$teal-500: $teal !default;\r\n$teal-600: shade-color($teal, 20%) !default;\r\n$teal-700: shade-color($teal, 40%) !default;\r\n$teal-800: shade-color($teal, 60%) !default;\r\n$teal-900: shade-color($teal, 80%) !default;\r\n\r\n$cyan-100: tint-color($cyan, 80%) !default;\r\n$cyan-200: tint-color($cyan, 60%) !default;\r\n$cyan-300: tint-color($cyan, 40%) !default;\r\n$cyan-400: tint-color($cyan, 20%) !default;\r\n$cyan-500: $cyan !default;\r\n$cyan-600: shade-color($cyan, 20%) !default;\r\n$cyan-700: shade-color($cyan, 40%) !default;\r\n$cyan-800: shade-color($cyan, 60%) !default;\r\n$cyan-900: shade-color($cyan, 80%) !default;\r\n\r\n$blues: (\r\n  \"blue-100\": $blue-100,\r\n  \"blue-200\": $blue-200,\r\n  \"blue-300\": $blue-300,\r\n  \"blue-400\": $blue-400,\r\n  \"blue-500\": $blue-500,\r\n  \"blue-600\": $blue-600,\r\n  \"blue-700\": $blue-700,\r\n  \"blue-800\": $blue-800,\r\n  \"blue-900\": $blue-900\r\n) !default;\r\n\r\n$indigos: (\r\n  \"indigo-100\": $indigo-100,\r\n  \"indigo-200\": $indigo-200,\r\n  \"indigo-300\": $indigo-300,\r\n  \"indigo-400\": $indigo-400,\r\n  \"indigo-500\": $indigo-500,\r\n  \"indigo-600\": $indigo-600,\r\n  \"indigo-700\": $indigo-700,\r\n  \"indigo-800\": $indigo-800,\r\n  \"indigo-900\": $indigo-900\r\n) !default;\r\n\r\n$purples: (\r\n  \"purple-100\": $purple-100,\r\n  \"purple-200\": $purple-200,\r\n  \"purple-300\": $purple-300,\r\n  \"purple-400\": $purple-400,\r\n  \"purple-500\": $purple-500,\r\n  \"purple-600\": $purple-600,\r\n  \"purple-700\": $purple-700,\r\n  \"purple-800\": $purple-800,\r\n  \"purple-900\": $purple-900\r\n) !default;\r\n\r\n$pinks: (\r\n  \"pink-100\": $pink-100,\r\n  \"pink-200\": $pink-200,\r\n  \"pink-300\": $pink-300,\r\n  \"pink-400\": $pink-400,\r\n  \"pink-500\": $pink-500,\r\n  \"pink-600\": $pink-600,\r\n  \"pink-700\": $pink-700,\r\n  \"pink-800\": $pink-800,\r\n  \"pink-900\": $pink-900\r\n) !default;\r\n\r\n$reds: (\r\n  \"red-100\": $red-100,\r\n  \"red-200\": $red-200,\r\n  \"red-300\": $red-300,\r\n  \"red-400\": $red-400,\r\n  \"red-500\": $red-500,\r\n  \"red-600\": $red-600,\r\n  \"red-700\": $red-700,\r\n  \"red-800\": $red-800,\r\n  \"red-900\": $red-900\r\n) !default;\r\n\r\n$oranges: (\r\n  \"orange-100\": $orange-100,\r\n  \"orange-200\": $orange-200,\r\n  \"orange-300\": $orange-300,\r\n  \"orange-400\": $orange-400,\r\n  \"orange-500\": $orange-500,\r\n  \"orange-600\": $orange-600,\r\n  \"orange-700\": $orange-700,\r\n  \"orange-800\": $orange-800,\r\n  \"orange-900\": $orange-900\r\n) !default;\r\n\r\n$yellows: (\r\n  \"yellow-100\": $yellow-100,\r\n  \"yellow-200\": $yellow-200,\r\n  \"yellow-300\": $yellow-300,\r\n  \"yellow-400\": $yellow-400,\r\n  \"yellow-500\": $yellow-500,\r\n  \"yellow-600\": $yellow-600,\r\n  \"yellow-700\": $yellow-700,\r\n  \"yellow-800\": $yellow-800,\r\n  \"yellow-900\": $yellow-900\r\n) !default;\r\n\r\n$greens: (\r\n  \"green-100\": $green-100,\r\n  \"green-200\": $green-200,\r\n  \"green-300\": $green-300,\r\n  \"green-400\": $green-400,\r\n  \"green-500\": $green-500,\r\n  \"green-600\": $green-600,\r\n  \"green-700\": $green-700,\r\n  \"green-800\": $green-800,\r\n  \"green-900\": $green-900\r\n) !default;\r\n\r\n$teals: (\r\n  \"teal-100\": $teal-100,\r\n  \"teal-200\": $teal-200,\r\n  \"teal-300\": $teal-300,\r\n  \"teal-400\": $teal-400,\r\n  \"teal-500\": $teal-500,\r\n  \"teal-600\": $teal-600,\r\n  \"teal-700\": $teal-700,\r\n  \"teal-800\": $teal-800,\r\n  \"teal-900\": $teal-900\r\n) !default;\r\n\r\n$cyans: (\r\n  \"cyan-100\": $cyan-100,\r\n  \"cyan-200\": $cyan-200,\r\n  \"cyan-300\": $cyan-300,\r\n  \"cyan-400\": $cyan-400,\r\n  \"cyan-500\": $cyan-500,\r\n  \"cyan-600\": $cyan-600,\r\n  \"cyan-700\": $cyan-700,\r\n  \"cyan-800\": $cyan-800,\r\n  \"cyan-900\": $cyan-900\r\n) !default;\r\n// fusv-enable\r\n\r\n// scss-docs-start theme-color-variables\r\n$primary:       $green !default;\r\n$secondary:     $green-light !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         $gray-100 !default;\r\n$dark:          $gray-900 !default;\r\n// scss-docs-end theme-color-variables\r\n\r\n// hover variables\r\n$primary-bg-hover:       #0f280b !default;\r\n$secondary-bg-hover:     #00563f !default;\r\n$success-bg-hover:       #0f280b !default;\r\n$info-bg-hover:          #1b458d !default;\r\n$warning-bg-hover:       #dd8002 !default;\r\n$danger-bg-hover:        #9a1e12 !default;\r\n$light-bg-hover:         #dcdbdb !default;\r\n$dark-bg-hover:          black !default;\r\n\r\n// scss-docs-start theme-colors-map\r\n$theme-colors: (\r\n  \"primary\":    $primary,\r\n  \"secondary\":  $secondary,\r\n  \"success\":    $success,\r\n  \"info\":       $info,\r\n  \"warning\":    $warning,\r\n  \"danger\":     $danger,\r\n  \"light\":      $light,\r\n  \"dark\":       $dark\r\n) !default;\r\n// scss-docs-end theme-colors-map\r\n\r\n// scss-docs-start theme-text-variables\r\n$primary-text-emphasis:   shade-color($primary, 60%) !default;\r\n$secondary-text-emphasis: shade-color($secondary, 60%) !default;\r\n$success-text-emphasis:   shade-color($success, 60%) !default;\r\n$info-text-emphasis:      shade-color($info, 60%) !default;\r\n$warning-text-emphasis:   shade-color($warning, 60%) !default;\r\n$danger-text-emphasis:    shade-color($danger, 60%) !default;\r\n$light-text-emphasis:     $gray-700 !default;\r\n$dark-text-emphasis:      $gray-700 !default;\r\n// scss-docs-end theme-text-variables\r\n\r\n// scss-docs-start theme-bg-subtle-variables\r\n$primary-bg-subtle:       tint-color($primary, 80%) !default;\r\n$secondary-bg-subtle:     tint-color($secondary, 80%) !default;\r\n$success-bg-subtle:       tint-color($success, 80%) !default;\r\n$info-bg-subtle:          tint-color($info, 80%) !default;\r\n$warning-bg-subtle:       tint-color($warning, 80%) !default;\r\n$danger-bg-subtle:        tint-color($danger, 80%) !default;\r\n$light-bg-subtle:         mix($gray-100, $white) !default;\r\n$dark-bg-subtle:          $gray-400 !default;\r\n// scss-docs-end theme-bg-subtle-variables\r\n\r\n// scss-docs-start theme-border-subtle-variables\r\n$primary-border-subtle:   tint-color($primary, 60%) !default;\r\n$secondary-border-subtle: tint-color($secondary, 60%) !default;\r\n$success-border-subtle:   tint-color($success, 60%) !default;\r\n$info-border-subtle:      tint-color($info, 60%) !default;\r\n$warning-border-subtle:   tint-color($warning, 60%) !default;\r\n$danger-border-subtle:    tint-color($danger, 60%) !default;\r\n$light-border-subtle:     $gray-200 !default;\r\n$dark-border-subtle:      $gray-500 !default;\r\n// scss-docs-end theme-border-subtle-variables\r\n\r\n// Characters which are escaped by the escape-svg function\r\n$escaped-characters: (\r\n  (\"<\", \"%3c\"),\r\n  (\">\", \"%3e\"),\r\n  (\"#\", \"%23\"),\r\n  (\"(\", \"%28\"),\r\n  (\")\", \"%29\"),\r\n) !default;\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-caret:                true !default;\r\n$enable-rounded:              true !default;\r\n$enable-shadows:              false !default;\r\n$enable-gradients:            false !default;\r\n$enable-transitions:          true !default;\r\n$enable-reduced-motion:       true !default;\r\n$enable-smooth-scroll:        true !default;\r\n$enable-grid-classes:         true !default;\r\n$enable-container-classes:    true !default;\r\n$enable-cssgrid:              false !default;\r\n$enable-button-pointers:      true !default;\r\n$enable-rfs:                  true !default;\r\n$enable-validation-icons:     true !default;\r\n$enable-negative-margins:     false !default;\r\n$enable-deprecation-messages: true !default;\r\n$enable-important-utilities:  true !default;\r\n\r\n$enable-dark-mode:            true !default;\r\n$color-mode-type:             data !default; // `data` or `media-query`\r\n\r\n// Prefix for :root CSS variables\r\n\r\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\r\n$prefix:                      $variable-prefix !default;\r\n\r\n// Gradient\r\n//\r\n// The gradient which is added to components if `$enable-gradients` is `true`\r\n// This gradient is also added to elements with `.bg-gradient`\r\n// scss-docs-start variable-gradient\r\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\r\n// scss-docs-end variable-gradient\r\n\r\n// Spacing\r\n//\r\n// Control the default styling of most Bootstrap elements by modifying these\r\n// variables. Mostly focused on spacing.\r\n// You can add more entries to the $spacers map, should you need more variation.\r\n\r\n// scss-docs-start spacer-variables-maps\r\n$spacer: 1rem !default;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * .25,\r\n  2: $spacer * .5,\r\n  3: $spacer,\r\n  4: $spacer * 1.5,\r\n  5: $spacer * 3,\r\n) !default;\r\n// scss-docs-end spacer-variables-maps\r\n\r\n// Position\r\n//\r\n// Define the edge positioning anchors of the position utilities.\r\n\r\n// scss-docs-start position-map\r\n$position-values: (\r\n  0: 0,\r\n  50: 50%,\r\n  100: 100%\r\n) !default;\r\n// scss-docs-end position-map\r\n\r\n// Body\r\n//\r\n// Settings for the `<body>` element.\r\n\r\n$body-text-align:           null !default;\r\n$body-color:                $gray-900 !default;\r\n$body-bg:                   $white !default;\r\n\r\n$body-secondary-color:      rgba($body-color, .75) !default;\r\n$body-secondary-bg:         $gray-200 !default;\r\n\r\n$body-tertiary-color:       rgba($body-color, .5) !default;\r\n$body-tertiary-bg:          $gray-100 !default;\r\n\r\n$body-emphasis-color:       $black !default;\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n\r\n$link-color:                              $primary !default;\r\n$link-decoration:                         underline !default;\r\n$link-shade-percentage:                   20% !default;\r\n$link-hover-color:                        shift-color-hard($link-color) !default;\r\n$link-hover-decoration:                   null !default;\r\n\r\n$stretched-link-pseudo-element:           after !default;\r\n$stretched-link-z-index:                  1 !default;\r\n\r\n// Paragraphs\r\n//\r\n// Style p element.\r\n\r\n$paragraph-margin-bottom:   1rem !default;\r\n\r\n\r\n// Grid breakpoints\r\n//\r\n// Define the minimum dimensions at which your layout will change,\r\n// adapting to different screen sizes, for use in media queries.\r\n\r\n// scss-docs-start grid-breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1400px\r\n) !default;\r\n// scss-docs-end grid-breakpoints\r\n\r\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\r\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\r\n\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px,\r\n  xxl: 1320px\r\n) !default;\r\n// scss-docs-end container-max-widths\r\n\r\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\r\n\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n\r\n$grid-columns:                12 !default;\r\n$grid-gutter-width:           1.5rem !default;\r\n$grid-row-columns:            6 !default;\r\n\r\n// Container padding\r\n\r\n$container-padding-x: $grid-gutter-width !default;\r\n\r\n\r\n// Components\r\n//\r\n// Define common padding and border radius sizes and more.\r\n\r\n// scss-docs-start border-variables\r\n$border-width:                1px !default;\r\n$border-widths: (\r\n  1: 1px,\r\n  2: 2px,\r\n  3: 3px,\r\n  4: 4px,\r\n  5: 5px\r\n) !default;\r\n$border-style:                solid !default;\r\n$border-color:                $gray-300 !default;\r\n$border-color-translucent:    rgba($black, .175) !default;\r\n// scss-docs-end border-variables\r\n\r\n// scss-docs-start border-radius-variables\r\n$border-radius:               .375rem !default;\r\n$border-radius-sm:            .25rem !default;\r\n$border-radius-lg:            .5rem !default;\r\n$border-radius-xl:            1rem !default;\r\n$border-radius-xxl:           2rem !default;\r\n$border-radius-pill:          50rem !default;\r\n// scss-docs-end border-radius-variables\r\n// fusv-disable\r\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\r\n// fusv-enable\r\n\r\n// scss-docs-start box-shadow-variables\r\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\r\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\r\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\r\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\r\n// scss-docs-end box-shadow-variables\r\n\r\n$component-active-color:      $white !default;\r\n$component-active-bg:         $success !default;\r\n\r\n// scss-docs-start focus-ring-variables\r\n$focus-ring-width:      .25rem !default;\r\n$focus-ring-opacity:    .25 !default;\r\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\r\n$focus-ring-blur:       0 !default;\r\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color !default;\r\n// scss-docs-end focus-ring-variables\r\n\r\n// scss-docs-start caret-variables\r\n$caret-width:                 .3em !default;\r\n$caret-vertical-align:        $caret-width * .85 !default;\r\n$caret-spacing:               $caret-width * .85 !default;\r\n// scss-docs-end caret-variables\r\n\r\n$transition-base:             all .2s ease-in-out !default;\r\n$transition-fade:             opacity .15s linear !default;\r\n// scss-docs-start collapse-transition\r\n$transition-collapse:         height .35s ease !default;\r\n$transition-collapse-width:   width .35s ease !default;\r\n// scss-docs-end collapse-transition\r\n\r\n// stylelint-disable function-disallowed-list\r\n// scss-docs-start aspect-ratios\r\n$aspect-ratios: (\r\n  \"1x1\": 100%,\r\n  \"4x3\": calc(3 / 4 * 100%),\r\n  \"16x9\": calc(9 / 16 * 100%),\r\n  \"21x9\": calc(9 / 21 * 100%)\r\n) !default;\r\n// scss-docs-end aspect-ratios\r\n// stylelint-enable function-disallowed-list\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// scss-docs-start font-variables\r\n// stylelint-disable value-keyword-case\r\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\r\n// stylelint-enable value-keyword-case\r\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\r\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\r\n\r\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\r\n// $font-size-base affects the font size of the body text\r\n$font-size-root:              null !default;\r\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\r\n$font-size-sm:                $font-size-base * .875 !default;\r\n$font-size-lg:                $font-size-base * 1.25 !default;\r\n\r\n$font-weight-lighter:         lighter !default;\r\n$font-weight-light:           300 !default;\r\n$font-weight-normal:          400 !default;\r\n$font-weight-medium:          500 !default;\r\n$font-weight-semibold:        600 !default;\r\n$font-weight-bold:            700 !default;\r\n$font-weight-bolder:          bolder !default;\r\n\r\n$font-weight-base:            $font-weight-normal !default;\r\n\r\n$line-height-base:            1.5 !default;\r\n$line-height-sm:              1.25 !default;\r\n$line-height-lg:              2 !default;\r\n\r\n$h1-font-size:                $font-size-base * 2.5 !default;\r\n$h2-font-size:                $font-size-base * 2 !default;\r\n$h3-font-size:                $font-size-base * 1.75 !default;\r\n$h4-font-size:                $font-size-base * 1.5 !default;\r\n$h5-font-size:                $font-size-base * 1.25 !default;\r\n$h6-font-size:                $font-size-base !default;\r\n// scss-docs-end font-variables\r\n\r\n// scss-docs-start font-sizes\r\n$font-sizes: (\r\n  1: $h1-font-size,\r\n  2: $h2-font-size,\r\n  3: $h3-font-size,\r\n  4: $h4-font-size,\r\n  5: $h5-font-size,\r\n  6: $h6-font-size\r\n) !default;\r\n// scss-docs-end font-sizes\r\n\r\n// scss-docs-start headings-variables\r\n$headings-margin-bottom:      $spacer * .5 !default;\r\n$headings-font-family:        null !default;\r\n$headings-font-style:         null !default;\r\n$headings-font-weight:        500 !default;\r\n$headings-line-height:        1.2 !default;\r\n$headings-color:              null !default;\r\n// scss-docs-end headings-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: 5rem,\r\n  2: 4.5rem,\r\n  3: 4rem,\r\n  4: 3.5rem,\r\n  5: 3rem,\r\n  6: 2.5rem\r\n) !default;\r\n\r\n$display-font-family: null !default;\r\n$display-font-style:  null !default;\r\n$display-font-weight: 300 !default;\r\n$display-line-height: $headings-line-height !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size:              $font-size-base * 1.25 !default;\r\n$lead-font-weight:            300 !default;\r\n\r\n$small-font-size:             .875em !default;\r\n\r\n$sub-sup-font-size:           .75em !default;\r\n\r\n$text-high-emphasis:            $high-emphasis !default;\r\n$text-medium-emphasis:          $medium-emphasis !default;\r\n$text-disabled:                 $disabled !default;\r\n\r\n$text-high-emphasis-inverse:    $high-emphasis-inverse-new !default;\r\n$text-medium-emphasis-inverse:  $medium-emphasis-inverse-new !default;\r\n$text-disabled-inverse:         $disabled-inverse-new !default;\r\n\r\n// fusv-disable\r\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\r\n// fusv-enable\r\n\r\n$initialism-font-size:        $small-font-size !default;\r\n\r\n$blockquote-margin-y:         $spacer !default;\r\n$blockquote-font-size:        $font-size-base * 1.25 !default;\r\n$blockquote-footer-color:     $gray-600 !default;\r\n$blockquote-footer-font-size: $small-font-size !default;\r\n\r\n$hr-margin-y:                 $spacer !default;\r\n$hr-color:                    inherit !default;\r\n\r\n// fusv-disable\r\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\r\n$hr-height:                   null !default; // Deprecated in v5.2.0\r\n// fusv-enable\r\n\r\n$hr-border-color:             null !default; // Allows for inherited colors\r\n$hr-border-width:             var(--#{$prefix}border-width) !default;\r\n$hr-opacity:                  .25 !default;\r\n\r\n$legend-margin-bottom:        .5rem !default;\r\n$legend-font-size:            1.5rem !default;\r\n$legend-font-weight:          null !default;\r\n\r\n$dt-font-weight:              $font-weight-bold !default;\r\n\r\n$list-inline-padding:         .5rem !default;\r\n\r\n$mark-padding:                .1875em !default;\r\n$mark-bg:                     $yellow-100 !default;\r\n// scss-docs-end type-variables\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n// scss-docs-start table-variables\r\n$table-cell-padding-y:        .5rem !default;\r\n$table-cell-padding-x:        .5rem !default;\r\n$table-cell-padding-y-sm:     .25rem !default;\r\n$table-cell-padding-x-sm:     .25rem !default;\r\n\r\n$table-cell-vertical-align:   top !default;\r\n\r\n$table-color:                 var(--#{$prefix}body-color) !default;\r\n$table-bg:                    transparent !default;\r\n$table-accent-bg:             transparent !default;\r\n\r\n$table-th-font-weight:        null !default;\r\n\r\n$table-striped-color:         $table-color !default;\r\n$table-striped-bg-factor:     .05 !default;\r\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\r\n\r\n$table-active-color:          $table-color !default;\r\n$table-active-bg-factor:      .1 !default;\r\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\r\n\r\n$table-hover-color:           $table-color !default;\r\n$table-hover-bg-factor:       .075 !default;\r\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\r\n\r\n$table-border-factor:         .1 !default;\r\n$table-border-width:          var(--#{$prefix}border-width) !default;\r\n$table-border-color:          var(--#{$prefix}border-color) !default;\r\n\r\n$table-striped-order:         odd !default;\r\n$table-striped-columns-order: even !default;\r\n\r\n$table-group-separator-color: currentcolor !default;\r\n\r\n$table-caption-color:         var(--#{$prefix}secondary-color) !default;\r\n\r\n$table-bg-scale:              -80% !default;\r\n// scss-docs-end table-variables\r\n\r\n// scss-docs-start table-loop\r\n$table-variants: (\r\n  \"primary\":    shift-color($primary, $table-bg-scale),\r\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\r\n  \"success\":    shift-color($success, $table-bg-scale),\r\n  \"info\":       shift-color($info, $table-bg-scale),\r\n  \"warning\":    shift-color($warning, $table-bg-scale),\r\n  \"danger\":     shift-color($danger, $table-bg-scale),\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n) !default;\r\n// scss-docs-end table-loop\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y:         .375rem !default;\r\n$input-btn-padding-x:         .75rem !default;\r\n$input-btn-font-family:       null !default;\r\n$input-btn-font-size:         $font-size-base !default;\r\n$input-btn-line-height:       $line-height-base !default;\r\n\r\n$input-btn-focus-width:         $focus-ring-width !default;\r\n$input-btn-focus-color-opacity: $focus-ring-opacity !default;\r\n$input-btn-focus-color:         $focus-ring-color !default;\r\n$input-btn-focus-blur:          $focus-ring-blur !default;\r\n$input-btn-focus-box-shadow:    $focus-ring-box-shadow !default;\r\n\r\n$input-btn-padding-y-sm:      .25rem !default;\r\n$input-btn-padding-x-sm:      .5rem !default;\r\n$input-btn-font-size-sm:      $font-size-sm !default;\r\n\r\n$input-btn-padding-y-lg:      .5rem !default;\r\n$input-btn-padding-x-lg:      1rem !default;\r\n$input-btn-font-size-lg:      $font-size-lg !default;\r\n\r\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n\r\n// Buttons\r\n//\r\n// For each of Bootstrap's buttons, define text, background, and border color.\r\n\r\n// scss-docs-start btn-variables\r\n$btn-padding-y:               $input-btn-padding-y !default;\r\n$btn-padding-x:               $input-btn-padding-x !default;\r\n$btn-font-family:             $input-btn-font-family !default;\r\n$btn-font-size:               $input-btn-font-size !default;\r\n$btn-line-height:             $input-btn-line-height !default;\r\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\r\n\r\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\r\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\r\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\r\n\r\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\r\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\r\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\r\n\r\n$btn-border-width:            $input-btn-border-width !default;\r\n\r\n$btn-font-weight:             $font-weight-normal !default;\r\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\r\n$btn-focus-width:             $input-btn-focus-width !default;\r\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\r\n$btn-disabled-opacity:        .65 !default;\r\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\r\n\r\n$btn-link-color:              var(--#{$prefix}link-color) !default;\r\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\r\n$btn-link-disabled-color:     $gray-600 !default;\r\n\r\n// Allows for customizing button radius independently from global border radius\r\n$btn-border-radius:           $border-radius !default;\r\n$btn-border-radius-sm:        $border-radius-sm !default;\r\n$btn-border-radius-lg:        $border-radius-lg !default;\r\n\r\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$btn-hover-bg-shade-amount:       15% !default;\r\n$btn-hover-bg-tint-amount:        15% !default;\r\n$btn-hover-border-shade-amount:   20% !default;\r\n$btn-hover-border-tint-amount:    10% !default;\r\n$btn-active-bg-shade-amount:      20% !default;\r\n$btn-active-bg-tint-amount:       20% !default;\r\n$btn-active-border-shade-amount:  25% !default;\r\n$btn-active-border-tint-amount:   10% !default;\r\n// scss-docs-end btn-variables\r\n\r\n\r\n// Forms\r\n\r\n// scss-docs-start form-text-variables\r\n$form-text-margin-top:                  .25rem !default;\r\n$form-text-font-size:                   $small-font-size !default;\r\n$form-text-font-style:                  null !default;\r\n$form-text-font-weight:                 null !default;\r\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end form-text-variables\r\n\r\n// scss-docs-start form-label-variables\r\n$form-label-margin-bottom:              .25rem !default;\r\n$form-label-font-size:                  null !default;\r\n$form-label-font-style:                 null !default;\r\n$form-label-font-weight:                null !default;\r\n$form-label-color:                      null !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-input-variables\r\n$input-padding-y:                       $input-btn-padding-y !default;\r\n$input-padding-x:                       $input-btn-padding-x !default;\r\n$input-font-family:                     $input-btn-font-family !default;\r\n$input-font-size:                       $input-btn-font-size !default;\r\n$input-font-weight:                     $font-weight-base !default;\r\n$input-line-height:                     $input-btn-line-height !default;\r\n\r\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\r\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\r\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\r\n\r\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\r\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\r\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\r\n\r\n$input-bg:                              var(--#{$prefix}body-bg) !default;\r\n$input-disabled-color:                  null !default;\r\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\r\n$input-disabled-border-color:           null !default;\r\n\r\n$input-color:                           var(--#{$prefix}body-color) !default;\r\n$input-border-color:                    var(--#{$prefix}border-color) !default;\r\n$input-border-width:                    $input-btn-border-width !default;\r\n$input-box-shadow:                      $box-shadow-inset !default;\r\n\r\n$input-border-radius:                   $border-radius !default;\r\n$input-border-radius-sm:                $border-radius-sm !default;\r\n$input-border-radius-lg:                $border-radius-lg !default;\r\n\r\n$input-focus-bg:                        $input-bg !default;\r\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\r\n$input-focus-color:                     $input-color !default;\r\n$input-focus-width:                     $input-btn-focus-width !default;\r\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\r\n\r\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\r\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\r\n\r\n$input-height-border:                   calc($input-border-width * 2) !default; // stylelint-disable-line function-disallowed-list\r\n\r\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\r\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\r\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\r\n\r\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\r\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\r\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\r\n\r\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$form-color-width:                      3rem !default;\r\n// scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-input-width:                  1em !default;\r\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\r\n$form-check-padding-start:                $form-check-input-width + .5em !default;\r\n$form-check-margin-bottom:                .125rem !default;\r\n$form-check-label-color:                  null !default;\r\n$form-check-label-cursor:                 null !default;\r\n$form-check-transition:                   null !default;\r\n\r\n$form-check-input-active-filter:          brightness(90%) !default;\r\n\r\n$form-check-input-bg:                     $input-bg !default;\r\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\r\n$form-check-input-border-radius:          .25em !default;\r\n$form-check-radio-border-radius:          50% !default;\r\n$form-check-input-focus-border:           $input-focus-border-color !default;\r\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\r\n\r\n$form-check-input-checked-color:          $component-active-color !default;\r\n$form-check-input-checked-bg-color:       $component-active-bg !default;\r\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\r\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\r\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\r\n\r\n$form-check-input-indeterminate-color:          $component-active-color !default;\r\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\r\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\r\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\r\n\r\n$form-check-input-disabled-opacity:        .5 !default;\r\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\r\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\r\n\r\n$form-check-inline-margin-end:    1rem !default;\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n$form-switch-color:               rgba($black, .25) !default;\r\n$form-switch-width:               2em !default;\r\n$form-switch-padding-start:       $form-switch-width + .5em !default;\r\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\r\n$form-switch-border-radius:       $form-switch-width !default;\r\n$form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n$form-switch-focus-color:         $input-focus-border-color !default;\r\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\r\n\r\n$form-switch-checked-color:       $component-active-color !default;\r\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\r\n$form-switch-checked-bg-position: right center !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start input-group-variables\r\n$input-group-addon-padding-y:           $input-padding-y !default;\r\n$input-group-addon-padding-x:           $input-padding-x !default;\r\n$input-group-addon-font-weight:         $input-font-weight !default;\r\n$input-group-addon-color:               $input-color !default;\r\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\r\n$input-group-addon-border-color:        $input-border-color !default;\r\n// scss-docs-end input-group-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-padding-y:             $input-padding-y !default;\r\n$form-select-padding-x:             $input-padding-x !default;\r\n$form-select-font-family:           $input-font-family !default;\r\n$form-select-font-size:             $input-font-size !default;\r\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\r\n$form-select-font-weight:           $input-font-weight !default;\r\n$form-select-line-height:           $input-line-height !default;\r\n$form-select-color:                 $input-color !default;\r\n$form-select-bg:                    $input-bg !default;\r\n$form-select-disabled-color:        null !default;\r\n$form-select-disabled-bg:           $input-disabled-bg !default;\r\n$form-select-disabled-border-color: $input-disabled-border-color !default;\r\n$form-select-bg-position:           right $form-select-padding-x center !default;\r\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\r\n$form-select-indicator-color:       $gray-800 !default;\r\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\r\n\r\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\r\n\r\n$form-select-border-width:        $input-border-width !default;\r\n$form-select-border-color:        $input-border-color !default;\r\n$form-select-border-radius:       $input-border-radius !default;\r\n$form-select-box-shadow:          $box-shadow-inset !default;\r\n\r\n$form-select-focus-border-color:  $input-focus-border-color !default;\r\n$form-select-focus-width:         $input-focus-width !default;\r\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\r\n\r\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\r\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\r\n$form-select-font-size-sm:        $input-font-size-sm !default;\r\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\r\n\r\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\r\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\r\n$form-select-font-size-lg:        $input-font-size-lg !default;\r\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\r\n\r\n$form-select-transition:          $input-transition !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-width:          100% !default;\r\n$form-range-track-height:         .5rem !default;\r\n$form-range-track-cursor:         pointer !default;\r\n$form-range-track-bg:             var(--#{$prefix}tertiary-bg) !default;\r\n$form-range-track-border-radius:  1rem !default;\r\n$form-range-track-box-shadow:     $box-shadow-inset !default;\r\n\r\n$form-range-thumb-width:                   1rem !default;\r\n$form-range-thumb-height:                  $form-range-thumb-width !default;\r\n$form-range-thumb-bg:                      $component-active-bg !default;\r\n$form-range-thumb-border:                  0 !default;\r\n$form-range-thumb-border-radius:           1rem !default;\r\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\r\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\r\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\r\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\r\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\r\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-range-variables\r\n\r\n// scss-docs-start form-file-variables\r\n$form-file-button-color:          $input-color !default;\r\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\r\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\r\n// scss-docs-end form-file-variables\r\n\r\n// scss-docs-start form-floating-variables\r\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\r\n$form-floating-line-height:             1.25 !default;\r\n$form-floating-padding-x:               $input-padding-x !default;\r\n$form-floating-padding-y:               1rem !default;\r\n$form-floating-input-padding-t:         1.625rem !default;\r\n$form-floating-input-padding-b:         .625rem !default;\r\n$form-floating-label-height:            1.875em !default;\r\n$form-floating-label-opacity:           .65 !default;\r\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\r\n$form-floating-label-disabled-color:    $gray-600 !default;\r\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\r\n// scss-docs-end form-floating-variables\r\n\r\n// Form validation\r\n\r\n// scss-docs-start form-feedback-variables\r\n$form-feedback-margin-top:          $form-text-margin-top !default;\r\n$form-feedback-font-size:           $form-text-font-size !default;\r\n$form-feedback-font-style:          $form-text-font-style !default;\r\n$form-feedback-valid-color:         $success !default;\r\n$form-feedback-invalid-color:       $danger !default;\r\n\r\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\r\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\r\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\r\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\r\n// scss-docs-end form-feedback-variables\r\n\r\n// scss-docs-start form-validation-colors\r\n$form-valid-color:                  $form-feedback-valid-color !default;\r\n$form-valid-border-color:           $form-feedback-valid-color !default;\r\n$form-invalid-color:                $form-feedback-invalid-color !default;\r\n$form-invalid-border-color:         $form-feedback-invalid-color !default;\r\n// scss-docs-end form-validation-colors\r\n\r\n// scss-docs-start form-validation-states\r\n$form-validation-states: (\r\n  \"valid\": (\r\n    \"color\": var(--#{$prefix}form-valid-color),\r\n    \"icon\": $form-feedback-icon-valid,\r\n    \"tooltip-color\": #fff,\r\n    \"tooltip-bg-color\": var(--#{$prefix}success),\r\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\r\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\r\n  ),\r\n  \"invalid\": (\r\n    \"color\": var(--#{$prefix}form-invalid-color),\r\n    \"icon\": $form-feedback-icon-invalid,\r\n    \"tooltip-color\": #fff,\r\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\r\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\r\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\r\n  )\r\n) !default;\r\n// scss-docs-end form-validation-states\r\n\r\n// Z-index master list\r\n//\r\n// Warning: Avoid customizing these values. They're used for a bird's eye view\r\n// of components dependent on the z-axis and are designed to all work together.\r\n\r\n// scss-docs-start zindex-stack\r\n$zindex-dropdown:                   1000 !default;\r\n$zindex-sticky:                     1020 !default;\r\n$zindex-fixed:                      1030 !default;\r\n$zindex-offcanvas-backdrop:         1040 !default;\r\n$zindex-offcanvas:                  1045 !default;\r\n$zindex-modal-backdrop:             1050 !default;\r\n$zindex-modal:                      1055 !default;\r\n$zindex-popover:                    1070 !default;\r\n$zindex-tooltip:                    1080 !default;\r\n$zindex-toast:                      1090 !default;\r\n// scss-docs-end zindex-stack\r\n\r\n// scss-docs-start zindex-levels-map\r\n$zindex-levels: (\r\n  n1: -1,\r\n  0: 0,\r\n  1: 1,\r\n  2: 2,\r\n  3: 3\r\n) !default;\r\n// scss-docs-end zindex-levels-map\r\n\r\n\r\n// Navs\r\n\r\n// scss-docs-start nav-variables\r\n$nav-link-padding-y:                .5rem !default;\r\n$nav-link-padding-x:                1rem !default;\r\n$nav-link-font-size:                null !default;\r\n$nav-link-font-weight:              null !default;\r\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\r\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\r\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\r\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\r\n$nav-link-focus-box-shadow:         $focus-ring-box-shadow !default;\r\n\r\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\r\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\r\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\r\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\r\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\r\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\r\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\r\n\r\n$nav-pills-border-radius:           $border-radius !default;\r\n$nav-pills-link-active-color:       $component-active-color !default;\r\n$nav-pills-link-active-bg:          $component-active-bg !default;\r\n\r\n$nav-underline-gap:                 1rem !default;\r\n$nav-underline-border-width:        .125rem !default;\r\n$nav-underline-link-active-color:   var(--#{$prefix}emphasis-color) !default;\r\n// scss-docs-end nav-variables\r\n\r\n\r\n// Navbar\r\n\r\n// scss-docs-start navbar-variables\r\n$navbar-padding-y:                  $spacer * .5 !default;\r\n$navbar-padding-x:                  null !default;\r\n\r\n$navbar-nav-link-padding-x:         .5rem !default;\r\n\r\n$navbar-brand-font-size:            $font-size-lg !default;\r\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\r\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\r\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\r\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\r\n$navbar-brand-margin-end:           1rem !default;\r\n\r\n$navbar-toggler-padding-y:          .25rem !default;\r\n$navbar-toggler-padding-x:          .75rem !default;\r\n$navbar-toggler-font-size:          $font-size-lg !default;\r\n$navbar-toggler-border-radius:      $btn-border-radius !default;\r\n$navbar-toggler-focus-width:        $btn-focus-width !default;\r\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\r\n\r\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\r\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\r\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\r\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\r\n$navbar-light-icon-color:           rgba($body-color, .75) !default;\r\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\r\n$navbar-light-brand-color:          $navbar-light-active-color !default;\r\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\r\n// scss-docs-end navbar-variables\r\n\r\n// scss-docs-start navbar-dark-variables\r\n$navbar-dark-color:                 rgba($white, .55) !default;\r\n$navbar-dark-hover-color:           rgba($white, .75) !default;\r\n$navbar-dark-active-color:          $white !default;\r\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\r\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\r\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\r\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\r\n// scss-docs-end navbar-dark-variables\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n// scss-docs-start dropdown-variables\r\n$dropdown-min-width:                10rem !default;\r\n$dropdown-padding-x:                0 !default;\r\n$dropdown-padding-y:                .5rem !default;\r\n$dropdown-spacer:                   .125rem !default;\r\n$dropdown-font-size:                $font-size-base !default;\r\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\r\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\r\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\r\n$dropdown-border-radius:            $border-radius !default;\r\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\r\n$dropdown-inner-border-radius:      calc($dropdown-border-radius - $dropdown-border-width) !default; // stylelint-disable-line function-disallowed-list\r\n$dropdown-divider-bg:               $dropdown-border-color !default;\r\n$dropdown-divider-margin-y:         $spacer * .5 !default;\r\n$dropdown-box-shadow:               $box-shadow !default;\r\n\r\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\r\n$dropdown-link-hover-color:         $dropdown-link-color !default;\r\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\r\n\r\n$dropdown-link-active-color:        $component-active-color !default;\r\n$dropdown-link-active-bg:           $component-active-bg !default;\r\n\r\n$dropdown-link-disabled-color:      var(--#{$prefix}tertiary-color) !default;\r\n\r\n$dropdown-item-padding-y:           $spacer * .25 !default;\r\n$dropdown-item-padding-x:           $spacer !default;\r\n\r\n$dropdown-header-color:             $gray-600 !default;\r\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\r\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\r\n// fusv-disable\r\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\r\n// fusv-enable\r\n// scss-docs-end dropdown-variables\r\n\r\n// scss-docs-start dropdown-dark-variables\r\n$dropdown-dark-color:               $gray-300 !default;\r\n$dropdown-dark-bg:                  $gray-800 !default;\r\n$dropdown-dark-border-color:        $dropdown-border-color !default;\r\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\r\n$dropdown-dark-box-shadow:          null !default;\r\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\r\n$dropdown-dark-link-hover-color:    $white !default;\r\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\r\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\r\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\r\n$dropdown-dark-link-disabled-color: $gray-500 !default;\r\n$dropdown-dark-header-color:        $gray-500 !default;\r\n// scss-docs-end dropdown-dark-variables\r\n\r\n\r\n// Pagination\r\n\r\n// scss-docs-start pagination-variables\r\n$pagination-padding-y:              .375rem !default;\r\n$pagination-padding-x:              .75rem !default;\r\n$pagination-padding-y-sm:           .25rem !default;\r\n$pagination-padding-x-sm:           .5rem !default;\r\n$pagination-padding-y-lg:           .75rem !default;\r\n$pagination-padding-x-lg:           1.5rem !default;\r\n\r\n$pagination-font-size:              $font-size-base !default;\r\n\r\n$pagination-color:                  var(--#{$prefix}link-color) !default;\r\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\r\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\r\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\r\n$pagination-margin-start:           calc($pagination-border-width * -1) !default; // stylelint-disable-line function-disallowed-list\r\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\r\n\r\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\r\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\r\n$pagination-focus-box-shadow:       $focus-ring-box-shadow !default;\r\n$pagination-focus-outline:          0 !default;\r\n\r\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\r\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\r\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\r\n\r\n$pagination-active-color:           $component-active-color !default;\r\n$pagination-active-bg:              $component-active-bg !default;\r\n$pagination-active-border-color:    $component-active-bg !default;\r\n\r\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\r\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\r\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\r\n\r\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$pagination-border-radius-sm:       $border-radius-sm !default;\r\n$pagination-border-radius-lg:       $border-radius-lg !default;\r\n// scss-docs-end pagination-variables\r\n\r\n\r\n// Placeholders\r\n\r\n// scss-docs-start placeholders\r\n$placeholder-opacity-max:           .5 !default;\r\n$placeholder-opacity-min:           .2 !default;\r\n// scss-docs-end placeholders\r\n\r\n// Cards\r\n\r\n// scss-docs-start card-variables\r\n$card-spacer-y:                     $spacer !default;\r\n$card-spacer-x:                     $spacer !default;\r\n$card-title-spacer-y:               $spacer * .5 !default;\r\n$card-title-color:                  null !default;\r\n$card-subtitle-color:               null !default;\r\n$card-border-width:                 var(--#{$prefix}border-width) !default;\r\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\r\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\r\n$card-box-shadow:                   null !default;\r\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\r\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\r\n$card-cap-padding-x:                $card-spacer-x !default;\r\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\r\n$card-cap-color:                    null !default;\r\n$card-height:                       null !default;\r\n$card-color:                        null !default;\r\n$card-bg:                           var(--#{$prefix}body-bg) !default;\r\n$card-img-overlay-padding:          $spacer !default;\r\n$card-group-margin:                 $grid-gutter-width * .5 !default;\r\n// scss-docs-end card-variables\r\n\r\n// Accordion\r\n\r\n// scss-docs-start accordion-variables\r\n$accordion-padding-y:                     1rem !default;\r\n$accordion-padding-x:                     1.25rem !default;\r\n$accordion-color:                         var(--#{$prefix}body-color) !default;\r\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\r\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\r\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\r\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\r\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\r\n\r\n$accordion-body-padding-y:                $accordion-padding-y !default;\r\n$accordion-body-padding-x:                $accordion-padding-x !default;\r\n\r\n$accordion-button-padding-y:              $accordion-padding-y !default;\r\n$accordion-button-padding-x:              $accordion-padding-x !default;\r\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\r\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\r\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\r\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\r\n$accordion-button-active-color:           var(--#{$prefix}primary-text) !default;\r\n\r\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\r\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\r\n\r\n$accordion-icon-width:                    1.25rem !default;\r\n$accordion-icon-color:                    $body-color !default;\r\n$accordion-icon-active-color:             $primary-text-emphasis !default;\r\n$accordion-icon-transition:               transform .2s ease-in-out !default;\r\n$accordion-icon-transform:                rotate(-180deg) !default;\r\n\r\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n// scss-docs-end accordion-variables\r\n\r\n// Tooltips\r\n\r\n// scss-docs-start tooltip-variables\r\n$tooltip-font-size:                 $font-size-sm !default;\r\n$tooltip-max-width:                 200px !default;\r\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\r\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\r\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\r\n$tooltip-opacity:                   .9 !default;\r\n$tooltip-padding-y:                 $spacer * .25 !default;\r\n$tooltip-padding-x:                 $spacer * .5 !default;\r\n$tooltip-margin:                    null !default; // TODO: remove this in v6\r\n\r\n$tooltip-arrow-width:               .8rem !default;\r\n$tooltip-arrow-height:              .4rem !default;\r\n// fusv-disable\r\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\r\n// fusv-enable\r\n// scss-docs-end tooltip-variables\r\n\r\n// Form tooltips must come after regular tooltips\r\n// scss-docs-start tooltip-feedback-variables\r\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\r\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\r\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\r\n$form-feedback-tooltip-line-height:   null !default;\r\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\r\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\r\n// scss-docs-end tooltip-feedback-variables\r\n\r\n\r\n// Popovers\r\n\r\n// scss-docs-start popover-variables\r\n$popover-font-size:                 $font-size-sm !default;\r\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\r\n$popover-max-width:                 276px !default;\r\n$popover-border-width:              var(--#{$prefix}border-width) !default;\r\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\r\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\r\n$popover-inner-border-radius:       calc($popover-border-radius - $popover-border-width) !default; // stylelint-disable-line function-disallowed-list\r\n$popover-box-shadow:                $box-shadow !default;\r\n\r\n$popover-header-font-size:          $font-size-base !default;\r\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\r\n$popover-header-color:              $headings-color !default;\r\n$popover-header-padding-y:          .5rem !default;\r\n$popover-header-padding-x:          $spacer !default;\r\n\r\n$popover-body-color:                var(--#{$prefix}body-color) !default;\r\n$popover-body-padding-y:            $spacer !default;\r\n$popover-body-padding-x:            $spacer !default;\r\n\r\n$popover-arrow-width:               1rem !default;\r\n$popover-arrow-height:              .5rem !default;\r\n// scss-docs-end popover-variables\r\n\r\n// fusv-disable\r\n// Deprecated in Bootstrap 5.2.0 for CSS variables\r\n$popover-arrow-color:               $popover-bg !default;\r\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\r\n// fusv-enable\r\n\r\n\r\n// Toasts\r\n\r\n// scss-docs-start toast-variables\r\n$toast-max-width:                   350px !default;\r\n$toast-padding-x:                   .75rem !default;\r\n$toast-padding-y:                   .5rem !default;\r\n$toast-font-size:                   .875rem !default;\r\n$toast-color:                       null !default;\r\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\r\n$toast-border-width:                var(--#{$prefix}border-width) !default;\r\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\r\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\r\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\r\n$toast-spacing:                     $container-padding-x !default;\r\n\r\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\r\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\r\n$toast-header-border-color:         $toast-border-color !default;\r\n// scss-docs-end toast-variables\r\n\r\n\r\n// Badges\r\n\r\n// scss-docs-start badge-variables\r\n$badge-font-size:                   .75em !default;\r\n$badge-font-weight:                 $font-weight-bold !default;\r\n$badge-color:                       $white !default;\r\n$badge-padding-y:                   .35em !default;\r\n$badge-padding-x:                   .65em !default;\r\n$badge-border-radius:               $border-radius !default;\r\n// scss-docs-end badge-variables\r\n\r\n\r\n// Modals\r\n\r\n// scss-docs-start modal-variables\r\n$modal-inner-padding:               $spacer !default;\r\n\r\n$modal-footer-margin-between:       .5rem !default;\r\n\r\n$modal-dialog-margin:               .5rem !default;\r\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\r\n\r\n$modal-title-line-height:           $line-height-base !default;\r\n\r\n$modal-content-color:               null !default;\r\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\r\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\r\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\r\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\r\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\r\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\r\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\r\n\r\n$modal-backdrop-bg:                 $black !default;\r\n$modal-backdrop-opacity:            .5 !default;\r\n\r\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\r\n$modal-header-border-width:         $modal-content-border-width !default;\r\n$modal-header-padding-y:            $modal-inner-padding !default;\r\n$modal-header-padding-x:            $modal-inner-padding !default;\r\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n\r\n$modal-footer-bg:                   null !default;\r\n$modal-footer-border-color:         $modal-header-border-color !default;\r\n$modal-footer-border-width:         $modal-header-border-width !default;\r\n\r\n$modal-sm:                          300px !default;\r\n$modal-md:                          500px !default;\r\n$modal-lg:                          800px !default;\r\n$modal-xl:                          1140px !default;\r\n\r\n$modal-fade-transform:              translate(0, -50px) !default;\r\n$modal-show-transform:              none !default;\r\n$modal-transition:                  transform .3s ease-out !default;\r\n$modal-scale-transform:             scale(1.02) !default;\r\n// scss-docs-end modal-variables\r\n\r\n\r\n// Alerts\r\n//\r\n// Define alert colors, border radius, and padding.\r\n\r\n// scss-docs-start alert-variables\r\n$alert-padding-y:               $spacer !default;\r\n$alert-padding-x:               $spacer !default;\r\n$alert-margin-bottom:           1rem !default;\r\n$alert-border-radius:           $border-radius !default;\r\n$alert-link-font-weight:        $font-weight-bold !default;\r\n$alert-border-width:            var(--#{$prefix}border-width) !default;\r\n$alert-bg-scale:                -80% !default;\r\n$alert-border-scale:            -70% !default;\r\n$alert-color-scale:             40% !default;\r\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\r\n// scss-docs-end alert-variables\r\n\r\n// fusv-disable\r\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\r\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\r\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\r\n// fusv-enable\r\n\r\n// Progress bars\r\n\r\n// scss-docs-start progress-variables\r\n$progress-height:                   1rem !default;\r\n$progress-font-size:                $font-size-base * .75 !default;\r\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\r\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\r\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\r\n$progress-bar-color:                $white !default;\r\n$progress-bar-bg:                   $primary !default;\r\n$progress-bar-animation-timing:     1s linear infinite !default;\r\n$progress-bar-transition:           width .6s ease !default;\r\n// scss-docs-end progress-variables\r\n\r\n\r\n// List group\r\n\r\n// scss-docs-start list-group-variables\r\n$list-group-color:                  var(--#{$prefix}body-color) !default;\r\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\r\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\r\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\r\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\r\n\r\n$list-group-item-padding-y:         $spacer * .5 !default;\r\n$list-group-item-padding-x:         $spacer !default;\r\n// fusv-disable\r\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\r\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\r\n// fusv-enable\r\n\r\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\r\n$list-group-active-color:           $component-active-color !default;\r\n$list-group-active-bg:              $component-active-bg !default;\r\n$list-group-active-border-color:    $list-group-active-bg !default;\r\n\r\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\r\n$list-group-disabled-bg:            $list-group-bg !default;\r\n\r\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\r\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\r\n\r\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\r\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\r\n// scss-docs-end list-group-variables\r\n\r\n\r\n// Image thumbnails\r\n\r\n// scss-docs-start thumbnail-variables\r\n$thumbnail-padding:                 .25rem !default;\r\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\r\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\r\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\r\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\r\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\r\n// scss-docs-end thumbnail-variables\r\n\r\n\r\n// Figures\r\n\r\n// scss-docs-start figure-variables\r\n$figure-caption-font-size:          $small-font-size !default;\r\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end figure-variables\r\n\r\n\r\n// Breadcrumbs\r\n\r\n// scss-docs-start breadcrumb-variables\r\n$breadcrumb-font-size:              null !default;\r\n$breadcrumb-padding-y:              0 !default;\r\n$breadcrumb-padding-x:              0 !default;\r\n$breadcrumb-item-padding-x:         .5rem !default;\r\n$breadcrumb-margin-bottom:          1rem !default;\r\n$breadcrumb-bg:                     null !default;\r\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\r\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\r\n$breadcrumb-divider:                quote(\"/\") !default;\r\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\r\n$breadcrumb-border-radius:          null !default;\r\n// scss-docs-end breadcrumb-variables\r\n\r\n// Carousel\r\n\r\n// scss-docs-start carousel-variables\r\n$carousel-control-color:             $white !default;\r\n$carousel-control-width:             15% !default;\r\n$carousel-control-opacity:           .5 !default;\r\n$carousel-control-hover-opacity:     .9 !default;\r\n$carousel-control-transition:        opacity .15s ease !default;\r\n\r\n$carousel-indicator-width:           30px !default;\r\n$carousel-indicator-height:          3px !default;\r\n$carousel-indicator-hit-area-height: 10px !default;\r\n$carousel-indicator-spacer:          3px !default;\r\n$carousel-indicator-opacity:         .5 !default;\r\n$carousel-indicator-active-bg:       $white !default;\r\n$carousel-indicator-active-opacity:  1 !default;\r\n$carousel-indicator-transition:      opacity .6s ease !default;\r\n\r\n$carousel-caption-width:             70% !default;\r\n$carousel-caption-color:             $white !default;\r\n$carousel-caption-padding-y:         1.25rem !default;\r\n$carousel-caption-spacer:            1.25rem !default;\r\n\r\n$carousel-control-icon-width:        2rem !default;\r\n\r\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\r\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n\r\n$carousel-transition-duration:       .6s !default;\r\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\r\n// scss-docs-end carousel-variables\r\n\r\n// scss-docs-start carousel-dark-variables\r\n$carousel-dark-indicator-active-bg:  $black !default;\r\n$carousel-dark-caption-color:        $black !default;\r\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\r\n// scss-docs-end carousel-dark-variables\r\n\r\n\r\n// Spinners\r\n\r\n// scss-docs-start spinner-variables\r\n$spinner-width:           2rem !default;\r\n$spinner-height:          $spinner-width !default;\r\n$spinner-vertical-align:  -.125em !default;\r\n$spinner-border-width:    .25em !default;\r\n$spinner-animation-speed: .75s !default;\r\n\r\n$spinner-width-sm:        1rem !default;\r\n$spinner-height-sm:       $spinner-width-sm !default;\r\n$spinner-border-width-sm: .2em !default;\r\n// scss-docs-end spinner-variables\r\n\r\n\r\n// Close\r\n\r\n// scss-docs-start close-variables\r\n$btn-close-width:            1em !default;\r\n$btn-close-height:           $btn-close-width !default;\r\n$btn-close-padding-x:        .25em !default;\r\n$btn-close-padding-y:        $btn-close-padding-x !default;\r\n$btn-close-color:            $black !default;\r\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\r\n$btn-close-focus-shadow:     $focus-ring-box-shadow !default;\r\n$btn-close-opacity:          .5 !default;\r\n$btn-close-hover-opacity:    .75 !default;\r\n$btn-close-focus-opacity:    1 !default;\r\n$btn-close-disabled-opacity: .25 !default;\r\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\r\n// scss-docs-end close-variables\r\n\r\n\r\n// Offcanvas\r\n\r\n// scss-docs-start offcanvas-variables\r\n$offcanvas-padding-y:               $modal-inner-padding !default;\r\n$offcanvas-padding-x:               $modal-inner-padding !default;\r\n$offcanvas-horizontal-width:        400px !default;\r\n$offcanvas-vertical-height:         30vh !default;\r\n$offcanvas-transition-duration:     .3s !default;\r\n$offcanvas-border-color:            $modal-content-border-color !default;\r\n$offcanvas-border-width:            $modal-content-border-width !default;\r\n$offcanvas-title-line-height:       $modal-title-line-height !default;\r\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\r\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\r\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\r\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\r\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\r\n// scss-docs-end offcanvas-variables\r\n\r\n// Code\r\n\r\n$code-font-size:                    $small-font-size !default;\r\n$code-color:                        $pink !default;\r\n\r\n$kbd-padding-y:                     .1875rem !default;\r\n$kbd-padding-x:                     .375rem !default;\r\n$kbd-font-size:                     $code-font-size !default;\r\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\r\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\r\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\r\n\r\n$pre-color:                         null !default;\r\n\r\n\r\n// Calendar & Date & Time Pickers\r\n\r\n// Calendar\r\n// scss-docs-start calendar-variables\r\n$calendar-table-margin:                     .5rem !default;\r\n$calendar-table-cell-size:                  2.75rem !default;\r\n\r\n$calendar-nav-padding:                      .5rem !default;\r\n$calendar-nav-border-width:                 1px !default;\r\n$calendar-nav-border-color:                 $border-color !default;\r\n$calendar-nav-date-color:                   $body-color !default;\r\n$calendar-nav-date-hover-color:             $primary !default;\r\n$calendar-nav-icon-width:                   1rem !default;\r\n$calendar-nav-icon-height:                  1rem !default;\r\n\r\n$calendar-nav-icon-double-next-color:       $gray-600 !default;\r\n$calendar-nav-icon-double-next:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-next-color}' points='95.314 447.313 72.686 424.687 245.373 252 72.686 79.313 95.314 56.687 290.627 252 95.314 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-next-color}' points='255.314 447.313 232.686 424.687 405.373 252 232.686 79.313 255.314 56.687 450.627 252 255.314 447.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-double-next-hover-color: $body-color !default;\r\n$calendar-nav-icon-double-next-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-next-hover-color}' points='95.314 447.313 72.686 424.687 245.373 252 72.686 79.313 95.314 56.687 290.627 252 95.314 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-next-hover-color}' points='255.314 447.313 232.686 424.687 405.373 252 232.686 79.313 255.314 56.687 450.627 252 255.314 447.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-double-prev-color:       $gray-600 !default;\r\n$calendar-nav-icon-double-prev:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-prev-color}' points='416.686 447.313 221.373 252 416.686 56.687 439.314 79.313 266.627 252 439.314 424.687 416.686 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-prev-color}' points='256.686 447.313 61.373 252 256.686 56.687 279.314 79.313 106.627 252 279.314 424.687 256.686 447.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-double-prev-hover-color: $body-color !default;\r\n$calendar-nav-icon-double-prev-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-prev-hover-color}' points='416.686 447.313 221.373 252 416.686 56.687 439.314 79.313 266.627 252 439.314 424.687 416.686 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-prev-hover-color}' points='256.686 447.313 61.373 252 256.686 56.687 279.314 79.313 106.627 252 279.314 424.687 256.686 447.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-next-color:              $gray-600 !default;\r\n$calendar-nav-icon-next:                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-next-color}' points='179.313 451.313 156.687 428.687 329.372 256 156.687 83.313 179.313 60.687 374.627 256 179.313 451.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-next-hover-color:        $body-color !default;\r\n$calendar-nav-icon-next-hover:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-next-hover-color}' points='179.313 451.313 156.687 428.687 329.372 256 156.687 83.313 179.313 60.687 374.627 256 179.313 451.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-prev-color:              $gray-600 !default;\r\n$calendar-nav-icon-prev:                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-prev-color}' points='324.687 451.313 129.373 256 324.687 60.687 347.313 83.313 174.628 256 347.313 428.687 324.687 451.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-prev-hover-color:        $body-color !default;\r\n$calendar-nav-icon-prev-hover:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-prev-hover-color}' points='324.687 451.313 129.373 256 324.687 60.687 347.313 83.313 174.628 256 347.313 428.687 324.687 451.313'></polygon></svg>\") !default;\r\n\r\n$calendar-cell-header-inner-color:          $text-medium-emphasis !default;\r\n\r\n$calendar-cell-hover-bg:                    $gray-200 !default;\r\n$calendar-cell-disabled-color:              $text-disabled !default;\r\n\r\n$calendar-cell-selected-color:              $white !default;\r\n$calendar-cell-selected-bg:                 $success !default;\r\n\r\n$calendar-cell-range-bg:                    rgba($success, .125) !default;\r\n$calendar-cell-range-hover-bg:              rgba($success, .25) !default;\r\n$calendar-cell-range-hover-border-color:    $success !default;\r\n\r\n$calendar-cell-today-color:                 $danger !default;\r\n// scss-docs-end calendar-variables\r\n\r\n// Picker\r\n$picker-footer-border-width:  1px !default;\r\n$picker-footer-border-color:  $border-color !default;\r\n$picker-footer-padding:       .5rem !default;\r\n\r\n// Date Picker\r\n// scss-docs-start date-picker-variables\r\n$date-picker-default-icon-color:       $gray-600 !default;\r\n$date-picker-default-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$date-picker-default-icon-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$date-picker-default-icon-color}'></rect></svg>\") !default;\r\n$date-picker-invalid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$form-feedback-invalid-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$form-feedback-invalid-color}'></rect></svg>\") !default;\r\n$date-picker-valid-icon:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$form-feedback-valid-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$form-feedback-valid-color}'></rect></svg>\") !default;\r\n\r\n$date-picker-cleaner-icon-color:       $gray-600 !default;\r\n$date-picker-cleaner-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-cleaner-icon-color}' points='306.912 214.461 256 265.373 205.088 214.461 182.461 237.088 233.373 288 182.461 338.912 205.088 361.539 256 310.627 306.912 361.539 329.539 338.912 278.627 288 329.539 237.088 306.912 214.461'></polygon><path fill='#{$date-picker-cleaner-icon-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path></svg>\") !default;\r\n$date-picker-cleaner-icon-hover-color: $body-color !default;\r\n$date-picker-cleaner-icon-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-cleaner-icon-hover-color}' points='306.912 214.461 256 265.373 205.088 214.461 182.461 237.088 233.373 288 182.461 338.912 205.088 361.539 256 310.627 306.912 361.539 329.539 338.912 278.627 288 329.539 237.088 306.912 214.461'></polygon><path fill='#{$date-picker-cleaner-icon-hover-color})' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path></svg>\") !default;\r\n\r\n$date-picker-separator-icon-color:     $gray-600 !default;\r\n$date-picker-separator-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-separator-icon-color}' points='359.873 121.377 337.246 144.004 433.243 240.001 16 240.001 16 240.002 16 272.001 16 272.002 433.24 272.002 337.246 367.996 359.873 390.623 494.498 256 359.873 121.377'></polygon></svg>\") !default;\r\n$date-picker-separator-icon-rtl:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-separator-icon-color}' points='497.333 239.999 80.092 239.999 176.087 144.004 153.46 121.377 18.837 256 153.46 390.623 176.087 367.996 80.09 271.999 497.333 271.999 497.333 239.999'></polygon></svg>\") !default;\r\n\r\n$date-picker-ranges-width:             10rem !default;\r\n$date-picker-ranges-padding:           $spacer * .5 !default;\r\n$date-picker-ranges-border-width:      1px !default;\r\n$date-picker-ranges-border-color:      $border-color !default;\r\n\r\n$date-picker-timepicker-width:         (7 * $calendar-table-cell-size) + (2 * $calendar-table-margin) !default;\r\n$date-picker-timepicker-border-width:  1px !default;\r\n$date-picker-timepicker-border-color:  $border-color !default;\r\n// scss-docs-end date-picker-variables\r\n\r\n// Time Picker\r\n// scss-docs-start time-picker-variables\r\n$time-picker-default-icon-color:       $gray-600 !default;\r\n$time-picker-default-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-default-icon-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$time-picker-default-icon-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-invalid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$form-feedback-invalid-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$form-feedback-invalid-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-valid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$form-feedback-valid-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$form-feedback-valid-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n\r\n$time-picker-cleaner-icon-color:       $gray-600 !default;\r\n$time-picker-cleaner-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-cleaner-icon-color}' points='348.071 141.302 260.308 229.065 172.545 141.302 149.917 163.929 237.681 251.692 149.917 339.456 172.545 362.083 260.308 274.32 348.071 362.083 370.699 339.456 282.935 251.692 370.699 163.929 348.071 141.302' class='ci-primary'></polygon><path fill='#{$time-picker-cleaner-icon-color}' d='M425.706,86.294A240,240,0,0,0,86.294,425.706,240,240,0,0,0,425.706,86.294ZM256,464C141.309,464,48,370.691,48,256S141.309,48,256,48s208,93.309,208,208S370.691,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-cleaner-icon-hover-color: $body-color !default;\r\n$time-picker-cleaner-icon-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-cleaner-icon-hover-color}' points='348.071 141.302 260.308 229.065 172.545 141.302 149.917 163.929 237.681 251.692 149.917 339.456 172.545 362.083 260.308 274.32 348.071 362.083 370.699 339.456 282.935 251.692 370.699 163.929 348.071 141.302' class='ci-primary'></polygon><path fill='#{$time-picker-cleaner-icon-hover-color}' d='M425.706,86.294A240,240,0,0,0,86.294,425.706,240,240,0,0,0,425.706,86.294ZM256,464C141.309,464,48,370.691,48,256S141.309,48,256,48s208,93.309,208,208S370.691,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n\r\n$time-picker-body-padding:             $spacer * .5 !default;\r\n$time-picker-roll-col-border-width:    1px !default;\r\n$time-picker-roll-col-border-color:    $border-color !default;\r\n// scss-docs-end time-picker-variables\r\n", "// Row\r\n//\r\n// Rows contain your columns.\r\n\r\n:root {\r\n  @each $name, $value in $grid-breakpoints {\r\n    --#{$prefix}breakpoint-#{$name}: #{$value};\r\n  }\r\n}\r\n\r\n@if $enable-grid-classes {\r\n  .row {\r\n    @include make-row();\r\n\r\n    > * {\r\n      @include make-col-ready();\r\n    }\r\n  }\r\n}\r\n\r\n@if $enable-cssgrid {\r\n  .grid {\r\n    display: grid;\r\n    grid-template-rows: repeat(var(--#{$prefix}rows, 1), 1fr);\r\n    grid-template-columns: repeat(var(--#{$prefix}columns, #{$grid-columns}), 1fr);\r\n    gap: var(--#{$prefix}gap, #{$grid-gutter-width});\r\n\r\n    @include make-cssgrid();\r\n  }\r\n}\r\n\r\n\r\n// Columns\r\n//\r\n// Common styles for small and large grid columns\r\n\r\n@if $enable-grid-classes {\r\n  @include make-grid-columns();\r\n}\r\n", "// Grid system\r\n//\r\n// Generate semantic grid columns with these mixins.\r\n\r\n@mixin make-row($gutter: $grid-gutter-width) {\r\n  --#{$prefix}gutter-x: #{$gutter};\r\n  --#{$prefix}gutter-y: 0;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\r\n  margin-top: calc(-1 * var(--#{$prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\r\n  margin-right: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\r\n  margin-left: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\r\n}\r\n\r\n@mixin make-col-ready() {\r\n  // Add box sizing if only the grid is loaded\r\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\r\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\r\n  // always setting `width: 100%;`. This works because we set the width\r\n  // later on to override this initial width.\r\n  flex-shrink: 0;\r\n  width: 100%;\r\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\r\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\r\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\r\n  margin-top: var(--#{$prefix}gutter-y);\r\n}\r\n\r\n@mixin make-col($size: false, $columns: $grid-columns) {\r\n  @if $size {\r\n    flex: 0 0 auto;\r\n    width: percentage(divide($size, $columns));\r\n\r\n  } @else {\r\n    flex: 1 1 0;\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n@mixin make-col-auto() {\r\n  flex: 0 0 auto;\r\n  width: auto;\r\n}\r\n\r\n@mixin make-col-offset($size, $columns: $grid-columns) {\r\n  $num: divide($size, $columns);\r\n  margin-left: if($num == 0, 0, percentage($num));\r\n}\r\n\r\n// Row columns\r\n//\r\n// Specify on a parent element(e.g., .row) to force immediate children into NN\r\n// number of columns. Supports wrapping to new lines, but does not do a Masonry\r\n// style grid.\r\n@mixin row-cols($count) {\r\n  > * {\r\n    flex: 0 0 auto;\r\n    width: divide(100%, $count);\r\n  }\r\n}\r\n\r\n// Framework grid generation\r\n//\r\n// Used only by Bootstrap to generate the correct number of grid classes given\r\n// any value of `$grid-columns`.\r\n\r\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\r\n  @each $breakpoint in map-keys($breakpoints) {\r\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\r\n\r\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\r\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\r\n      .col#{$infix} {\r\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\r\n      }\r\n\r\n      .row-cols#{$infix}-auto > * {\r\n        @include make-col-auto();\r\n      }\r\n\r\n      @if $grid-row-columns > 0 {\r\n        @for $i from 1 through $grid-row-columns {\r\n          .row-cols#{$infix}-#{$i} {\r\n            @include row-cols($i);\r\n          }\r\n        }\r\n      }\r\n\r\n      .col#{$infix}-auto {\r\n        @include make-col-auto();\r\n      }\r\n\r\n      @if $columns > 0 {\r\n        @for $i from 1 through $columns {\r\n          .col#{$infix}-#{$i} {\r\n            @include make-col($i, $columns);\r\n          }\r\n        }\r\n\r\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\r\n        @for $i from 0 through ($columns - 1) {\r\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\r\n            .offset#{$infix}-#{$i} {\r\n              @include make-col-offset($i, $columns);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Gutters\r\n      //\r\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\r\n      @each $key, $value in $gutters {\r\n        .g#{$infix}-#{$key},\r\n        .gx#{$infix}-#{$key} {\r\n          --#{$prefix}gutter-x: #{$value};\r\n        }\r\n\r\n        .g#{$infix}-#{$key},\r\n        .gy#{$infix}-#{$key} {\r\n          --#{$prefix}gutter-y: #{$value};\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\r\n  @each $breakpoint in map-keys($breakpoints) {\r\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\r\n\r\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\r\n      @if $columns > 0 {\r\n        @for $i from 1 through $columns {\r\n          .g-col#{$infix}-#{$i} {\r\n            grid-column: auto / span $i;\r\n          }\r\n        }\r\n\r\n        // Start with `1` because `0` is and invalid value.\r\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\r\n        @for $i from 1 through ($columns - 1) {\r\n          .g-start#{$infix}-#{$i} {\r\n            grid-column-start: $i;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Utility generator\r\n// Used to generate utilities & print utilities\r\n@mixin generate-utility($utility, $infix: \"\", $is-rfs-media-query: false) {\r\n  $values: map-get($utility, values);\r\n\r\n  // If the values are a list or string, convert it into a map\r\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\r\n    $values: zip($values, $values);\r\n  }\r\n\r\n  @each $key, $value in $values {\r\n    $properties: map-get($utility, property);\r\n\r\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\r\n    @if type-of($properties) == \"string\" {\r\n      $properties: append((), $properties);\r\n    }\r\n\r\n    // Use custom class if present\r\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\r\n    $property-class: if($property-class == null, \"\", $property-class);\r\n\r\n    // Use custom CSS variable name if present, otherwise default to `class`\r\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\r\n\r\n    // State params to generate pseudo-classes\r\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\r\n\r\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\r\n\r\n    // Don't prefix if value key is null (e.g. with shadow class)\r\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\r\n\r\n    @if map-get($utility, rfs) {\r\n      // Inside the media query\r\n      @if $is-rfs-media-query {\r\n        $val: rfs-value($value);\r\n\r\n        // Do not render anything if fluid and non fluid values are the same\r\n        $value: if($val == rfs-fluid-value($value), null, $val);\r\n      }\r\n      @else {\r\n        $value: rfs-fluid-value($value);\r\n      }\r\n    }\r\n\r\n    $is-css-var: map-get($utility, css-var);\r\n    $is-local-vars: map-get($utility, local-vars);\r\n    $is-rtl: map-get($utility, rtl);\r\n\r\n    @if $value != null {\r\n      @if $is-rtl == false {\r\n        /* rtl:begin:remove */\r\n      }\r\n\r\n      @if $is-css-var {\r\n        .#{$property-class + $infix + $property-class-modifier} {\r\n          --#{$prefix}#{$css-variable-name}: #{$value};\r\n        }\r\n\r\n        @each $pseudo in $state {\r\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\r\n            --#{$prefix}#{$css-variable-name}: #{$value};\r\n          }\r\n        }\r\n      } @else {\r\n        .#{$property-class + $infix + $property-class-modifier} {\r\n          @each $property in $properties {\r\n            @if $is-local-vars {\r\n              @each $local-var, $variable in $is-local-vars {\r\n                --#{$prefix}#{$local-var}: #{$variable};\r\n              }\r\n            }\r\n            #{$property}: $value if($enable-important-utilities, !important, null);\r\n          }\r\n        }\r\n\r\n        @each $pseudo in $state {\r\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\r\n            @each $property in $properties {\r\n              @if $is-local-vars {\r\n                @each $local-var, $variable in $is-local-vars {\r\n                  --#{$prefix}#{$local-var}: #{$variable};\r\n                }\r\n              }\r\n              #{$property}: $value if($enable-important-utilities, !important, null);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      @if $is-rtl == false {\r\n        /* rtl:end:remove */\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Loop over each breakpoint\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n\r\n  // Generate media query if needed\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    // Loop over each utility property\r\n    @each $key, $utility in $utilities {\r\n      // The utility can be disabled with `false`, thus check if the utility is a map first\r\n      // Only proceed if responsive media queries are enabled or if it's the base media query\r\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\r\n        @include generate-utility($utility, $infix);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// RFS rescaling\r\n@media (min-width: $rfs-mq-value) {\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\r\n      // Loop over each utility property\r\n      @each $key, $utility in $utilities {\r\n        // The utility can be disabled with `false`, thus check if the utility is a map first\r\n        // Only proceed if responsive media queries are enabled or if it's the base media query\r\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\r\n          @include generate-utility($utility, $infix, true);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Print utilities\r\n@media print {\r\n  @each $key, $utility in $utilities {\r\n    // The utility can be disabled with `false`, thus check if the utility is a map first\r\n    // Then check if the utility needs print styles\r\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\r\n      @include generate-utility($utility, \"-print\");\r\n    }\r\n  }\r\n}\r\n"]}