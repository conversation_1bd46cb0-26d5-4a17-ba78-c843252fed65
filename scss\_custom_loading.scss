.loaderScreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 9999;
    padding: 64px;
    text-align: center;
    font-family: Arial, Helvetica, sans-serif;
    cursor: progress;
    .loaderText{
        color: #555555;
        font-size: 26px;
    }
    
    &.softLoading {
        z-index: 998;
        background-color: rgba(255,255,255,0.6);
    }
    
    .skeleton {
        position: absolute;
        top: 30%;
        left: 50%;
        -moz-transform: translateX(-50%) translateY(-50%);
        -webkit-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(-50%);
    }
}



.buymedLoader {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}
.buymedLoader div {
    display: inline-block;
    position: absolute;
    left: 10%;
    width: 20%;
    border-radius: 5px;
    background: #fff;
    animation: buymedLoaderAnimate 1s cubic-bezier(0, 0.5, 0.5, 1) infinite;
    box-shadow: 2px 2px 3px #777;
}
.buymedLoader div:nth-child(1) {
    left: 10%;
    animation-delay: -0.24s;
}
.buymedLoader div:nth-child(2) {
    left: 40%;
    animation-delay: -0.12s;
}
.buymedLoader div:nth-child(3) {
    left: 70%;
    animation-delay: 0s;
}
@keyframes buymedLoaderAnimate {
    0% {
        top: 10%;
        height: 80%;
    }
    50%, 100% {
        top: 30%;
        height: 40%;
    }
}