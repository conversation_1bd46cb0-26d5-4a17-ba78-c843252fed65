import {
	<PERSON><PERSON>,
	Card,
	CardBody,
	Col,
	FormInput,
	FormLabel,
	FormSwitch,
	Row,
	Tooltip,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { isEmptyObject } from "@buymed/solidjs-component/utils";
import { Show } from "solid-js";
import SaveIcon from "~icons/mdi/content-save";
import { useForm } from "~/contexts/FormContext";
import { BENEFICIARY_STATUS } from "~/services/tender/beneficiary.model";

export function BenaficiaryForm() {
	const { form, errors, isSubmitting, data, isDirty } = useForm();
	const { t } = useTranslate();

	return (
		<Row class="gap-3">
			<form ref={form}>
				<Card>
					<CardBody>
						<section class="d-flex flex-column row-gap-3">
							<header class="section-header">Thông tin chung</header>
							<Row class="row-gap-3">
								<Col xs={12} md={6} lg={4}>
									<FormInput
										name="name"
										id="name"
										label="Tên khách hàng"
										placeholder="Nhập tên khách hàng"
										invalid={!isEmptyObject(errors("name"))}
										feedbackInvalid={errors("name")}
										required
									/>
								</Col>
								<Col xs={12} md={6} lg={4}>
									<FormInput
										name="phoneNumber"
										id="phoneNumber"
										label="Số điện thoại"
										invalid={!isEmptyObject(errors("phoneNumber"))}
										feedbackInvalid={errors("phoneNumber")}
										required
									/>
								</Col>
								<Col xs={12} md={6} lg={4}>
									<FormInput
										name="taxCode"
										id="taxCode"
										label="Mã số thuế"
										invalid={!isEmptyObject(errors("taxCode"))}
										feedbackInvalid={errors("taxCode")}
										required
									/>
								</Col>
								<Col xs={12} md={6} lg={4}>
									<FormInput
										name="address"
										id="address"
										label="Địa chỉ"
										invalid={!isEmptyObject(errors("address"))}
										feedbackInvalid={errors("address")}
										required
									/>
								</Col>
								<Col>
									<FormInput
										name="email"
										id="email"
										label="Email"
										invalid={!isEmptyObject(errors("email"))}
										feedbackInvalid={errors("email")}
									/>
								</Col>
								<Col
									xs={12}
									md={4}
									style={{
										display: "flex",
										"flex-direction": "column",
										"justify-content": "space-evenly",
									}}
								>
									<FormLabel>Trạng thái</FormLabel>
									<div>
										<FormSwitch
											id="statusSwitch"
											name="status"
											checked={data().status === BENEFICIARY_STATUS.ACTIVE}
											style={{ width: "3rem", height: "1.5rem" }}
										/>
									</div>
								</Col>
							</Row>
						</section>
					</CardBody>
				</Card>
				<Show when={true}>
					<div class="submit-wrapper">
						<Tooltip content={t`common:button.save`}>
							<Button
								loading={isSubmitting()}
								color="success"
								disabled={!isDirty()}
								class="ms-2"
								type="submit"
							>
								<SaveIcon class="fs-5" />
								Lưu thông tin
							</Button>
						</Tooltip>
					</div>
				</Show>
			</form>
		</Row>
	);
}
