{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_root.scss", "../../scss/vendor/_rfs.scss", "bootstrap-utilities.css", "../../scss/mixins/_color-mode.scss", "../../scss/mixins/_clearfix.scss", "../../scss/helpers/_color-bg.scss", "../../scss/helpers/_colored-links.scss", "../../scss/helpers/_focus-ring.scss", "../../scss/helpers/_icon-link.scss", "../../scss/mixins/_transition.scss", "../../scss/helpers/_ratio.scss", "../../scss/helpers/_position.scss", "../../scss/_variables.scss", "../../scss/mixins/_breakpoints.scss", "../../scss/helpers/_stacks.scss", "../../scss/helpers/_visually-hidden.scss", "../../scss/mixins/_visually-hidden.scss", "../../scss/helpers/_stretched-link.scss", "../../scss/helpers/_text-truncation.scss", "../../scss/mixins/_text-truncate.scss", "../../scss/helpers/_vr.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AACE;;;;EAAA;ACDF;;EASI,kBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,iBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,mBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,gBAAA;EAAA,kBAAA;EAAA,uBAAA;EAIA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAIA,qBAAA;EAAA,uBAAA;EAAA,qBAAA;EAAA,kBAAA;EAAA,qBAAA;EAAA,oBAAA;EAAA,mBAAA;EAAA,kBAAA;EAIA,2BAAA;EAAA,iCAAA;EAAA,2BAAA;EAAA,0BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,yBAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAIA,+BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,+BAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAGF,6BAAA;EACA,uBAAA;EAMA,qNAAA;EACA,yGAAA;EACA,yFAAA;EAOA,gDAAA;ECiPI,yBALI;ED1OR,0BAAA;EACA,0BAAA;EAKA,wBAAA;EACA,+BAAA;EACA,kBAAA;EACA,+BAAA;EAEA,yBAAA;EACA,gCAAA;EAEA,4CAAA;EACA,oCAAA;EACA,0BAAA;EACA,oCAAA;EAEA,0CAAA;EACA,mCAAA;EACA,yBAAA;EACA,mCAAA;EAOA,wBAAA;EACA,8BAAA;EACA,+BAAA;EAEA,8BAAA;EACA,qCAAA;EAMA,wBAAA;EACA,0BAAA;EAGA,sBAAA;EACA,wBAAA;EACA,0BAAA;EACA,mDAAA;EAEA,4BAAA;EACA,8BAAA;EACA,6BAAA;EACA,2BAAA;EACA,4BAAA;EACA,mDAAA;EACA,8BAAA;EAGA,kDAAA;EACA,2DAAA;EACA,oDAAA;EACA,2DAAA;EAIA,8BAAA;EACA,6BAAA;EACA,4CAAA;EAIA,8BAAA;EACA,qCAAA;EACA,gCAAA;EACA,uCAAA;AENF;;AC3GI;EHuHA,kBAAA;EAGA,wBAAA;EACA,kCAAA;EACA,qBAAA;EACA,4BAAA;EAEA,yBAAA;EACA,sCAAA;EAEA,+CAAA;EACA,uCAAA;EACA,0BAAA;EACA,iCAAA;EAEA,6CAAA;EACA,sCAAA;EACA,yBAAA;EACA,gCAAA;EAGE,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAIA,+BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,+BAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAOF,wBAAA;EACA,8BAAA;EACA,kCAAA;EACA,wCAAA;EAEA,wBAAA;EAEA,0BAAA;EACA,wDAAA;EAEA,8BAAA;EACA,qCAAA;EACA,gCAAA;EACA,uCAAA;AETJ;;AE7KE;EACE,cAAA;EACA,WAAA;EACA,WAAA;AFgLJ;;AGhLE;EACE,sBAAA;EACA,qEAAA;AHmLJ;;AGrLE;EACE,sBAAA;EACA,yEAAA;AHwLJ;;AG1LE;EACE,sBAAA;EACA,qEAAA;AH6LJ;;AG/LE;EACE,sBAAA;EACA,uEAAA;AHkMJ;;AGpME;EACE,sBAAA;EACA,uEAAA;AHuMJ;;AGzME;EACE,sBAAA;EACA,uEAAA;AH4MJ;;AG9ME;EACE,sBAAA;EACA,yEAAA;AHiNJ;;AGnNE;EACE,sBAAA;EACA,sEAAA;AHsNJ;;AIzNE;EACE,6DAAA;EACA,uFAAA;AJ4NJ;AIzNM;EAGE,iDAAA;EACA,2EAAA;AJyNR;;AIlOE;EACE,+DAAA;EACA,yFAAA;AJqOJ;AIlOM;EAGE,qDAAA;EACA,+EAAA;AJkOR;;AI3OE;EACE,6DAAA;EACA,uFAAA;AJ8OJ;AI3OM;EAGE,iDAAA;EACA,2EAAA;AJ2OR;;AIpPE;EACE,0DAAA;EACA,oFAAA;AJuPJ;AIpPM;EAGE,mDAAA;EACA,6EAAA;AJoPR;;AI7PE;EACE,6DAAA;EACA,uFAAA;AJgQJ;AI7PM;EAGE,oDAAA;EACA,8EAAA;AJ6PR;;AItQE;EACE,4DAAA;EACA,sFAAA;AJyQJ;AItQM;EAGE,kDAAA;EACA,4EAAA;AJsQR;;AI/QE;EACE,2DAAA;EACA,qFAAA;AJkRJ;AI/QM;EAGE,qDAAA;EACA,+EAAA;AJ+QR;;AIxRE;EACE,0DAAA;EACA,oFAAA;AJ2RJ;AIxRM;EAGE,kDAAA;EACA,4EAAA;AJwRR;;AIjRA;EACE,oEAAA;EACA,8FAAA;AJoRF;AIjRI;EAEE,uEAAA;EACA,iGAAA;AJkRN;;AK9SA;EACE,UAAA;EAEA,kJAAA;ALgTF;;AMnTA;EACE,oBAAA;EACA,aAAA;EACA,mBAAA;EACA,kFAAA;EACA,6BAAA;EACA,2BAAA;ANsTF;AMpTE;EACE,cAAA;EACA,UAAA;EACA,WAAA;ECKE,sCDJF;ANsTJ;AO9SM;EDZJ;ICaM,gBAAA;EPiTN;AACF;;AMpTI;EACE,mEAAA;ANuTN;;AQzUA;EACE,kBAAA;EACA,WAAA;AR4UF;AQ1UE;EACE,cAAA;EACA,mCAAA;EACA,WAAA;AR4UJ;AQzUE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;AR2UJ;;AQtUE;EACE,uBAAA;ARyUJ;;AQ1UE;EACE,sBAAA;AR6UJ;;AQ9UE;EACE,yBAAA;ARiVJ;;AQlVE;EACE,iCAAA;ARqVJ;;AS1WA;EACE,eAAA;EACA,MAAA;EACA,QAAA;EACA,OAAA;EACA,aCsnCkC;AVzwBpC;;AS1WA;EACE,eAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,aC8mCkC;AVjwBpC;;ASrWI;EACE,gBAAA;EACA,MAAA;EACA,aCkmC8B;AV1vBpC;;ASrWI;EACE,gBAAA;EACA,SAAA;EACA,aC4lC8B;AVpvBpC;;AWzUI;EFxCA;IACE,gBAAA;IACA,MAAA;IACA,aCkmC8B;EV7uBlC;ESlXE;IACE,gBAAA;IACA,SAAA;IACA,aC4lC8B;EVxuBlC;AACF;AWtVI;EFxCA;IACE,gBAAA;IACA,MAAA;IACA,aCkmC8B;EVjuBlC;ES9XE;IACE,gBAAA;IACA,SAAA;IACA,aC4lC8B;EV5tBlC;AACF;AWlWI;EFxCA;IACE,gBAAA;IACA,MAAA;IACA,aCkmC8B;EVrtBlC;ES1YE;IACE,gBAAA;IACA,SAAA;IACA,aC4lC8B;EVhtBlC;AACF;AW9WI;EFxCA;IACE,gBAAA;IACA,MAAA;IACA,aCkmC8B;EVzsBlC;EStZE;IACE,gBAAA;IACA,SAAA;IACA,aC4lC8B;EVpsBlC;AACF;AW1XI;EFxCA;IACE,gBAAA;IACA,MAAA;IACA,aCkmC8B;EV7rBlC;ESlaE;IACE,gBAAA;IACA,SAAA;IACA,aC4lC8B;EVxrBlC;AACF;AYpcA;EACE,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,mBAAA;AZscF;;AYncA;EACE,aAAA;EACA,cAAA;EACA,sBAAA;EACA,mBAAA;AZscF;;Aa9cA;;ECIE,qBAAA;EACA,sBAAA;EACA,qBAAA;EACA,uBAAA;EACA,2BAAA;EACA,iCAAA;EACA,8BAAA;EACA,oBAAA;Ad+cF;Ac5cE;;EACE,6BAAA;Ad+cJ;;Ae7dE;EACE,kBAAA;EACA,MAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,ULudsC;EKtdtC,WAAA;AfgeJ;;AgBxeA;ECAE,gBAAA;EACA,uBAAA;EACA,mBAAA;AjB4eF;;AkBlfA;EACE,qBAAA;EACA,mBAAA;EACA,UAAA;EACA,eAAA;EACA,8BAAA;EACA,aRitB4B;AV5N9B;;AmBzbQ;EAOI,mCAAA;AnBsbZ;;AmB7bQ;EAOI,8BAAA;AnB0bZ;;AmBjcQ;EAOI,iCAAA;AnB8bZ;;AmBrcQ;EAOI,iCAAA;AnBkcZ;;AmBzcQ;EAOI,sCAAA;AnBscZ;;AmB7cQ;EAOI,mCAAA;AnB0cZ;;AmBjdQ;EAOI,sBAAA;AnB8cZ;;AmBrdQ;EAOI,uBAAA;AnBkdZ;;AmBzdQ;EAOI,sBAAA;AnBsdZ;;AmB7dQ;EAOI,iCAAA;EAAA,8BAAA;AnB0dZ;;AmBjeQ;EAOI,+BAAA;EAAA,4BAAA;AnB8dZ;;AmBreQ;EAOI,8BAAA;EAAA,2BAAA;AnBkeZ;;AmBzeQ;EAOI,oCAAA;EAAA,iCAAA;AnBseZ;;AmB7eQ;EAOI,8BAAA;EAAA,2BAAA;AnB0eZ;;AmBjfQ;EAOI,qBAAA;AnB8eZ;;AmBrfQ;EAOI,wBAAA;AnBkfZ;;AmBzfQ;EAOI,uBAAA;AnBsfZ;;AmB7fQ;EAOI,wBAAA;AnB0fZ;;AmBjgBQ;EAOI,qBAAA;AnB8fZ;;AmBrgBQ;EAOI,yBAAA;AnBkgBZ;;AmBzgBQ;EAOI,2BAAA;AnBsgBZ;;AmB7gBQ;EAOI,4BAAA;AnB0gBZ;;AmBjhBQ;EAOI,2BAAA;AnB8gBZ;;AmBrhBQ;EAOI,2BAAA;AnBkhBZ;;AmBzhBQ;EAOI,6BAAA;AnBshBZ;;AmB7hBQ;EAOI,8BAAA;AnB0hBZ;;AmBjiBQ;EAOI,6BAAA;AnB8hBZ;;AmBriBQ;EAOI,2BAAA;AnBkiBZ;;AmBziBQ;EAOI,6BAAA;AnBsiBZ;;AmB7iBQ;EAOI,8BAAA;AnB0iBZ;;AmBjjBQ;EAOI,6BAAA;AnB8iBZ;;AmBrjBQ;EAOI,0BAAA;AnBkjBZ;;AmBzjBQ;EAOI,gCAAA;AnBsjBZ;;AmB7jBQ;EAOI,yBAAA;AnB0jBZ;;AmBjkBQ;EAOI,wBAAA;AnB8jBZ;;AmBrkBQ;EAOI,yBAAA;AnBkkBZ;;AmBzkBQ;EAOI,6BAAA;AnBskBZ;;AmB7kBQ;EAOI,8BAAA;AnB0kBZ;;AmBjlBQ;EAOI,wBAAA;AnB8kBZ;;AmBrlBQ;EAOI,+BAAA;AnBklBZ;;AmBzlBQ;EAOI,wBAAA;AnBslBZ;;AmB7lBQ;EAOI,wDAAA;AnB0lBZ;;AmBjmBQ;EAOI,8DAAA;AnB8lBZ;;AmBrmBQ;EAOI,uDAAA;AnBkmBZ;;AmBzmBQ;EAOI,2BAAA;AnBsmBZ;;AmBvnBQ;EACE,gFAAA;AnB0nBV;;AmB3nBQ;EACE,kFAAA;AnB8nBV;;AmB/nBQ;EACE,gFAAA;AnBkoBV;;AmBnoBQ;EACE,6EAAA;AnBsoBV;;AmBvoBQ;EACE,gFAAA;AnB0oBV;;AmB3oBQ;EACE,+EAAA;AnB8oBV;;AmB/oBQ;EACE,8EAAA;AnBkpBV;;AmBnpBQ;EACE,6EAAA;AnBspBV;;AmB7oBQ;EAOI,2BAAA;AnB0oBZ;;AmBjpBQ;EAOI,6BAAA;AnB8oBZ;;AmBrpBQ;EAOI,6BAAA;AnBkpBZ;;AmBzpBQ;EAOI,0BAAA;AnBspBZ;;AmB7pBQ;EAOI,2BAAA;AnB0pBZ;;AmBjqBQ;EAOI,iBAAA;AnB8pBZ;;AmBrqBQ;EAOI,mBAAA;AnBkqBZ;;AmBzqBQ;EAOI,oBAAA;AnBsqBZ;;AmB7qBQ;EAOI,oBAAA;AnB0qBZ;;AmBjrBQ;EAOI,sBAAA;AnB8qBZ;;AmBrrBQ;EAOI,uBAAA;AnBkrBZ;;AmBzrBQ;EAOI,kBAAA;AnBsrBZ;;AmB7rBQ;EAOI,oBAAA;AnB0rBZ;;AmBjsBQ;EAOI,qBAAA;AnB8rBZ;;AmBrsBQ;EAOI,mBAAA;AnBksBZ;;AmBzsBQ;EAOI,qBAAA;AnBssBZ;;AmB7sBQ;EAOI,sBAAA;AnB0sBZ;;AmBjtBQ;EAOI,2CAAA;AnB8sBZ;;AmBrtBQ;EAOI,sCAAA;AnBktBZ;;AmBztBQ;EAOI,sCAAA;AnBstBZ;;AmB7tBQ;EAOI,uFAAA;AnB0tBZ;;AmBjuBQ;EAOI,oBAAA;AnB8tBZ;;AmBruBQ;EAOI,2FAAA;AnBkuBZ;;AmBzuBQ;EAOI,wBAAA;AnBsuBZ;;AmB7uBQ;EAOI,6FAAA;AnB0uBZ;;AmBjvBQ;EAOI,0BAAA;AnB8uBZ;;AmBrvBQ;EAOI,8FAAA;AnBkvBZ;;AmBzvBQ;EAOI,2BAAA;AnBsvBZ;;AmB7vBQ;EAOI,4FAAA;AnB0vBZ;;AmBjwBQ;EAOI,yBAAA;AnB8vBZ;;AmBrwBQ;EAIQ,sBAAA;EAGJ,8EAAA;AnBmwBZ;;AmB1wBQ;EAIQ,sBAAA;EAGJ,gFAAA;AnBwwBZ;;AmB/wBQ;EAIQ,sBAAA;EAGJ,8EAAA;AnB6wBZ;;AmBpxBQ;EAIQ,sBAAA;EAGJ,2EAAA;AnBkxBZ;;AmBzxBQ;EAIQ,sBAAA;EAGJ,8EAAA;AnBuxBZ;;AmB9xBQ;EAIQ,sBAAA;EAGJ,6EAAA;AnB4xBZ;;AmBnyBQ;EAIQ,sBAAA;EAGJ,4EAAA;AnBiyBZ;;AmBxyBQ;EAIQ,sBAAA;EAGJ,2EAAA;AnBsyBZ;;AmB7yBQ;EAIQ,sBAAA;EAGJ,4EAAA;AnB2yBZ;;AmBlzBQ;EAIQ,sBAAA;EAGJ,4EAAA;AnBgzBZ;;AmBvzBQ;EAOI,wDAAA;AnBozBZ;;AmB3zBQ;EAOI,0DAAA;AnBwzBZ;;AmB/zBQ;EAOI,wDAAA;AnB4zBZ;;AmBn0BQ;EAOI,qDAAA;AnBg0BZ;;AmBv0BQ;EAOI,wDAAA;AnBo0BZ;;AmB30BQ;EAOI,uDAAA;AnBw0BZ;;AmB/0BQ;EAOI,sDAAA;AnB40BZ;;AmBn1BQ;EAOI,qDAAA;AnBg1BZ;;AmBv1BQ;EAOI,4BAAA;AnBo1BZ;;AmB31BQ;EAOI,4BAAA;AnBw1BZ;;AmB/1BQ;EAOI,4BAAA;AnB41BZ;;AmBn2BQ;EAOI,4BAAA;AnBg2BZ;;AmBv2BQ;EAOI,4BAAA;AnBo2BZ;;AmBr3BQ;EACE,wBAAA;AnBw3BV;;AmBz3BQ;EACE,yBAAA;AnB43BV;;AmB73BQ;EACE,wBAAA;AnBg4BV;;AmBj4BQ;EACE,yBAAA;AnBo4BV;;AmBr4BQ;EACE,sBAAA;AnBw4BV;;AmB/3BQ;EAOI,qBAAA;AnB43BZ;;AmBn4BQ;EAOI,qBAAA;AnBg4BZ;;AmBv4BQ;EAOI,qBAAA;AnBo4BZ;;AmB34BQ;EAOI,sBAAA;AnBw4BZ;;AmB/4BQ;EAOI,sBAAA;AnB44BZ;;AmBn5BQ;EAOI,0BAAA;AnBg5BZ;;AmBv5BQ;EAOI,uBAAA;AnBo5BZ;;AmB35BQ;EAOI,2BAAA;AnBw5BZ;;AmB/5BQ;EAOI,sBAAA;AnB45BZ;;AmBn6BQ;EAOI,sBAAA;AnBg6BZ;;AmBv6BQ;EAOI,sBAAA;AnBo6BZ;;AmB36BQ;EAOI,uBAAA;AnBw6BZ;;AmB/6BQ;EAOI,uBAAA;AnB46BZ;;AmBn7BQ;EAOI,2BAAA;AnBg7BZ;;AmBv7BQ;EAOI,wBAAA;AnBo7BZ;;AmB37BQ;EAOI,4BAAA;AnBw7BZ;;AmB/7BQ;EAOI,yBAAA;AnB47BZ;;AmBn8BQ;EAOI,8BAAA;AnBg8BZ;;AmBv8BQ;EAOI,iCAAA;AnBo8BZ;;AmB38BQ;EAOI,sCAAA;AnBw8BZ;;AmB/8BQ;EAOI,yCAAA;AnB48BZ;;AmBn9BQ;EAOI,uBAAA;AnBg9BZ;;AmBv9BQ;EAOI,uBAAA;AnBo9BZ;;AmB39BQ;EAOI,yBAAA;AnBw9BZ;;AmB/9BQ;EAOI,yBAAA;AnB49BZ;;AmBn+BQ;EAOI,0BAAA;AnBg+BZ;;AmBv+BQ;EAOI,4BAAA;AnBo+BZ;;AmB3+BQ;EAOI,kCAAA;AnBw+BZ;;AmB/+BQ;EAOI,sCAAA;AnB4+BZ;;AmBn/BQ;EAOI,oCAAA;AnBg/BZ;;AmBv/BQ;EAOI,kCAAA;AnBo/BZ;;AmB3/BQ;EAOI,yCAAA;AnBw/BZ;;AmB//BQ;EAOI,wCAAA;AnB4/BZ;;AmBngCQ;EAOI,wCAAA;AnBggCZ;;AmBvgCQ;EAOI,kCAAA;AnBogCZ;;AmB3gCQ;EAOI,gCAAA;AnBwgCZ;;AmB/gCQ;EAOI,8BAAA;AnB4gCZ;;AmBnhCQ;EAOI,gCAAA;AnBghCZ;;AmBvhCQ;EAOI,+BAAA;AnBohCZ;;AmB3hCQ;EAOI,oCAAA;AnBwhCZ;;AmB/hCQ;EAOI,kCAAA;AnB4hCZ;;AmBniCQ;EAOI,gCAAA;AnBgiCZ;;AmBviCQ;EAOI,uCAAA;AnBoiCZ;;AmB3iCQ;EAOI,sCAAA;AnBwiCZ;;AmB/iCQ;EAOI,iCAAA;AnB4iCZ;;AmBnjCQ;EAOI,2BAAA;AnBgjCZ;;AmBvjCQ;EAOI,iCAAA;AnBojCZ;;AmB3jCQ;EAOI,+BAAA;AnBwjCZ;;AmB/jCQ;EAOI,6BAAA;AnB4jCZ;;AmBnkCQ;EAOI,+BAAA;AnBgkCZ;;AmBvkCQ;EAOI,8BAAA;AnBokCZ;;AmB3kCQ;EAOI,oBAAA;AnBwkCZ;;AmB/kCQ;EAOI,mBAAA;AnB4kCZ;;AmBnlCQ;EAOI,mBAAA;AnBglCZ;;AmBvlCQ;EAOI,mBAAA;AnBolCZ;;AmB3lCQ;EAOI,mBAAA;AnBwlCZ;;AmB/lCQ;EAOI,mBAAA;AnB4lCZ;;AmBnmCQ;EAOI,mBAAA;AnBgmCZ;;AmBvmCQ;EAOI,mBAAA;AnBomCZ;;AmB3mCQ;EAOI,oBAAA;AnBwmCZ;;AmB/mCQ;EAOI,0BAAA;AnB4mCZ;;AmBnnCQ;EAOI,yBAAA;AnBgnCZ;;AmBvnCQ;EAOI,uBAAA;AnBonCZ;;AmB3nCQ;EAOI,yBAAA;AnBwnCZ;;AmB/nCQ;EAOI,uBAAA;AnB4nCZ;;AmBnoCQ;EAOI,uBAAA;AnBgoCZ;;AmBvoCQ;EAOI,0BAAA;EAAA,yBAAA;AnBqoCZ;;AmB5oCQ;EAOI,gCAAA;EAAA,+BAAA;AnB0oCZ;;AmBjpCQ;EAOI,+BAAA;EAAA,8BAAA;AnB+oCZ;;AmBtpCQ;EAOI,6BAAA;EAAA,4BAAA;AnBopCZ;;AmB3pCQ;EAOI,+BAAA;EAAA,8BAAA;AnBypCZ;;AmBhqCQ;EAOI,6BAAA;EAAA,4BAAA;AnB8pCZ;;AmBrqCQ;EAOI,6BAAA;EAAA,4BAAA;AnBmqCZ;;AmB1qCQ;EAOI,wBAAA;EAAA,2BAAA;AnBwqCZ;;AmB/qCQ;EAOI,8BAAA;EAAA,iCAAA;AnB6qCZ;;AmBprCQ;EAOI,6BAAA;EAAA,gCAAA;AnBkrCZ;;AmBzrCQ;EAOI,2BAAA;EAAA,8BAAA;AnBurCZ;;AmB9rCQ;EAOI,6BAAA;EAAA,gCAAA;AnB4rCZ;;AmBnsCQ;EAOI,2BAAA;EAAA,8BAAA;AnBisCZ;;AmBxsCQ;EAOI,2BAAA;EAAA,8BAAA;AnBssCZ;;AmB7sCQ;EAOI,wBAAA;AnB0sCZ;;AmBjtCQ;EAOI,8BAAA;AnB8sCZ;;AmBrtCQ;EAOI,6BAAA;AnBktCZ;;AmBztCQ;EAOI,2BAAA;AnBstCZ;;AmB7tCQ;EAOI,6BAAA;AnB0tCZ;;AmBjuCQ;EAOI,2BAAA;AnB8tCZ;;AmBruCQ;EAOI,2BAAA;AnBkuCZ;;AmBzuCQ;EAOI,0BAAA;AnBsuCZ;;AmB7uCQ;EAOI,gCAAA;AnB0uCZ;;AmBjvCQ;EAOI,+BAAA;AnB8uCZ;;AmBrvCQ;EAOI,6BAAA;AnBkvCZ;;AmBzvCQ;EAOI,+BAAA;AnBsvCZ;;AmB7vCQ;EAOI,6BAAA;AnB0vCZ;;AmBjwCQ;EAOI,6BAAA;AnB8vCZ;;AmBrwCQ;EAOI,2BAAA;AnBkwCZ;;AmBzwCQ;EAOI,iCAAA;AnBswCZ;;AmB7wCQ;EAOI,gCAAA;AnB0wCZ;;AmBjxCQ;EAOI,8BAAA;AnB8wCZ;;AmBrxCQ;EAOI,gCAAA;AnBkxCZ;;AmBzxCQ;EAOI,8BAAA;AnBsxCZ;;AmB7xCQ;EAOI,8BAAA;AnB0xCZ;;AmBjyCQ;EAOI,yBAAA;AnB8xCZ;;AmBryCQ;EAOI,+BAAA;AnBkyCZ;;AmBzyCQ;EAOI,8BAAA;AnBsyCZ;;AmB7yCQ;EAOI,4BAAA;AnB0yCZ;;AmBjzCQ;EAOI,8BAAA;AnB8yCZ;;AmBrzCQ;EAOI,4BAAA;AnBkzCZ;;AmBzzCQ;EAOI,4BAAA;AnBszCZ;;AmB7zCQ;EAOI,qBAAA;AnB0zCZ;;AmBj0CQ;EAOI,2BAAA;AnB8zCZ;;AmBr0CQ;EAOI,0BAAA;AnBk0CZ;;AmBz0CQ;EAOI,wBAAA;AnBs0CZ;;AmB70CQ;EAOI,0BAAA;AnB00CZ;;AmBj1CQ;EAOI,wBAAA;AnB80CZ;;AmBr1CQ;EAOI,2BAAA;EAAA,0BAAA;AnBm1CZ;;AmB11CQ;EAOI,iCAAA;EAAA,gCAAA;AnBw1CZ;;AmB/1CQ;EAOI,gCAAA;EAAA,+BAAA;AnB61CZ;;AmBp2CQ;EAOI,8BAAA;EAAA,6BAAA;AnBk2CZ;;AmBz2CQ;EAOI,gCAAA;EAAA,+BAAA;AnBu2CZ;;AmB92CQ;EAOI,8BAAA;EAAA,6BAAA;AnB42CZ;;AmBn3CQ;EAOI,yBAAA;EAAA,4BAAA;AnBi3CZ;;AmBx3CQ;EAOI,+BAAA;EAAA,kCAAA;AnBs3CZ;;AmB73CQ;EAOI,8BAAA;EAAA,iCAAA;AnB23CZ;;AmBl4CQ;EAOI,4BAAA;EAAA,+BAAA;AnBg4CZ;;AmBv4CQ;EAOI,8BAAA;EAAA,iCAAA;AnBq4CZ;;AmB54CQ;EAOI,4BAAA;EAAA,+BAAA;AnB04CZ;;AmBj5CQ;EAOI,yBAAA;AnB84CZ;;AmBr5CQ;EAOI,+BAAA;AnBk5CZ;;AmBz5CQ;EAOI,8BAAA;AnBs5CZ;;AmB75CQ;EAOI,4BAAA;AnB05CZ;;AmBj6CQ;EAOI,8BAAA;AnB85CZ;;AmBr6CQ;EAOI,4BAAA;AnBk6CZ;;AmBz6CQ;EAOI,2BAAA;AnBs6CZ;;AmB76CQ;EAOI,iCAAA;AnB06CZ;;AmBj7CQ;EAOI,gCAAA;AnB86CZ;;AmBr7CQ;EAOI,8BAAA;AnBk7CZ;;AmBz7CQ;EAOI,gCAAA;AnBs7CZ;;AmB77CQ;EAOI,8BAAA;AnB07CZ;;AmBj8CQ;EAOI,4BAAA;AnB87CZ;;AmBr8CQ;EAOI,kCAAA;AnBk8CZ;;AmBz8CQ;EAOI,iCAAA;AnBs8CZ;;AmB78CQ;EAOI,+BAAA;AnB08CZ;;AmBj9CQ;EAOI,iCAAA;AnB88CZ;;AmBr9CQ;EAOI,+BAAA;AnBk9CZ;;AmBz9CQ;EAOI,0BAAA;AnBs9CZ;;AmB79CQ;EAOI,gCAAA;AnB09CZ;;AmBj+CQ;EAOI,+BAAA;AnB89CZ;;AmBr+CQ;EAOI,6BAAA;AnBk+CZ;;AmBz+CQ;EAOI,+BAAA;AnBs+CZ;;AmB7+CQ;EAOI,6BAAA;AnB0+CZ;;AmBj/CQ;EAOI,iBAAA;AnB8+CZ;;AmBr/CQ;EAOI,uBAAA;AnBk/CZ;;AmBz/CQ;EAOI,sBAAA;AnBs/CZ;;AmB7/CQ;EAOI,oBAAA;AnB0/CZ;;AmBjgDQ;EAOI,sBAAA;AnB8/CZ;;AmBrgDQ;EAOI,oBAAA;AnBkgDZ;;AmBzgDQ;EAOI,qBAAA;AnBsgDZ;;AmB7gDQ;EAOI,2BAAA;AnB0gDZ;;AmBjhDQ;EAOI,0BAAA;AnB8gDZ;;AmBrhDQ;EAOI,wBAAA;AnBkhDZ;;AmBzhDQ;EAOI,0BAAA;AnBshDZ;;AmB7hDQ;EAOI,wBAAA;AnB0hDZ;;AmBjiDQ;EAOI,6BAAA;EAAA,wBAAA;AnB8hDZ;;AmBriDQ;EAOI,mCAAA;EAAA,8BAAA;AnBkiDZ;;AmBziDQ;EAOI,kCAAA;EAAA,6BAAA;AnBsiDZ;;AmB7iDQ;EAOI,gCAAA;EAAA,2BAAA;AnB0iDZ;;AmBjjDQ;EAOI,kCAAA;EAAA,6BAAA;AnB8iDZ;;AmBrjDQ;EAOI,gCAAA;EAAA,2BAAA;AnBkjDZ;;AmBzjDQ;EAOI,gDAAA;AnBsjDZ;;AmB7jDQ;EAOI,4CAAA;AnB0jDZ;;AmBjkDQ;EAOI,4CAAA;AnB8jDZ;;AmBrkDQ;EAOI,0CAAA;AnBkkDZ;;AmBzkDQ;EAOI,4CAAA;AnBskDZ;;AmB7kDQ;EAOI,6BAAA;AnB0kDZ;;AmBjlDQ;EAOI,0BAAA;AnB8kDZ;;AmBrlDQ;EAOI,6BAAA;AnBklDZ;;AmBzlDQ;EAOI,6BAAA;AnBslDZ;;AmB7lDQ;EAOI,+BAAA;AnB0lDZ;;AmBjmDQ;EAOI,2BAAA;AnB8lDZ;;AmBrmDQ;EAOI,2BAAA;AnBkmDZ;;AmBzmDQ;EAOI,2BAAA;AnBsmDZ;;AmB7mDQ;EAOI,2BAAA;AnB0mDZ;;AmBjnDQ;EAOI,2BAAA;AnB8mDZ;;AmBrnDQ;EAOI,8BAAA;AnBknDZ;;AmBznDQ;EAOI,yBAAA;AnBsnDZ;;AmB7nDQ;EAOI,4BAAA;AnB0nDZ;;AmBjoDQ;EAOI,2BAAA;AnB8nDZ;;AmBroDQ;EAOI,yBAAA;AnBkoDZ;;AmBzoDQ;EAOI,2BAAA;AnBsoDZ;;AmB7oDQ;EAOI,4BAAA;AnB0oDZ;;AmBjpDQ;EAOI,6BAAA;AnB8oDZ;;AmBrpDQ;EAOI,gCAAA;AnBkpDZ;;AmBzpDQ;EAOI,qCAAA;AnBspDZ;;AmB7pDQ;EAOI,wCAAA;AnB0pDZ;;AmBjqDQ;EAOI,oCAAA;AnB8pDZ;;AmBrqDQ;EAOI,oCAAA;AnBkqDZ;;AmBzqDQ;EAOI,qCAAA;AnBsqDZ;;AmB7qDQ;EAOI,8BAAA;AnB0qDZ;;AmBjrDQ;EAOI,8BAAA;AnB8qDZ;;AmBnsDQ,qBAAA;AAcA;EAOI,gCAAA;EAAA,iCAAA;AnBorDZ;;AmBjqDQ,mBAAA;AA1BA;EAIQ,oBAAA;EAGJ,qEAAA;AnB0rDZ;;AmBjsDQ;EAIQ,oBAAA;EAGJ,uEAAA;AnB+rDZ;;AmBtsDQ;EAIQ,oBAAA;EAGJ,qEAAA;AnBosDZ;;AmB3sDQ;EAIQ,oBAAA;EAGJ,kEAAA;AnBysDZ;;AmBhtDQ;EAIQ,oBAAA;EAGJ,qEAAA;AnB8sDZ;;AmBrtDQ;EAIQ,oBAAA;EAGJ,oEAAA;AnBmtDZ;;AmB1tDQ;EAIQ,oBAAA;EAGJ,mEAAA;AnBwtDZ;;AmB/tDQ;EAIQ,oBAAA;EAGJ,kEAAA;AnB6tDZ;;AmBpuDQ;EAIQ,oBAAA;EAGJ,mEAAA;AnBkuDZ;;AmBzuDQ;EAIQ,oBAAA;EAGJ,mEAAA;AnBuuDZ;;AmB9uDQ;EAIQ,oBAAA;EAGJ,wEAAA;AnB4uDZ;;AmBnvDQ;EAIQ,oBAAA;EAGJ,2CAAA;AnBivDZ;;AmBxvDQ;EAIQ,oBAAA;EAGJ,oCAAA;AnBsvDZ;;AmB7vDQ;EAIQ,oBAAA;EAGJ,0CAAA;AnB2vDZ;;AmBlwDQ;EAIQ,oBAAA;EAGJ,2CAAA;AnBgwDZ;;AmBvwDQ;EAIQ,oBAAA;EAGJ,0CAAA;AnBqwDZ;;AmB5wDQ;EAIQ,oBAAA;EAGJ,0CAAA;AnB0wDZ;;AmBjxDQ;EAIQ,oBAAA;EAGJ,yBAAA;AnB+wDZ;;AmBhyDQ;EACE,uBAAA;AnBmyDV;;AmBpyDQ;EACE,sBAAA;AnBuyDV;;AmBxyDQ;EACE,uBAAA;AnB2yDV;;AmB5yDQ;EACE,oBAAA;AnB+yDV;;AmBtyDQ;EAOI,iDAAA;AnBmyDZ;;AmB1yDQ;EAOI,mDAAA;AnBuyDZ;;AmB9yDQ;EAOI,iDAAA;AnB2yDZ;;AmBlzDQ;EAOI,8CAAA;AnB+yDZ;;AmBtzDQ;EAOI,iDAAA;AnBmzDZ;;AmB1zDQ;EAOI,gDAAA;AnBuzDZ;;AmB9zDQ;EAOI,+CAAA;AnB2zDZ;;AmBl0DQ;EAOI,8CAAA;AnB+zDZ;;AmBh1DQ;EACE,sBAAA;AnBm1DV;;AmB/0DU;EACE,sBAAA;AnBk1DZ;;AmBx1DQ;EACE,uBAAA;AnB21DV;;AmBv1DU;EACE,uBAAA;AnB01DZ;;AmBh2DQ;EACE,sBAAA;AnBm2DV;;AmB/1DU;EACE,sBAAA;AnBk2DZ;;AmBx2DQ;EACE,uBAAA;AnB22DV;;AmBv2DU;EACE,uBAAA;AnB02DZ;;AmBh3DQ;EACE,oBAAA;AnBm3DV;;AmB/2DU;EACE,oBAAA;AnBk3DZ;;AmB92DQ;EAOI,yCAAA;AnB22DZ;;AmBt2DU;EAOI,yCAAA;AnBm2Dd;;AmBt3DQ;EAOI,wCAAA;AnBm3DZ;;AmB92DU;EAOI,wCAAA;AnB22Dd;;AmB93DQ;EAOI,yCAAA;AnB23DZ;;AmBt3DU;EAOI,yCAAA;AnBm3Dd;;AmBt4DQ;EAIQ,8BAAA;EAGJ,+FAAA;AnBo4DZ;;AmB34DQ;EAIQ,8BAAA;EAGJ,iGAAA;AnBy4DZ;;AmBh5DQ;EAIQ,8BAAA;EAGJ,+FAAA;AnB84DZ;;AmBr5DQ;EAIQ,8BAAA;EAGJ,4FAAA;AnBm5DZ;;AmB15DQ;EAIQ,8BAAA;EAGJ,+FAAA;AnBw5DZ;;AmB/5DQ;EAIQ,8BAAA;EAGJ,8FAAA;AnB65DZ;;AmBp6DQ;EAIQ,8BAAA;EAGJ,6FAAA;AnBk6DZ;;AmBz6DQ;EAIQ,8BAAA;EAGJ,4FAAA;AnBu6DZ;;AmB96DQ;EAIQ,8BAAA;EAGJ,qGAAA;AnB46DZ;;AmB77DQ;EACE,gCAAA;AnBg8DV;;AmB57DU;EACE,gCAAA;AnB+7DZ;;AmBr8DQ;EACE,iCAAA;AnBw8DV;;AmBp8DU;EACE,iCAAA;AnBu8DZ;;AmB78DQ;EACE,gCAAA;AnBg9DV;;AmB58DU;EACE,gCAAA;AnB+8DZ;;AmBr9DQ;EACE,iCAAA;AnBw9DV;;AmBp9DU;EACE,iCAAA;AnBu9DZ;;AmB79DQ;EACE,8BAAA;AnBg+DV;;AmB59DU;EACE,8BAAA;AnB+9DZ;;AmB39DQ;EAIQ,kBAAA;EAGJ,8EAAA;AnBy9DZ;;AmBh+DQ;EAIQ,kBAAA;EAGJ,gFAAA;AnB89DZ;;AmBr+DQ;EAIQ,kBAAA;EAGJ,8EAAA;AnBm+DZ;;AmB1+DQ;EAIQ,kBAAA;EAGJ,2EAAA;AnBw+DZ;;AmB/+DQ;EAIQ,kBAAA;EAGJ,8EAAA;AnB6+DZ;;AmBp/DQ;EAIQ,kBAAA;EAGJ,6EAAA;AnBk/DZ;;AmBz/DQ;EAIQ,kBAAA;EAGJ,4EAAA;AnBu/DZ;;AmB9/DQ;EAIQ,kBAAA;EAGJ,2EAAA;AnB4/DZ;;AmBngEQ;EAIQ,kBAAA;EAGJ,4EAAA;AnBigEZ;;AmBxgEQ;EAIQ,kBAAA;EAGJ,4EAAA;AnBsgEZ;;AmB7gEQ;EAIQ,kBAAA;EAGJ,8EAAA;AnB2gEZ;;AmBlhEQ;EAIQ,kBAAA;EAGJ,wCAAA;AnBghEZ;;AmBvhEQ;EAIQ,kBAAA;EAGJ,mFAAA;AnBqhEZ;;AmB5hEQ;EAIQ,kBAAA;EAGJ,kFAAA;AnB0hEZ;;AmB3iEQ;EACE,oBAAA;AnB8iEV;;AmB/iEQ;EACE,qBAAA;AnBkjEV;;AmBnjEQ;EACE,oBAAA;AnBsjEV;;AmBvjEQ;EACE,qBAAA;AnB0jEV;;AmB3jEQ;EACE,kBAAA;AnB8jEV;;AmBrjEQ;EAOI,wDAAA;AnBkjEZ;;AmBzjEQ;EAOI,0DAAA;AnBsjEZ;;AmB7jEQ;EAOI,wDAAA;AnB0jEZ;;AmBjkEQ;EAOI,qDAAA;AnB8jEZ;;AmBrkEQ;EAOI,wDAAA;AnBkkEZ;;AmBzkEQ;EAOI,uDAAA;AnBskEZ;;AmB7kEQ;EAOI,sDAAA;AnB0kEZ;;AmBjlEQ;EAOI,qDAAA;AnB8kEZ;;AmBrlEQ;EAOI,+CAAA;AnBklEZ;;AmBzlEQ;EAOI,mCAAA;EAAA,gCAAA;EAAA,2BAAA;AnBslEZ;;AmB7lEQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;AnB0lEZ;;AmBjmEQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;AnB8lEZ;;AmBrmEQ;EAOI,+BAAA;AnBkmEZ;;AmBzmEQ;EAOI,+BAAA;AnBsmEZ;;AmB7mEQ;EAOI,iDAAA;AnB0mEZ;;AmBjnEQ;EAOI,2BAAA;AnB8mEZ;;AmBrnEQ;EAOI,oDAAA;AnBknEZ;;AmBznEQ;EAOI,iDAAA;AnBsnEZ;;AmB7nEQ;EAOI,oDAAA;AnB0nEZ;;AmBjoEQ;EAOI,oDAAA;AnB8nEZ;;AmBroEQ;EAOI,qDAAA;AnBkoEZ;;AmBzoEQ;EAOI,6BAAA;AnBsoEZ;;AmB7oEQ;EAOI,sDAAA;AnB0oEZ;;AmBjpEQ;EAOI,0DAAA;EAAA,2DAAA;AnB+oEZ;;AmBtpEQ;EAOI,oCAAA;EAAA,qCAAA;AnBopEZ;;AmB3pEQ;EAOI,6DAAA;EAAA,8DAAA;AnBypEZ;;AmBhqEQ;EAOI,0DAAA;EAAA,2DAAA;AnB8pEZ;;AmBrqEQ;EAOI,6DAAA;EAAA,8DAAA;AnBmqEZ;;AmB1qEQ;EAOI,6DAAA;EAAA,8DAAA;AnBwqEZ;;AmB/qEQ;EAOI,8DAAA;EAAA,+DAAA;AnB6qEZ;;AmBprEQ;EAOI,sCAAA;EAAA,uCAAA;AnBkrEZ;;AmBzrEQ;EAOI,+DAAA;EAAA,gEAAA;AnBurEZ;;AmB9rEQ;EAOI,2DAAA;EAAA,8DAAA;AnB4rEZ;;AmBnsEQ;EAOI,qCAAA;EAAA,wCAAA;AnBisEZ;;AmBxsEQ;EAOI,8DAAA;EAAA,iEAAA;AnBssEZ;;AmB7sEQ;EAOI,2DAAA;EAAA,8DAAA;AnB2sEZ;;AmBltEQ;EAOI,8DAAA;EAAA,iEAAA;AnBgtEZ;;AmBvtEQ;EAOI,8DAAA;EAAA,iEAAA;AnBqtEZ;;AmB5tEQ;EAOI,+DAAA;EAAA,kEAAA;AnB0tEZ;;AmBjuEQ;EAOI,uCAAA;EAAA,0CAAA;AnB+tEZ;;AmBtuEQ;EAOI,gEAAA;EAAA,mEAAA;AnBouEZ;;AmB3uEQ;EAOI,8DAAA;EAAA,6DAAA;AnByuEZ;;AmBhvEQ;EAOI,wCAAA;EAAA,uCAAA;AnB8uEZ;;AmBrvEQ;EAOI,iEAAA;EAAA,gEAAA;AnBmvEZ;;AmB1vEQ;EAOI,8DAAA;EAAA,6DAAA;AnBwvEZ;;AmB/vEQ;EAOI,iEAAA;EAAA,gEAAA;AnB6vEZ;;AmBpwEQ;EAOI,iEAAA;EAAA,gEAAA;AnBkwEZ;;AmBzwEQ;EAOI,kEAAA;EAAA,iEAAA;AnBuwEZ;;AmB9wEQ;EAOI,0CAAA;EAAA,yCAAA;AnB4wEZ;;AmBnxEQ;EAOI,mEAAA;EAAA,kEAAA;AnBixEZ;;AmBxxEQ;EAOI,6DAAA;EAAA,0DAAA;AnBsxEZ;;AmB7xEQ;EAOI,uCAAA;EAAA,oCAAA;AnB2xEZ;;AmBlyEQ;EAOI,gEAAA;EAAA,6DAAA;AnBgyEZ;;AmBvyEQ;EAOI,6DAAA;EAAA,0DAAA;AnBqyEZ;;AmB5yEQ;EAOI,gEAAA;EAAA,6DAAA;AnB0yEZ;;AmBjzEQ;EAOI,gEAAA;EAAA,6DAAA;AnB+yEZ;;AmBtzEQ;EAOI,iEAAA;EAAA,8DAAA;AnBozEZ;;AmB3zEQ;EAOI,yCAAA;EAAA,sCAAA;AnByzEZ;;AmBh0EQ;EAOI,kEAAA;EAAA,+DAAA;AnB8zEZ;;AmBr0EQ;EAOI,8BAAA;AnBk0EZ;;AmBz0EQ;EAOI,6BAAA;AnBs0EZ;;AmB70EQ;EAOI,sBAAA;AnB00EZ;;AmBj1EQ;EAOI,qBAAA;AnB80EZ;;AmBr1EQ;EAOI,qBAAA;AnBk1EZ;;AmBz1EQ;EAOI,qBAAA;AnBs1EZ;;AmB71EQ;EAOI,qBAAA;AnB01EZ;;AWp2EI;EQGI;IAOI,sBAAA;EnB+1EV;EmBt2EM;IAOI,uBAAA;EnBk2EV;EmBz2EM;IAOI,sBAAA;EnBq2EV;EmB52EM;IAOI,iCAAA;IAAA,8BAAA;EnBw2EV;EmB/2EM;IAOI,+BAAA;IAAA,4BAAA;EnB22EV;EmBl3EM;IAOI,8BAAA;IAAA,2BAAA;EnB82EV;EmBr3EM;IAOI,oCAAA;IAAA,iCAAA;EnBi3EV;EmBx3EM;IAOI,8BAAA;IAAA,2BAAA;EnBo3EV;EmB33EM;IAOI,0BAAA;EnBu3EV;EmB93EM;IAOI,gCAAA;EnB03EV;EmBj4EM;IAOI,yBAAA;EnB63EV;EmBp4EM;IAOI,wBAAA;EnBg4EV;EmBv4EM;IAOI,yBAAA;EnBm4EV;EmB14EM;IAOI,6BAAA;EnBs4EV;EmB74EM;IAOI,8BAAA;EnBy4EV;EmBh5EM;IAOI,wBAAA;EnB44EV;EmBn5EM;IAOI,+BAAA;EnB+4EV;EmBt5EM;IAOI,wBAAA;EnBk5EV;EmBz5EM;IAOI,yBAAA;EnBq5EV;EmB55EM;IAOI,8BAAA;EnBw5EV;EmB/5EM;IAOI,iCAAA;EnB25EV;EmBl6EM;IAOI,sCAAA;EnB85EV;EmBr6EM;IAOI,yCAAA;EnBi6EV;EmBx6EM;IAOI,uBAAA;EnBo6EV;EmB36EM;IAOI,uBAAA;EnBu6EV;EmB96EM;IAOI,yBAAA;EnB06EV;EmBj7EM;IAOI,yBAAA;EnB66EV;EmBp7EM;IAOI,0BAAA;EnBg7EV;EmBv7EM;IAOI,4BAAA;EnBm7EV;EmB17EM;IAOI,kCAAA;EnBs7EV;EmB77EM;IAOI,sCAAA;EnBy7EV;EmBh8EM;IAOI,oCAAA;EnB47EV;EmBn8EM;IAOI,kCAAA;EnB+7EV;EmBt8EM;IAOI,yCAAA;EnBk8EV;EmBz8EM;IAOI,wCAAA;EnBq8EV;EmB58EM;IAOI,wCAAA;EnBw8EV;EmB/8EM;IAOI,kCAAA;EnB28EV;EmBl9EM;IAOI,gCAAA;EnB88EV;EmBr9EM;IAOI,8BAAA;EnBi9EV;EmBx9EM;IAOI,gCAAA;EnBo9EV;EmB39EM;IAOI,+BAAA;EnBu9EV;EmB99EM;IAOI,oCAAA;EnB09EV;EmBj+EM;IAOI,kCAAA;EnB69EV;EmBp+EM;IAOI,gCAAA;EnBg+EV;EmBv+EM;IAOI,uCAAA;EnBm+EV;EmB1+EM;IAOI,sCAAA;EnBs+EV;EmB7+EM;IAOI,iCAAA;EnBy+EV;EmBh/EM;IAOI,2BAAA;EnB4+EV;EmBn/EM;IAOI,iCAAA;EnB++EV;EmBt/EM;IAOI,+BAAA;EnBk/EV;EmBz/EM;IAOI,6BAAA;EnBq/EV;EmB5/EM;IAOI,+BAAA;EnBw/EV;EmB//EM;IAOI,8BAAA;EnB2/EV;EmBlgFM;IAOI,oBAAA;EnB8/EV;EmBrgFM;IAOI,mBAAA;EnBigFV;EmBxgFM;IAOI,mBAAA;EnBogFV;EmB3gFM;IAOI,mBAAA;EnBugFV;EmB9gFM;IAOI,mBAAA;EnB0gFV;EmBjhFM;IAOI,mBAAA;EnB6gFV;EmBphFM;IAOI,mBAAA;EnBghFV;EmBvhFM;IAOI,mBAAA;EnBmhFV;EmB1hFM;IAOI,oBAAA;EnBshFV;EmB7hFM;IAOI,0BAAA;EnByhFV;EmBhiFM;IAOI,yBAAA;EnB4hFV;EmBniFM;IAOI,uBAAA;EnB+hFV;EmBtiFM;IAOI,yBAAA;EnBkiFV;EmBziFM;IAOI,uBAAA;EnBqiFV;EmB5iFM;IAOI,uBAAA;EnBwiFV;EmB/iFM;IAOI,0BAAA;IAAA,yBAAA;EnB4iFV;EmBnjFM;IAOI,gCAAA;IAAA,+BAAA;EnBgjFV;EmBvjFM;IAOI,+BAAA;IAAA,8BAAA;EnBojFV;EmB3jFM;IAOI,6BAAA;IAAA,4BAAA;EnBwjFV;EmB/jFM;IAOI,+BAAA;IAAA,8BAAA;EnB4jFV;EmBnkFM;IAOI,6BAAA;IAAA,4BAAA;EnBgkFV;EmBvkFM;IAOI,6BAAA;IAAA,4BAAA;EnBokFV;EmB3kFM;IAOI,wBAAA;IAAA,2BAAA;EnBwkFV;EmB/kFM;IAOI,8BAAA;IAAA,iCAAA;EnB4kFV;EmBnlFM;IAOI,6BAAA;IAAA,gCAAA;EnBglFV;EmBvlFM;IAOI,2BAAA;IAAA,8BAAA;EnBolFV;EmB3lFM;IAOI,6BAAA;IAAA,gCAAA;EnBwlFV;EmB/lFM;IAOI,2BAAA;IAAA,8BAAA;EnB4lFV;EmBnmFM;IAOI,2BAAA;IAAA,8BAAA;EnBgmFV;EmBvmFM;IAOI,wBAAA;EnBmmFV;EmB1mFM;IAOI,8BAAA;EnBsmFV;EmB7mFM;IAOI,6BAAA;EnBymFV;EmBhnFM;IAOI,2BAAA;EnB4mFV;EmBnnFM;IAOI,6BAAA;EnB+mFV;EmBtnFM;IAOI,2BAAA;EnBknFV;EmBznFM;IAOI,2BAAA;EnBqnFV;EmB5nFM;IAOI,0BAAA;EnBwnFV;EmB/nFM;IAOI,gCAAA;EnB2nFV;EmBloFM;IAOI,+BAAA;EnB8nFV;EmBroFM;IAOI,6BAAA;EnBioFV;EmBxoFM;IAOI,+BAAA;EnBooFV;EmB3oFM;IAOI,6BAAA;EnBuoFV;EmB9oFM;IAOI,6BAAA;EnB0oFV;EmBjpFM;IAOI,2BAAA;EnB6oFV;EmBppFM;IAOI,iCAAA;EnBgpFV;EmBvpFM;IAOI,gCAAA;EnBmpFV;EmB1pFM;IAOI,8BAAA;EnBspFV;EmB7pFM;IAOI,gCAAA;EnBypFV;EmBhqFM;IAOI,8BAAA;EnB4pFV;EmBnqFM;IAOI,8BAAA;EnB+pFV;EmBtqFM;IAOI,yBAAA;EnBkqFV;EmBzqFM;IAOI,+BAAA;EnBqqFV;EmB5qFM;IAOI,8BAAA;EnBwqFV;EmB/qFM;IAOI,4BAAA;EnB2qFV;EmBlrFM;IAOI,8BAAA;EnB8qFV;EmBrrFM;IAOI,4BAAA;EnBirFV;EmBxrFM;IAOI,4BAAA;EnBorFV;EmB3rFM;IAOI,qBAAA;EnBurFV;EmB9rFM;IAOI,2BAAA;EnB0rFV;EmBjsFM;IAOI,0BAAA;EnB6rFV;EmBpsFM;IAOI,wBAAA;EnBgsFV;EmBvsFM;IAOI,0BAAA;EnBmsFV;EmB1sFM;IAOI,wBAAA;EnBssFV;EmB7sFM;IAOI,2BAAA;IAAA,0BAAA;EnB0sFV;EmBjtFM;IAOI,iCAAA;IAAA,gCAAA;EnB8sFV;EmBrtFM;IAOI,gCAAA;IAAA,+BAAA;EnBktFV;EmBztFM;IAOI,8BAAA;IAAA,6BAAA;EnBstFV;EmB7tFM;IAOI,gCAAA;IAAA,+BAAA;EnB0tFV;EmBjuFM;IAOI,8BAAA;IAAA,6BAAA;EnB8tFV;EmBruFM;IAOI,yBAAA;IAAA,4BAAA;EnBkuFV;EmBzuFM;IAOI,+BAAA;IAAA,kCAAA;EnBsuFV;EmB7uFM;IAOI,8BAAA;IAAA,iCAAA;EnB0uFV;EmBjvFM;IAOI,4BAAA;IAAA,+BAAA;EnB8uFV;EmBrvFM;IAOI,8BAAA;IAAA,iCAAA;EnBkvFV;EmBzvFM;IAOI,4BAAA;IAAA,+BAAA;EnBsvFV;EmB7vFM;IAOI,yBAAA;EnByvFV;EmBhwFM;IAOI,+BAAA;EnB4vFV;EmBnwFM;IAOI,8BAAA;EnB+vFV;EmBtwFM;IAOI,4BAAA;EnBkwFV;EmBzwFM;IAOI,8BAAA;EnBqwFV;EmB5wFM;IAOI,4BAAA;EnBwwFV;EmB/wFM;IAOI,2BAAA;EnB2wFV;EmBlxFM;IAOI,iCAAA;EnB8wFV;EmBrxFM;IAOI,gCAAA;EnBixFV;EmBxxFM;IAOI,8BAAA;EnBoxFV;EmB3xFM;IAOI,gCAAA;EnBuxFV;EmB9xFM;IAOI,8BAAA;EnB0xFV;EmBjyFM;IAOI,4BAAA;EnB6xFV;EmBpyFM;IAOI,kCAAA;EnBgyFV;EmBvyFM;IAOI,iCAAA;EnBmyFV;EmB1yFM;IAOI,+BAAA;EnBsyFV;EmB7yFM;IAOI,iCAAA;EnByyFV;EmBhzFM;IAOI,+BAAA;EnB4yFV;EmBnzFM;IAOI,0BAAA;EnB+yFV;EmBtzFM;IAOI,gCAAA;EnBkzFV;EmBzzFM;IAOI,+BAAA;EnBqzFV;EmB5zFM;IAOI,6BAAA;EnBwzFV;EmB/zFM;IAOI,+BAAA;EnB2zFV;EmBl0FM;IAOI,6BAAA;EnB8zFV;EmBr0FM;IAOI,iBAAA;EnBi0FV;EmBx0FM;IAOI,uBAAA;EnBo0FV;EmB30FM;IAOI,sBAAA;EnBu0FV;EmB90FM;IAOI,oBAAA;EnB00FV;EmBj1FM;IAOI,sBAAA;EnB60FV;EmBp1FM;IAOI,oBAAA;EnBg1FV;EmBv1FM;IAOI,qBAAA;EnBm1FV;EmB11FM;IAOI,2BAAA;EnBs1FV;EmB71FM;IAOI,0BAAA;EnBy1FV;EmBh2FM;IAOI,wBAAA;EnB41FV;EmBn2FM;IAOI,0BAAA;EnB+1FV;EmBt2FM;IAOI,wBAAA;EnBk2FV;EmBz2FM;IAOI,6BAAA;IAAA,wBAAA;EnBq2FV;EmB52FM;IAOI,mCAAA;IAAA,8BAAA;EnBw2FV;EmB/2FM;IAOI,kCAAA;IAAA,6BAAA;EnB22FV;EmBl3FM;IAOI,gCAAA;IAAA,2BAAA;EnB82FV;EmBr3FM;IAOI,kCAAA;IAAA,6BAAA;EnBi3FV;EmBx3FM;IAOI,gCAAA;IAAA,2BAAA;EnBo3FV;EmB33FM;IAOI,2BAAA;EnBu3FV;EmB93FM;IAOI,4BAAA;EnB03FV;EmBj4FM;IAOI,6BAAA;EnB63FV;AACF;AWx4FI;EQGI;IAOI,sBAAA;EnBk4FV;EmBz4FM;IAOI,uBAAA;EnBq4FV;EmB54FM;IAOI,sBAAA;EnBw4FV;EmB/4FM;IAOI,iCAAA;IAAA,8BAAA;EnB24FV;EmBl5FM;IAOI,+BAAA;IAAA,4BAAA;EnB84FV;EmBr5FM;IAOI,8BAAA;IAAA,2BAAA;EnBi5FV;EmBx5FM;IAOI,oCAAA;IAAA,iCAAA;EnBo5FV;EmB35FM;IAOI,8BAAA;IAAA,2BAAA;EnBu5FV;EmB95FM;IAOI,0BAAA;EnB05FV;EmBj6FM;IAOI,gCAAA;EnB65FV;EmBp6FM;IAOI,yBAAA;EnBg6FV;EmBv6FM;IAOI,wBAAA;EnBm6FV;EmB16FM;IAOI,yBAAA;EnBs6FV;EmB76FM;IAOI,6BAAA;EnBy6FV;EmBh7FM;IAOI,8BAAA;EnB46FV;EmBn7FM;IAOI,wBAAA;EnB+6FV;EmBt7FM;IAOI,+BAAA;EnBk7FV;EmBz7FM;IAOI,wBAAA;EnBq7FV;EmB57FM;IAOI,yBAAA;EnBw7FV;EmB/7FM;IAOI,8BAAA;EnB27FV;EmBl8FM;IAOI,iCAAA;EnB87FV;EmBr8FM;IAOI,sCAAA;EnBi8FV;EmBx8FM;IAOI,yCAAA;EnBo8FV;EmB38FM;IAOI,uBAAA;EnBu8FV;EmB98FM;IAOI,uBAAA;EnB08FV;EmBj9FM;IAOI,yBAAA;EnB68FV;EmBp9FM;IAOI,yBAAA;EnBg9FV;EmBv9FM;IAOI,0BAAA;EnBm9FV;EmB19FM;IAOI,4BAAA;EnBs9FV;EmB79FM;IAOI,kCAAA;EnBy9FV;EmBh+FM;IAOI,sCAAA;EnB49FV;EmBn+FM;IAOI,oCAAA;EnB+9FV;EmBt+FM;IAOI,kCAAA;EnBk+FV;EmBz+FM;IAOI,yCAAA;EnBq+FV;EmB5+FM;IAOI,wCAAA;EnBw+FV;EmB/+FM;IAOI,wCAAA;EnB2+FV;EmBl/FM;IAOI,kCAAA;EnB8+FV;EmBr/FM;IAOI,gCAAA;EnBi/FV;EmBx/FM;IAOI,8BAAA;EnBo/FV;EmB3/FM;IAOI,gCAAA;EnBu/FV;EmB9/FM;IAOI,+BAAA;EnB0/FV;EmBjgGM;IAOI,oCAAA;EnB6/FV;EmBpgGM;IAOI,kCAAA;EnBggGV;EmBvgGM;IAOI,gCAAA;EnBmgGV;EmB1gGM;IAOI,uCAAA;EnBsgGV;EmB7gGM;IAOI,sCAAA;EnBygGV;EmBhhGM;IAOI,iCAAA;EnB4gGV;EmBnhGM;IAOI,2BAAA;EnB+gGV;EmBthGM;IAOI,iCAAA;EnBkhGV;EmBzhGM;IAOI,+BAAA;EnBqhGV;EmB5hGM;IAOI,6BAAA;EnBwhGV;EmB/hGM;IAOI,+BAAA;EnB2hGV;EmBliGM;IAOI,8BAAA;EnB8hGV;EmBriGM;IAOI,oBAAA;EnBiiGV;EmBxiGM;IAOI,mBAAA;EnBoiGV;EmB3iGM;IAOI,mBAAA;EnBuiGV;EmB9iGM;IAOI,mBAAA;EnB0iGV;EmBjjGM;IAOI,mBAAA;EnB6iGV;EmBpjGM;IAOI,mBAAA;EnBgjGV;EmBvjGM;IAOI,mBAAA;EnBmjGV;EmB1jGM;IAOI,mBAAA;EnBsjGV;EmB7jGM;IAOI,oBAAA;EnByjGV;EmBhkGM;IAOI,0BAAA;EnB4jGV;EmBnkGM;IAOI,yBAAA;EnB+jGV;EmBtkGM;IAOI,uBAAA;EnBkkGV;EmBzkGM;IAOI,yBAAA;EnBqkGV;EmB5kGM;IAOI,uBAAA;EnBwkGV;EmB/kGM;IAOI,uBAAA;EnB2kGV;EmBllGM;IAOI,0BAAA;IAAA,yBAAA;EnB+kGV;EmBtlGM;IAOI,gCAAA;IAAA,+BAAA;EnBmlGV;EmB1lGM;IAOI,+BAAA;IAAA,8BAAA;EnBulGV;EmB9lGM;IAOI,6BAAA;IAAA,4BAAA;EnB2lGV;EmBlmGM;IAOI,+BAAA;IAAA,8BAAA;EnB+lGV;EmBtmGM;IAOI,6BAAA;IAAA,4BAAA;EnBmmGV;EmB1mGM;IAOI,6BAAA;IAAA,4BAAA;EnBumGV;EmB9mGM;IAOI,wBAAA;IAAA,2BAAA;EnB2mGV;EmBlnGM;IAOI,8BAAA;IAAA,iCAAA;EnB+mGV;EmBtnGM;IAOI,6BAAA;IAAA,gCAAA;EnBmnGV;EmB1nGM;IAOI,2BAAA;IAAA,8BAAA;EnBunGV;EmB9nGM;IAOI,6BAAA;IAAA,gCAAA;EnB2nGV;EmBloGM;IAOI,2BAAA;IAAA,8BAAA;EnB+nGV;EmBtoGM;IAOI,2BAAA;IAAA,8BAAA;EnBmoGV;EmB1oGM;IAOI,wBAAA;EnBsoGV;EmB7oGM;IAOI,8BAAA;EnByoGV;EmBhpGM;IAOI,6BAAA;EnB4oGV;EmBnpGM;IAOI,2BAAA;EnB+oGV;EmBtpGM;IAOI,6BAAA;EnBkpGV;EmBzpGM;IAOI,2BAAA;EnBqpGV;EmB5pGM;IAOI,2BAAA;EnBwpGV;EmB/pGM;IAOI,0BAAA;EnB2pGV;EmBlqGM;IAOI,gCAAA;EnB8pGV;EmBrqGM;IAOI,+BAAA;EnBiqGV;EmBxqGM;IAOI,6BAAA;EnBoqGV;EmB3qGM;IAOI,+BAAA;EnBuqGV;EmB9qGM;IAOI,6BAAA;EnB0qGV;EmBjrGM;IAOI,6BAAA;EnB6qGV;EmBprGM;IAOI,2BAAA;EnBgrGV;EmBvrGM;IAOI,iCAAA;EnBmrGV;EmB1rGM;IAOI,gCAAA;EnBsrGV;EmB7rGM;IAOI,8BAAA;EnByrGV;EmBhsGM;IAOI,gCAAA;EnB4rGV;EmBnsGM;IAOI,8BAAA;EnB+rGV;EmBtsGM;IAOI,8BAAA;EnBksGV;EmBzsGM;IAOI,yBAAA;EnBqsGV;EmB5sGM;IAOI,+BAAA;EnBwsGV;EmB/sGM;IAOI,8BAAA;EnB2sGV;EmBltGM;IAOI,4BAAA;EnB8sGV;EmBrtGM;IAOI,8BAAA;EnBitGV;EmBxtGM;IAOI,4BAAA;EnBotGV;EmB3tGM;IAOI,4BAAA;EnButGV;EmB9tGM;IAOI,qBAAA;EnB0tGV;EmBjuGM;IAOI,2BAAA;EnB6tGV;EmBpuGM;IAOI,0BAAA;EnBguGV;EmBvuGM;IAOI,wBAAA;EnBmuGV;EmB1uGM;IAOI,0BAAA;EnBsuGV;EmB7uGM;IAOI,wBAAA;EnByuGV;EmBhvGM;IAOI,2BAAA;IAAA,0BAAA;EnB6uGV;EmBpvGM;IAOI,iCAAA;IAAA,gCAAA;EnBivGV;EmBxvGM;IAOI,gCAAA;IAAA,+BAAA;EnBqvGV;EmB5vGM;IAOI,8BAAA;IAAA,6BAAA;EnByvGV;EmBhwGM;IAOI,gCAAA;IAAA,+BAAA;EnB6vGV;EmBpwGM;IAOI,8BAAA;IAAA,6BAAA;EnBiwGV;EmBxwGM;IAOI,yBAAA;IAAA,4BAAA;EnBqwGV;EmB5wGM;IAOI,+BAAA;IAAA,kCAAA;EnBywGV;EmBhxGM;IAOI,8BAAA;IAAA,iCAAA;EnB6wGV;EmBpxGM;IAOI,4BAAA;IAAA,+BAAA;EnBixGV;EmBxxGM;IAOI,8BAAA;IAAA,iCAAA;EnBqxGV;EmB5xGM;IAOI,4BAAA;IAAA,+BAAA;EnByxGV;EmBhyGM;IAOI,yBAAA;EnB4xGV;EmBnyGM;IAOI,+BAAA;EnB+xGV;EmBtyGM;IAOI,8BAAA;EnBkyGV;EmBzyGM;IAOI,4BAAA;EnBqyGV;EmB5yGM;IAOI,8BAAA;EnBwyGV;EmB/yGM;IAOI,4BAAA;EnB2yGV;EmBlzGM;IAOI,2BAAA;EnB8yGV;EmBrzGM;IAOI,iCAAA;EnBizGV;EmBxzGM;IAOI,gCAAA;EnBozGV;EmB3zGM;IAOI,8BAAA;EnBuzGV;EmB9zGM;IAOI,gCAAA;EnB0zGV;EmBj0GM;IAOI,8BAAA;EnB6zGV;EmBp0GM;IAOI,4BAAA;EnBg0GV;EmBv0GM;IAOI,kCAAA;EnBm0GV;EmB10GM;IAOI,iCAAA;EnBs0GV;EmB70GM;IAOI,+BAAA;EnBy0GV;EmBh1GM;IAOI,iCAAA;EnB40GV;EmBn1GM;IAOI,+BAAA;EnB+0GV;EmBt1GM;IAOI,0BAAA;EnBk1GV;EmBz1GM;IAOI,gCAAA;EnBq1GV;EmB51GM;IAOI,+BAAA;EnBw1GV;EmB/1GM;IAOI,6BAAA;EnB21GV;EmBl2GM;IAOI,+BAAA;EnB81GV;EmBr2GM;IAOI,6BAAA;EnBi2GV;EmBx2GM;IAOI,iBAAA;EnBo2GV;EmB32GM;IAOI,uBAAA;EnBu2GV;EmB92GM;IAOI,sBAAA;EnB02GV;EmBj3GM;IAOI,oBAAA;EnB62GV;EmBp3GM;IAOI,sBAAA;EnBg3GV;EmBv3GM;IAOI,oBAAA;EnBm3GV;EmB13GM;IAOI,qBAAA;EnBs3GV;EmB73GM;IAOI,2BAAA;EnBy3GV;EmBh4GM;IAOI,0BAAA;EnB43GV;EmBn4GM;IAOI,wBAAA;EnB+3GV;EmBt4GM;IAOI,0BAAA;EnBk4GV;EmBz4GM;IAOI,wBAAA;EnBq4GV;EmB54GM;IAOI,6BAAA;IAAA,wBAAA;EnBw4GV;EmB/4GM;IAOI,mCAAA;IAAA,8BAAA;EnB24GV;EmBl5GM;IAOI,kCAAA;IAAA,6BAAA;EnB84GV;EmBr5GM;IAOI,gCAAA;IAAA,2BAAA;EnBi5GV;EmBx5GM;IAOI,kCAAA;IAAA,6BAAA;EnBo5GV;EmB35GM;IAOI,gCAAA;IAAA,2BAAA;EnBu5GV;EmB95GM;IAOI,2BAAA;EnB05GV;EmBj6GM;IAOI,4BAAA;EnB65GV;EmBp6GM;IAOI,6BAAA;EnBg6GV;AACF;AW36GI;EQGI;IAOI,sBAAA;EnBq6GV;EmB56GM;IAOI,uBAAA;EnBw6GV;EmB/6GM;IAOI,sBAAA;EnB26GV;EmBl7GM;IAOI,iCAAA;IAAA,8BAAA;EnB86GV;EmBr7GM;IAOI,+BAAA;IAAA,4BAAA;EnBi7GV;EmBx7GM;IAOI,8BAAA;IAAA,2BAAA;EnBo7GV;EmB37GM;IAOI,oCAAA;IAAA,iCAAA;EnBu7GV;EmB97GM;IAOI,8BAAA;IAAA,2BAAA;EnB07GV;EmBj8GM;IAOI,0BAAA;EnB67GV;EmBp8GM;IAOI,gCAAA;EnBg8GV;EmBv8GM;IAOI,yBAAA;EnBm8GV;EmB18GM;IAOI,wBAAA;EnBs8GV;EmB78GM;IAOI,yBAAA;EnBy8GV;EmBh9GM;IAOI,6BAAA;EnB48GV;EmBn9GM;IAOI,8BAAA;EnB+8GV;EmBt9GM;IAOI,wBAAA;EnBk9GV;EmBz9GM;IAOI,+BAAA;EnBq9GV;EmB59GM;IAOI,wBAAA;EnBw9GV;EmB/9GM;IAOI,yBAAA;EnB29GV;EmBl+GM;IAOI,8BAAA;EnB89GV;EmBr+GM;IAOI,iCAAA;EnBi+GV;EmBx+GM;IAOI,sCAAA;EnBo+GV;EmB3+GM;IAOI,yCAAA;EnBu+GV;EmB9+GM;IAOI,uBAAA;EnB0+GV;EmBj/GM;IAOI,uBAAA;EnB6+GV;EmBp/GM;IAOI,yBAAA;EnBg/GV;EmBv/GM;IAOI,yBAAA;EnBm/GV;EmB1/GM;IAOI,0BAAA;EnBs/GV;EmB7/GM;IAOI,4BAAA;EnBy/GV;EmBhgHM;IAOI,kCAAA;EnB4/GV;EmBngHM;IAOI,sCAAA;EnB+/GV;EmBtgHM;IAOI,oCAAA;EnBkgHV;EmBzgHM;IAOI,kCAAA;EnBqgHV;EmB5gHM;IAOI,yCAAA;EnBwgHV;EmB/gHM;IAOI,wCAAA;EnB2gHV;EmBlhHM;IAOI,wCAAA;EnB8gHV;EmBrhHM;IAOI,kCAAA;EnBihHV;EmBxhHM;IAOI,gCAAA;EnBohHV;EmB3hHM;IAOI,8BAAA;EnBuhHV;EmB9hHM;IAOI,gCAAA;EnB0hHV;EmBjiHM;IAOI,+BAAA;EnB6hHV;EmBpiHM;IAOI,oCAAA;EnBgiHV;EmBviHM;IAOI,kCAAA;EnBmiHV;EmB1iHM;IAOI,gCAAA;EnBsiHV;EmB7iHM;IAOI,uCAAA;EnByiHV;EmBhjHM;IAOI,sCAAA;EnB4iHV;EmBnjHM;IAOI,iCAAA;EnB+iHV;EmBtjHM;IAOI,2BAAA;EnBkjHV;EmBzjHM;IAOI,iCAAA;EnBqjHV;EmB5jHM;IAOI,+BAAA;EnBwjHV;EmB/jHM;IAOI,6BAAA;EnB2jHV;EmBlkHM;IAOI,+BAAA;EnB8jHV;EmBrkHM;IAOI,8BAAA;EnBikHV;EmBxkHM;IAOI,oBAAA;EnBokHV;EmB3kHM;IAOI,mBAAA;EnBukHV;EmB9kHM;IAOI,mBAAA;EnB0kHV;EmBjlHM;IAOI,mBAAA;EnB6kHV;EmBplHM;IAOI,mBAAA;EnBglHV;EmBvlHM;IAOI,mBAAA;EnBmlHV;EmB1lHM;IAOI,mBAAA;EnBslHV;EmB7lHM;IAOI,mBAAA;EnBylHV;EmBhmHM;IAOI,oBAAA;EnB4lHV;EmBnmHM;IAOI,0BAAA;EnB+lHV;EmBtmHM;IAOI,yBAAA;EnBkmHV;EmBzmHM;IAOI,uBAAA;EnBqmHV;EmB5mHM;IAOI,yBAAA;EnBwmHV;EmB/mHM;IAOI,uBAAA;EnB2mHV;EmBlnHM;IAOI,uBAAA;EnB8mHV;EmBrnHM;IAOI,0BAAA;IAAA,yBAAA;EnBknHV;EmBznHM;IAOI,gCAAA;IAAA,+BAAA;EnBsnHV;EmB7nHM;IAOI,+BAAA;IAAA,8BAAA;EnB0nHV;EmBjoHM;IAOI,6BAAA;IAAA,4BAAA;EnB8nHV;EmBroHM;IAOI,+BAAA;IAAA,8BAAA;EnBkoHV;EmBzoHM;IAOI,6BAAA;IAAA,4BAAA;EnBsoHV;EmB7oHM;IAOI,6BAAA;IAAA,4BAAA;EnB0oHV;EmBjpHM;IAOI,wBAAA;IAAA,2BAAA;EnB8oHV;EmBrpHM;IAOI,8BAAA;IAAA,iCAAA;EnBkpHV;EmBzpHM;IAOI,6BAAA;IAAA,gCAAA;EnBspHV;EmB7pHM;IAOI,2BAAA;IAAA,8BAAA;EnB0pHV;EmBjqHM;IAOI,6BAAA;IAAA,gCAAA;EnB8pHV;EmBrqHM;IAOI,2BAAA;IAAA,8BAAA;EnBkqHV;EmBzqHM;IAOI,2BAAA;IAAA,8BAAA;EnBsqHV;EmB7qHM;IAOI,wBAAA;EnByqHV;EmBhrHM;IAOI,8BAAA;EnB4qHV;EmBnrHM;IAOI,6BAAA;EnB+qHV;EmBtrHM;IAOI,2BAAA;EnBkrHV;EmBzrHM;IAOI,6BAAA;EnBqrHV;EmB5rHM;IAOI,2BAAA;EnBwrHV;EmB/rHM;IAOI,2BAAA;EnB2rHV;EmBlsHM;IAOI,0BAAA;EnB8rHV;EmBrsHM;IAOI,gCAAA;EnBisHV;EmBxsHM;IAOI,+BAAA;EnBosHV;EmB3sHM;IAOI,6BAAA;EnBusHV;EmB9sHM;IAOI,+BAAA;EnB0sHV;EmBjtHM;IAOI,6BAAA;EnB6sHV;EmBptHM;IAOI,6BAAA;EnBgtHV;EmBvtHM;IAOI,2BAAA;EnBmtHV;EmB1tHM;IAOI,iCAAA;EnBstHV;EmB7tHM;IAOI,gCAAA;EnBytHV;EmBhuHM;IAOI,8BAAA;EnB4tHV;EmBnuHM;IAOI,gCAAA;EnB+tHV;EmBtuHM;IAOI,8BAAA;EnBkuHV;EmBzuHM;IAOI,8BAAA;EnBquHV;EmB5uHM;IAOI,yBAAA;EnBwuHV;EmB/uHM;IAOI,+BAAA;EnB2uHV;EmBlvHM;IAOI,8BAAA;EnB8uHV;EmBrvHM;IAOI,4BAAA;EnBivHV;EmBxvHM;IAOI,8BAAA;EnBovHV;EmB3vHM;IAOI,4BAAA;EnBuvHV;EmB9vHM;IAOI,4BAAA;EnB0vHV;EmBjwHM;IAOI,qBAAA;EnB6vHV;EmBpwHM;IAOI,2BAAA;EnBgwHV;EmBvwHM;IAOI,0BAAA;EnBmwHV;EmB1wHM;IAOI,wBAAA;EnBswHV;EmB7wHM;IAOI,0BAAA;EnBywHV;EmBhxHM;IAOI,wBAAA;EnB4wHV;EmBnxHM;IAOI,2BAAA;IAAA,0BAAA;EnBgxHV;EmBvxHM;IAOI,iCAAA;IAAA,gCAAA;EnBoxHV;EmB3xHM;IAOI,gCAAA;IAAA,+BAAA;EnBwxHV;EmB/xHM;IAOI,8BAAA;IAAA,6BAAA;EnB4xHV;EmBnyHM;IAOI,gCAAA;IAAA,+BAAA;EnBgyHV;EmBvyHM;IAOI,8BAAA;IAAA,6BAAA;EnBoyHV;EmB3yHM;IAOI,yBAAA;IAAA,4BAAA;EnBwyHV;EmB/yHM;IAOI,+BAAA;IAAA,kCAAA;EnB4yHV;EmBnzHM;IAOI,8BAAA;IAAA,iCAAA;EnBgzHV;EmBvzHM;IAOI,4BAAA;IAAA,+BAAA;EnBozHV;EmB3zHM;IAOI,8BAAA;IAAA,iCAAA;EnBwzHV;EmB/zHM;IAOI,4BAAA;IAAA,+BAAA;EnB4zHV;EmBn0HM;IAOI,yBAAA;EnB+zHV;EmBt0HM;IAOI,+BAAA;EnBk0HV;EmBz0HM;IAOI,8BAAA;EnBq0HV;EmB50HM;IAOI,4BAAA;EnBw0HV;EmB/0HM;IAOI,8BAAA;EnB20HV;EmBl1HM;IAOI,4BAAA;EnB80HV;EmBr1HM;IAOI,2BAAA;EnBi1HV;EmBx1HM;IAOI,iCAAA;EnBo1HV;EmB31HM;IAOI,gCAAA;EnBu1HV;EmB91HM;IAOI,8BAAA;EnB01HV;EmBj2HM;IAOI,gCAAA;EnB61HV;EmBp2HM;IAOI,8BAAA;EnBg2HV;EmBv2HM;IAOI,4BAAA;EnBm2HV;EmB12HM;IAOI,kCAAA;EnBs2HV;EmB72HM;IAOI,iCAAA;EnBy2HV;EmBh3HM;IAOI,+BAAA;EnB42HV;EmBn3HM;IAOI,iCAAA;EnB+2HV;EmBt3HM;IAOI,+BAAA;EnBk3HV;EmBz3HM;IAOI,0BAAA;EnBq3HV;EmB53HM;IAOI,gCAAA;EnBw3HV;EmB/3HM;IAOI,+BAAA;EnB23HV;EmBl4HM;IAOI,6BAAA;EnB83HV;EmBr4HM;IAOI,+BAAA;EnBi4HV;EmBx4HM;IAOI,6BAAA;EnBo4HV;EmB34HM;IAOI,iBAAA;EnBu4HV;EmB94HM;IAOI,uBAAA;EnB04HV;EmBj5HM;IAOI,sBAAA;EnB64HV;EmBp5HM;IAOI,oBAAA;EnBg5HV;EmBv5HM;IAOI,sBAAA;EnBm5HV;EmB15HM;IAOI,oBAAA;EnBs5HV;EmB75HM;IAOI,qBAAA;EnBy5HV;EmBh6HM;IAOI,2BAAA;EnB45HV;EmBn6HM;IAOI,0BAAA;EnB+5HV;EmBt6HM;IAOI,wBAAA;EnBk6HV;EmBz6HM;IAOI,0BAAA;EnBq6HV;EmB56HM;IAOI,wBAAA;EnBw6HV;EmB/6HM;IAOI,6BAAA;IAAA,wBAAA;EnB26HV;EmBl7HM;IAOI,mCAAA;IAAA,8BAAA;EnB86HV;EmBr7HM;IAOI,kCAAA;IAAA,6BAAA;EnBi7HV;EmBx7HM;IAOI,gCAAA;IAAA,2BAAA;EnBo7HV;EmB37HM;IAOI,kCAAA;IAAA,6BAAA;EnBu7HV;EmB97HM;IAOI,gCAAA;IAAA,2BAAA;EnB07HV;EmBj8HM;IAOI,2BAAA;EnB67HV;EmBp8HM;IAOI,4BAAA;EnBg8HV;EmBv8HM;IAOI,6BAAA;EnBm8HV;AACF;AW98HI;EQGI;IAOI,sBAAA;EnBw8HV;EmB/8HM;IAOI,uBAAA;EnB28HV;EmBl9HM;IAOI,sBAAA;EnB88HV;EmBr9HM;IAOI,iCAAA;IAAA,8BAAA;EnBi9HV;EmBx9HM;IAOI,+BAAA;IAAA,4BAAA;EnBo9HV;EmB39HM;IAOI,8BAAA;IAAA,2BAAA;EnBu9HV;EmB99HM;IAOI,oCAAA;IAAA,iCAAA;EnB09HV;EmBj+HM;IAOI,8BAAA;IAAA,2BAAA;EnB69HV;EmBp+HM;IAOI,0BAAA;EnBg+HV;EmBv+HM;IAOI,gCAAA;EnBm+HV;EmB1+HM;IAOI,yBAAA;EnBs+HV;EmB7+HM;IAOI,wBAAA;EnBy+HV;EmBh/HM;IAOI,yBAAA;EnB4+HV;EmBn/HM;IAOI,6BAAA;EnB++HV;EmBt/HM;IAOI,8BAAA;EnBk/HV;EmBz/HM;IAOI,wBAAA;EnBq/HV;EmB5/HM;IAOI,+BAAA;EnBw/HV;EmB//HM;IAOI,wBAAA;EnB2/HV;EmBlgIM;IAOI,yBAAA;EnB8/HV;EmBrgIM;IAOI,8BAAA;EnBigIV;EmBxgIM;IAOI,iCAAA;EnBogIV;EmB3gIM;IAOI,sCAAA;EnBugIV;EmB9gIM;IAOI,yCAAA;EnB0gIV;EmBjhIM;IAOI,uBAAA;EnB6gIV;EmBphIM;IAOI,uBAAA;EnBghIV;EmBvhIM;IAOI,yBAAA;EnBmhIV;EmB1hIM;IAOI,yBAAA;EnBshIV;EmB7hIM;IAOI,0BAAA;EnByhIV;EmBhiIM;IAOI,4BAAA;EnB4hIV;EmBniIM;IAOI,kCAAA;EnB+hIV;EmBtiIM;IAOI,sCAAA;EnBkiIV;EmBziIM;IAOI,oCAAA;EnBqiIV;EmB5iIM;IAOI,kCAAA;EnBwiIV;EmB/iIM;IAOI,yCAAA;EnB2iIV;EmBljIM;IAOI,wCAAA;EnB8iIV;EmBrjIM;IAOI,wCAAA;EnBijIV;EmBxjIM;IAOI,kCAAA;EnBojIV;EmB3jIM;IAOI,gCAAA;EnBujIV;EmB9jIM;IAOI,8BAAA;EnB0jIV;EmBjkIM;IAOI,gCAAA;EnB6jIV;EmBpkIM;IAOI,+BAAA;EnBgkIV;EmBvkIM;IAOI,oCAAA;EnBmkIV;EmB1kIM;IAOI,kCAAA;EnBskIV;EmB7kIM;IAOI,gCAAA;EnBykIV;EmBhlIM;IAOI,uCAAA;EnB4kIV;EmBnlIM;IAOI,sCAAA;EnB+kIV;EmBtlIM;IAOI,iCAAA;EnBklIV;EmBzlIM;IAOI,2BAAA;EnBqlIV;EmB5lIM;IAOI,iCAAA;EnBwlIV;EmB/lIM;IAOI,+BAAA;EnB2lIV;EmBlmIM;IAOI,6BAAA;EnB8lIV;EmBrmIM;IAOI,+BAAA;EnBimIV;EmBxmIM;IAOI,8BAAA;EnBomIV;EmB3mIM;IAOI,oBAAA;EnBumIV;EmB9mIM;IAOI,mBAAA;EnB0mIV;EmBjnIM;IAOI,mBAAA;EnB6mIV;EmBpnIM;IAOI,mBAAA;EnBgnIV;EmBvnIM;IAOI,mBAAA;EnBmnIV;EmB1nIM;IAOI,mBAAA;EnBsnIV;EmB7nIM;IAOI,mBAAA;EnBynIV;EmBhoIM;IAOI,mBAAA;EnB4nIV;EmBnoIM;IAOI,oBAAA;EnB+nIV;EmBtoIM;IAOI,0BAAA;EnBkoIV;EmBzoIM;IAOI,yBAAA;EnBqoIV;EmB5oIM;IAOI,uBAAA;EnBwoIV;EmB/oIM;IAOI,yBAAA;EnB2oIV;EmBlpIM;IAOI,uBAAA;EnB8oIV;EmBrpIM;IAOI,uBAAA;EnBipIV;EmBxpIM;IAOI,0BAAA;IAAA,yBAAA;EnBqpIV;EmB5pIM;IAOI,gCAAA;IAAA,+BAAA;EnBypIV;EmBhqIM;IAOI,+BAAA;IAAA,8BAAA;EnB6pIV;EmBpqIM;IAOI,6BAAA;IAAA,4BAAA;EnBiqIV;EmBxqIM;IAOI,+BAAA;IAAA,8BAAA;EnBqqIV;EmB5qIM;IAOI,6BAAA;IAAA,4BAAA;EnByqIV;EmBhrIM;IAOI,6BAAA;IAAA,4BAAA;EnB6qIV;EmBprIM;IAOI,wBAAA;IAAA,2BAAA;EnBirIV;EmBxrIM;IAOI,8BAAA;IAAA,iCAAA;EnBqrIV;EmB5rIM;IAOI,6BAAA;IAAA,gCAAA;EnByrIV;EmBhsIM;IAOI,2BAAA;IAAA,8BAAA;EnB6rIV;EmBpsIM;IAOI,6BAAA;IAAA,gCAAA;EnBisIV;EmBxsIM;IAOI,2BAAA;IAAA,8BAAA;EnBqsIV;EmB5sIM;IAOI,2BAAA;IAAA,8BAAA;EnBysIV;EmBhtIM;IAOI,wBAAA;EnB4sIV;EmBntIM;IAOI,8BAAA;EnB+sIV;EmBttIM;IAOI,6BAAA;EnBktIV;EmBztIM;IAOI,2BAAA;EnBqtIV;EmB5tIM;IAOI,6BAAA;EnBwtIV;EmB/tIM;IAOI,2BAAA;EnB2tIV;EmBluIM;IAOI,2BAAA;EnB8tIV;EmBruIM;IAOI,0BAAA;EnBiuIV;EmBxuIM;IAOI,gCAAA;EnBouIV;EmB3uIM;IAOI,+BAAA;EnBuuIV;EmB9uIM;IAOI,6BAAA;EnB0uIV;EmBjvIM;IAOI,+BAAA;EnB6uIV;EmBpvIM;IAOI,6BAAA;EnBgvIV;EmBvvIM;IAOI,6BAAA;EnBmvIV;EmB1vIM;IAOI,2BAAA;EnBsvIV;EmB7vIM;IAOI,iCAAA;EnByvIV;EmBhwIM;IAOI,gCAAA;EnB4vIV;EmBnwIM;IAOI,8BAAA;EnB+vIV;EmBtwIM;IAOI,gCAAA;EnBkwIV;EmBzwIM;IAOI,8BAAA;EnBqwIV;EmB5wIM;IAOI,8BAAA;EnBwwIV;EmB/wIM;IAOI,yBAAA;EnB2wIV;EmBlxIM;IAOI,+BAAA;EnB8wIV;EmBrxIM;IAOI,8BAAA;EnBixIV;EmBxxIM;IAOI,4BAAA;EnBoxIV;EmB3xIM;IAOI,8BAAA;EnBuxIV;EmB9xIM;IAOI,4BAAA;EnB0xIV;EmBjyIM;IAOI,4BAAA;EnB6xIV;EmBpyIM;IAOI,qBAAA;EnBgyIV;EmBvyIM;IAOI,2BAAA;EnBmyIV;EmB1yIM;IAOI,0BAAA;EnBsyIV;EmB7yIM;IAOI,wBAAA;EnByyIV;EmBhzIM;IAOI,0BAAA;EnB4yIV;EmBnzIM;IAOI,wBAAA;EnB+yIV;EmBtzIM;IAOI,2BAAA;IAAA,0BAAA;EnBmzIV;EmB1zIM;IAOI,iCAAA;IAAA,gCAAA;EnBuzIV;EmB9zIM;IAOI,gCAAA;IAAA,+BAAA;EnB2zIV;EmBl0IM;IAOI,8BAAA;IAAA,6BAAA;EnB+zIV;EmBt0IM;IAOI,gCAAA;IAAA,+BAAA;EnBm0IV;EmB10IM;IAOI,8BAAA;IAAA,6BAAA;EnBu0IV;EmB90IM;IAOI,yBAAA;IAAA,4BAAA;EnB20IV;EmBl1IM;IAOI,+BAAA;IAAA,kCAAA;EnB+0IV;EmBt1IM;IAOI,8BAAA;IAAA,iCAAA;EnBm1IV;EmB11IM;IAOI,4BAAA;IAAA,+BAAA;EnBu1IV;EmB91IM;IAOI,8BAAA;IAAA,iCAAA;EnB21IV;EmBl2IM;IAOI,4BAAA;IAAA,+BAAA;EnB+1IV;EmBt2IM;IAOI,yBAAA;EnBk2IV;EmBz2IM;IAOI,+BAAA;EnBq2IV;EmB52IM;IAOI,8BAAA;EnBw2IV;EmB/2IM;IAOI,4BAAA;EnB22IV;EmBl3IM;IAOI,8BAAA;EnB82IV;EmBr3IM;IAOI,4BAAA;EnBi3IV;EmBx3IM;IAOI,2BAAA;EnBo3IV;EmB33IM;IAOI,iCAAA;EnBu3IV;EmB93IM;IAOI,gCAAA;EnB03IV;EmBj4IM;IAOI,8BAAA;EnB63IV;EmBp4IM;IAOI,gCAAA;EnBg4IV;EmBv4IM;IAOI,8BAAA;EnBm4IV;EmB14IM;IAOI,4BAAA;EnBs4IV;EmB74IM;IAOI,kCAAA;EnBy4IV;EmBh5IM;IAOI,iCAAA;EnB44IV;EmBn5IM;IAOI,+BAAA;EnB+4IV;EmBt5IM;IAOI,iCAAA;EnBk5IV;EmBz5IM;IAOI,+BAAA;EnBq5IV;EmB55IM;IAOI,0BAAA;EnBw5IV;EmB/5IM;IAOI,gCAAA;EnB25IV;EmBl6IM;IAOI,+BAAA;EnB85IV;EmBr6IM;IAOI,6BAAA;EnBi6IV;EmBx6IM;IAOI,+BAAA;EnBo6IV;EmB36IM;IAOI,6BAAA;EnBu6IV;EmB96IM;IAOI,iBAAA;EnB06IV;EmBj7IM;IAOI,uBAAA;EnB66IV;EmBp7IM;IAOI,sBAAA;EnBg7IV;EmBv7IM;IAOI,oBAAA;EnBm7IV;EmB17IM;IAOI,sBAAA;EnBs7IV;EmB77IM;IAOI,oBAAA;EnBy7IV;EmBh8IM;IAOI,qBAAA;EnB47IV;EmBn8IM;IAOI,2BAAA;EnB+7IV;EmBt8IM;IAOI,0BAAA;EnBk8IV;EmBz8IM;IAOI,wBAAA;EnBq8IV;EmB58IM;IAOI,0BAAA;EnBw8IV;EmB/8IM;IAOI,wBAAA;EnB28IV;EmBl9IM;IAOI,6BAAA;IAAA,wBAAA;EnB88IV;EmBr9IM;IAOI,mCAAA;IAAA,8BAAA;EnBi9IV;EmBx9IM;IAOI,kCAAA;IAAA,6BAAA;EnBo9IV;EmB39IM;IAOI,gCAAA;IAAA,2BAAA;EnBu9IV;EmB99IM;IAOI,kCAAA;IAAA,6BAAA;EnB09IV;EmBj+IM;IAOI,gCAAA;IAAA,2BAAA;EnB69IV;EmBp+IM;IAOI,2BAAA;EnBg+IV;EmBv+IM;IAOI,4BAAA;EnBm+IV;EmB1+IM;IAOI,6BAAA;EnBs+IV;AACF;AWj/II;EQGI;IAOI,sBAAA;EnB2+IV;EmBl/IM;IAOI,uBAAA;EnB8+IV;EmBr/IM;IAOI,sBAAA;EnBi/IV;EmBx/IM;IAOI,iCAAA;IAAA,8BAAA;EnBo/IV;EmB3/IM;IAOI,+BAAA;IAAA,4BAAA;EnBu/IV;EmB9/IM;IAOI,8BAAA;IAAA,2BAAA;EnB0/IV;EmBjgJM;IAOI,oCAAA;IAAA,iCAAA;EnB6/IV;EmBpgJM;IAOI,8BAAA;IAAA,2BAAA;EnBggJV;EmBvgJM;IAOI,0BAAA;EnBmgJV;EmB1gJM;IAOI,gCAAA;EnBsgJV;EmB7gJM;IAOI,yBAAA;EnBygJV;EmBhhJM;IAOI,wBAAA;EnB4gJV;EmBnhJM;IAOI,yBAAA;EnB+gJV;EmBthJM;IAOI,6BAAA;EnBkhJV;EmBzhJM;IAOI,8BAAA;EnBqhJV;EmB5hJM;IAOI,wBAAA;EnBwhJV;EmB/hJM;IAOI,+BAAA;EnB2hJV;EmBliJM;IAOI,wBAAA;EnB8hJV;EmBriJM;IAOI,yBAAA;EnBiiJV;EmBxiJM;IAOI,8BAAA;EnBoiJV;EmB3iJM;IAOI,iCAAA;EnBuiJV;EmB9iJM;IAOI,sCAAA;EnB0iJV;EmBjjJM;IAOI,yCAAA;EnB6iJV;EmBpjJM;IAOI,uBAAA;EnBgjJV;EmBvjJM;IAOI,uBAAA;EnBmjJV;EmB1jJM;IAOI,yBAAA;EnBsjJV;EmB7jJM;IAOI,yBAAA;EnByjJV;EmBhkJM;IAOI,0BAAA;EnB4jJV;EmBnkJM;IAOI,4BAAA;EnB+jJV;EmBtkJM;IAOI,kCAAA;EnBkkJV;EmBzkJM;IAOI,sCAAA;EnBqkJV;EmB5kJM;IAOI,oCAAA;EnBwkJV;EmB/kJM;IAOI,kCAAA;EnB2kJV;EmBllJM;IAOI,yCAAA;EnB8kJV;EmBrlJM;IAOI,wCAAA;EnBilJV;EmBxlJM;IAOI,wCAAA;EnBolJV;EmB3lJM;IAOI,kCAAA;EnBulJV;EmB9lJM;IAOI,gCAAA;EnB0lJV;EmBjmJM;IAOI,8BAAA;EnB6lJV;EmBpmJM;IAOI,gCAAA;EnBgmJV;EmBvmJM;IAOI,+BAAA;EnBmmJV;EmB1mJM;IAOI,oCAAA;EnBsmJV;EmB7mJM;IAOI,kCAAA;EnBymJV;EmBhnJM;IAOI,gCAAA;EnB4mJV;EmBnnJM;IAOI,uCAAA;EnB+mJV;EmBtnJM;IAOI,sCAAA;EnBknJV;EmBznJM;IAOI,iCAAA;EnBqnJV;EmB5nJM;IAOI,2BAAA;EnBwnJV;EmB/nJM;IAOI,iCAAA;EnB2nJV;EmBloJM;IAOI,+BAAA;EnB8nJV;EmBroJM;IAOI,6BAAA;EnBioJV;EmBxoJM;IAOI,+BAAA;EnBooJV;EmB3oJM;IAOI,8BAAA;EnBuoJV;EmB9oJM;IAOI,oBAAA;EnB0oJV;EmBjpJM;IAOI,mBAAA;EnB6oJV;EmBppJM;IAOI,mBAAA;EnBgpJV;EmBvpJM;IAOI,mBAAA;EnBmpJV;EmB1pJM;IAOI,mBAAA;EnBspJV;EmB7pJM;IAOI,mBAAA;EnBypJV;EmBhqJM;IAOI,mBAAA;EnB4pJV;EmBnqJM;IAOI,mBAAA;EnB+pJV;EmBtqJM;IAOI,oBAAA;EnBkqJV;EmBzqJM;IAOI,0BAAA;EnBqqJV;EmB5qJM;IAOI,yBAAA;EnBwqJV;EmB/qJM;IAOI,uBAAA;EnB2qJV;EmBlrJM;IAOI,yBAAA;EnB8qJV;EmBrrJM;IAOI,uBAAA;EnBirJV;EmBxrJM;IAOI,uBAAA;EnBorJV;EmB3rJM;IAOI,0BAAA;IAAA,yBAAA;EnBwrJV;EmB/rJM;IAOI,gCAAA;IAAA,+BAAA;EnB4rJV;EmBnsJM;IAOI,+BAAA;IAAA,8BAAA;EnBgsJV;EmBvsJM;IAOI,6BAAA;IAAA,4BAAA;EnBosJV;EmB3sJM;IAOI,+BAAA;IAAA,8BAAA;EnBwsJV;EmB/sJM;IAOI,6BAAA;IAAA,4BAAA;EnB4sJV;EmBntJM;IAOI,6BAAA;IAAA,4BAAA;EnBgtJV;EmBvtJM;IAOI,wBAAA;IAAA,2BAAA;EnBotJV;EmB3tJM;IAOI,8BAAA;IAAA,iCAAA;EnBwtJV;EmB/tJM;IAOI,6BAAA;IAAA,gCAAA;EnB4tJV;EmBnuJM;IAOI,2BAAA;IAAA,8BAAA;EnBguJV;EmBvuJM;IAOI,6BAAA;IAAA,gCAAA;EnBouJV;EmB3uJM;IAOI,2BAAA;IAAA,8BAAA;EnBwuJV;EmB/uJM;IAOI,2BAAA;IAAA,8BAAA;EnB4uJV;EmBnvJM;IAOI,wBAAA;EnB+uJV;EmBtvJM;IAOI,8BAAA;EnBkvJV;EmBzvJM;IAOI,6BAAA;EnBqvJV;EmB5vJM;IAOI,2BAAA;EnBwvJV;EmB/vJM;IAOI,6BAAA;EnB2vJV;EmBlwJM;IAOI,2BAAA;EnB8vJV;EmBrwJM;IAOI,2BAAA;EnBiwJV;EmBxwJM;IAOI,0BAAA;EnBowJV;EmB3wJM;IAOI,gCAAA;EnBuwJV;EmB9wJM;IAOI,+BAAA;EnB0wJV;EmBjxJM;IAOI,6BAAA;EnB6wJV;EmBpxJM;IAOI,+BAAA;EnBgxJV;EmBvxJM;IAOI,6BAAA;EnBmxJV;EmB1xJM;IAOI,6BAAA;EnBsxJV;EmB7xJM;IAOI,2BAAA;EnByxJV;EmBhyJM;IAOI,iCAAA;EnB4xJV;EmBnyJM;IAOI,gCAAA;EnB+xJV;EmBtyJM;IAOI,8BAAA;EnBkyJV;EmBzyJM;IAOI,gCAAA;EnBqyJV;EmB5yJM;IAOI,8BAAA;EnBwyJV;EmB/yJM;IAOI,8BAAA;EnB2yJV;EmBlzJM;IAOI,yBAAA;EnB8yJV;EmBrzJM;IAOI,+BAAA;EnBizJV;EmBxzJM;IAOI,8BAAA;EnBozJV;EmB3zJM;IAOI,4BAAA;EnBuzJV;EmB9zJM;IAOI,8BAAA;EnB0zJV;EmBj0JM;IAOI,4BAAA;EnB6zJV;EmBp0JM;IAOI,4BAAA;EnBg0JV;EmBv0JM;IAOI,qBAAA;EnBm0JV;EmB10JM;IAOI,2BAAA;EnBs0JV;EmB70JM;IAOI,0BAAA;EnBy0JV;EmBh1JM;IAOI,wBAAA;EnB40JV;EmBn1JM;IAOI,0BAAA;EnB+0JV;EmBt1JM;IAOI,wBAAA;EnBk1JV;EmBz1JM;IAOI,2BAAA;IAAA,0BAAA;EnBs1JV;EmB71JM;IAOI,iCAAA;IAAA,gCAAA;EnB01JV;EmBj2JM;IAOI,gCAAA;IAAA,+BAAA;EnB81JV;EmBr2JM;IAOI,8BAAA;IAAA,6BAAA;EnBk2JV;EmBz2JM;IAOI,gCAAA;IAAA,+BAAA;EnBs2JV;EmB72JM;IAOI,8BAAA;IAAA,6BAAA;EnB02JV;EmBj3JM;IAOI,yBAAA;IAAA,4BAAA;EnB82JV;EmBr3JM;IAOI,+BAAA;IAAA,kCAAA;EnBk3JV;EmBz3JM;IAOI,8BAAA;IAAA,iCAAA;EnBs3JV;EmB73JM;IAOI,4BAAA;IAAA,+BAAA;EnB03JV;EmBj4JM;IAOI,8BAAA;IAAA,iCAAA;EnB83JV;EmBr4JM;IAOI,4BAAA;IAAA,+BAAA;EnBk4JV;EmBz4JM;IAOI,yBAAA;EnBq4JV;EmB54JM;IAOI,+BAAA;EnBw4JV;EmB/4JM;IAOI,8BAAA;EnB24JV;EmBl5JM;IAOI,4BAAA;EnB84JV;EmBr5JM;IAOI,8BAAA;EnBi5JV;EmBx5JM;IAOI,4BAAA;EnBo5JV;EmB35JM;IAOI,2BAAA;EnBu5JV;EmB95JM;IAOI,iCAAA;EnB05JV;EmBj6JM;IAOI,gCAAA;EnB65JV;EmBp6JM;IAOI,8BAAA;EnBg6JV;EmBv6JM;IAOI,gCAAA;EnBm6JV;EmB16JM;IAOI,8BAAA;EnBs6JV;EmB76JM;IAOI,4BAAA;EnBy6JV;EmBh7JM;IAOI,kCAAA;EnB46JV;EmBn7JM;IAOI,iCAAA;EnB+6JV;EmBt7JM;IAOI,+BAAA;EnBk7JV;EmBz7JM;IAOI,iCAAA;EnBq7JV;EmB57JM;IAOI,+BAAA;EnBw7JV;EmB/7JM;IAOI,0BAAA;EnB27JV;EmBl8JM;IAOI,gCAAA;EnB87JV;EmBr8JM;IAOI,+BAAA;EnBi8JV;EmBx8JM;IAOI,6BAAA;EnBo8JV;EmB38JM;IAOI,+BAAA;EnBu8JV;EmB98JM;IAOI,6BAAA;EnB08JV;EmBj9JM;IAOI,iBAAA;EnB68JV;EmBp9JM;IAOI,uBAAA;EnBg9JV;EmBv9JM;IAOI,sBAAA;EnBm9JV;EmB19JM;IAOI,oBAAA;EnBs9JV;EmB79JM;IAOI,sBAAA;EnBy9JV;EmBh+JM;IAOI,oBAAA;EnB49JV;EmBn+JM;IAOI,qBAAA;EnB+9JV;EmBt+JM;IAOI,2BAAA;EnBk+JV;EmBz+JM;IAOI,0BAAA;EnBq+JV;EmB5+JM;IAOI,wBAAA;EnBw+JV;EmB/+JM;IAOI,0BAAA;EnB2+JV;EmBl/JM;IAOI,wBAAA;EnB8+JV;EmBr/JM;IAOI,6BAAA;IAAA,wBAAA;EnBi/JV;EmBx/JM;IAOI,mCAAA;IAAA,8BAAA;EnBo/JV;EmB3/JM;IAOI,kCAAA;IAAA,6BAAA;EnBu/JV;EmB9/JM;IAOI,gCAAA;IAAA,2BAAA;EnB0/JV;EmBjgKM;IAOI,kCAAA;IAAA,6BAAA;EnB6/JV;EmBpgKM;IAOI,gCAAA;IAAA,2BAAA;EnBggKV;EmBvgKM;IAOI,2BAAA;EnBmgKV;EmB1gKM;IAOI,4BAAA;EnBsgKV;EmB7gKM;IAOI,6BAAA;EnBygKV;AACF;AoBhkKA;ED+CQ;IAOI,4BAAA;EnB8gKV;EmBrhKM;IAOI,0BAAA;EnBihKV;EmBxhKM;IAOI,6BAAA;EnBohKV;EmB3hKM;IAOI,4BAAA;EnBuhKV;AACF;AoB3jKA;ED4BQ;IAOI,0BAAA;EnB4hKV;EmBniKM;IAOI,gCAAA;EnB+hKV;EmBtiKM;IAOI,yBAAA;EnBkiKV;EmBziKM;IAOI,wBAAA;EnBqiKV;EmB5iKM;IAOI,yBAAA;EnBwiKV;EmB/iKM;IAOI,6BAAA;EnB2iKV;EmBljKM;IAOI,8BAAA;EnB8iKV;EmBrjKM;IAOI,wBAAA;EnBijKV;EmBxjKM;IAOI,+BAAA;EnBojKV;EmB3jKM;IAOI,wBAAA;EnBujKV;AACF", "file": "bootstrap-utilities.css", "sourcesContent": ["@mixin bsBanner($file) {\r\n  /*!\r\n   * Bootstrap #{$file} v5.3.0-alpha1 (https://getbootstrap.com/)\r\n   * Copyright 2011-2023 The Bootstrap Authors\r\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\r\n   */\r\n}\r\n", ":root,\r\n[data-bs-theme=\"light\"] {\r\n  // Note: Custom variable values only support SassScript inside `#{}`.\r\n\r\n  // Colors\r\n  //\r\n  // Generate palettes for full colors, grays, and theme colors.\r\n\r\n  @each $color, $value in $colors {\r\n    --#{$prefix}#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $grays {\r\n    --#{$prefix}gray-#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors {\r\n    --#{$prefix}#{$color}: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-rgb {\r\n    --#{$prefix}#{$color}-rgb: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-text {\r\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-bg-subtle {\r\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\r\n  }\r\n\r\n  @each $color, $value in $theme-colors-border-subtle {\r\n    --#{$prefix}#{$color}-border-subtle: #{$value};\r\n  }\r\n\r\n  --#{$prefix}white-rgb: #{to-rgb($white)};\r\n  --#{$prefix}black-rgb: #{to-rgb($black)};\r\n\r\n  // Fonts\r\n\r\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\r\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\r\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\r\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\r\n  --#{$prefix}gradient: #{$gradient};\r\n\r\n  // Root and body\r\n  // scss-docs-start root-body-variables\r\n  @if $font-size-root != null {\r\n    --#{$prefix}root-font-size: #{$font-size-root};\r\n  }\r\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\r\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\r\n  --#{$prefix}body-font-weight: #{$font-weight-base};\r\n  --#{$prefix}body-line-height: #{$line-height-base};\r\n  @if $body-text-align != null {\r\n    --#{$prefix}body-text-align: #{$body-text-align};\r\n  }\r\n\r\n  --#{$prefix}body-color: #{$body-color};\r\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\r\n  --#{$prefix}body-bg: #{$body-bg};\r\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\r\n\r\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\r\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\r\n\r\n  --#{$prefix}secondary-color: #{$body-secondary-color};\r\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\r\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\r\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\r\n\r\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\r\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\r\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\r\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\r\n  // scss-docs-end root-body-variables\r\n\r\n  @if $headings-color != null {\r\n    --#{$prefix}heading-color: #{$headings-color};\r\n  }\r\n\r\n  --#{$prefix}link-color: #{$link-color};\r\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\r\n  --#{$prefix}link-decoration: #{$link-decoration};\r\n\r\n  --#{$prefix}link-hover-color: #{$link-hover-color};\r\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\r\n\r\n  @if $link-hover-decoration != null {\r\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\r\n  }\r\n\r\n  --#{$prefix}code-color: #{$code-color};\r\n  --#{$prefix}highlight-bg: #{$mark-bg};\r\n\r\n  // scss-docs-start root-border-var\r\n  --#{$prefix}border-width: #{$border-width};\r\n  --#{$prefix}border-style: #{$border-style};\r\n  --#{$prefix}border-color: #{$border-color};\r\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\r\n\r\n  --#{$prefix}border-radius: #{$border-radius};\r\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\r\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\r\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\r\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\r\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\r\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\r\n  // scss-docs-end root-border-var\r\n\r\n  --#{$prefix}box-shadow: #{$box-shadow};\r\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\r\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\r\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\r\n\r\n  // Focus styles\r\n  // scss-docs-start root-focus-variables\r\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\r\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\r\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\r\n  // scss-docs-end root-focus-variables\r\n\r\n  // scss-docs-start root-form-validation-variables\r\n  --#{$prefix}form-valid-color: #{$form-valid-color};\r\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\r\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\r\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\r\n  // scss-docs-end root-form-validation-variables\r\n}\r\n\r\n@if $enable-dark-mode {\r\n  @include color-mode(dark, true) {\r\n    color-scheme: dark;\r\n\r\n    // scss-docs-start root-dark-mode-vars\r\n    --#{$prefix}body-color: #{$body-color-dark};\r\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\r\n    --#{$prefix}body-bg: #{$body-bg-dark};\r\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\r\n\r\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\r\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\r\n\r\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\r\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\r\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\r\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\r\n\r\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\r\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\r\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\r\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\r\n\r\n    @each $color, $value in $theme-colors-text-dark {\r\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\r\n    }\r\n\r\n    @each $color, $value in $theme-colors-bg-subtle-dark {\r\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\r\n    }\r\n\r\n    @each $color, $value in $theme-colors-border-subtle-dark {\r\n      --#{$prefix}#{$color}-border-subtle: #{$value};\r\n    }\r\n\r\n    @if $headings-color-dark != null {\r\n      --#{$prefix}heading-color: #{$headings-color-dark};\r\n    }\r\n\r\n    --#{$prefix}link-color: #{$link-color-dark};\r\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\r\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\r\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\r\n\r\n    --#{$prefix}code-color: #{$code-color-dark};\r\n\r\n    --#{$prefix}border-color: #{$border-color-dark};\r\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\r\n\r\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\r\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\r\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\r\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\r\n    // scss-docs-end root-dark-mode-vars\r\n  }\r\n}\r\n", "// stylelint-disable property-disallowed-list, scss/dollar-variable-default\r\n\r\n// SCSS RFS mixin\r\n//\r\n// Automated responsive values for font sizes, paddings, margins and much more\r\n//\r\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\r\n\r\n// Configuration\r\n\r\n// Base value\r\n$rfs-base-value: 1.25rem !default;\r\n$rfs-unit: rem !default;\r\n\r\n@if $rfs-unit != rem and $rfs-unit != px {\r\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\r\n}\r\n\r\n// Breakpoint at where values start decreasing if screen width is smaller\r\n$rfs-breakpoint: 1200px !default;\r\n$rfs-breakpoint-unit: px !default;\r\n\r\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\r\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\r\n}\r\n\r\n// Resize values based on screen height and width\r\n$rfs-two-dimensional: false !default;\r\n\r\n// Factor of decrease\r\n$rfs-factor: 10 !default;\r\n\r\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\r\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\r\n}\r\n\r\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\r\n$rfs-mode: min-media-query !default;\r\n\r\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\r\n$rfs-class: false !default;\r\n\r\n// 1 rem = $rfs-rem-value px\r\n$rfs-rem-value: 16 !default;\r\n\r\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\r\n$rfs-safari-iframe-resize-bug-fix: false !default;\r\n\r\n// Disable RFS by setting $enable-rfs to false\r\n$enable-rfs: true !default;\r\n\r\n// Cache $rfs-base-value unit\r\n$rfs-base-value-unit: unit($rfs-base-value);\r\n\r\n@function divide($dividend, $divisor, $precision: 10) {\r\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\r\n  $dividend: abs($dividend);\r\n  $divisor: abs($divisor);\r\n  @if $dividend == 0 {\r\n    @return 0;\r\n  }\r\n  @if $divisor == 0 {\r\n    @error \"Cannot divide by 0\";\r\n  }\r\n  $remainder: $dividend;\r\n  $result: 0;\r\n  $factor: 10;\r\n  @while ($remainder > 0 and $precision >= 0) {\r\n    $quotient: 0;\r\n    @while ($remainder >= $divisor) {\r\n      $remainder: $remainder - $divisor;\r\n      $quotient: $quotient + 1;\r\n    }\r\n    $result: $result * 10 + $quotient;\r\n    $factor: $factor * .1;\r\n    $remainder: $remainder * 10;\r\n    $precision: $precision - 1;\r\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\r\n      $result: $result + 1;\r\n    }\r\n  }\r\n  $result: $result * $factor * $sign;\r\n  $dividend-unit: unit($dividend);\r\n  $divisor-unit: unit($divisor);\r\n  $unit-map: (\r\n    \"px\": 1px,\r\n    \"rem\": 1rem,\r\n    \"em\": 1em,\r\n    \"%\": 1%\r\n  );\r\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\r\n    $result: $result * map-get($unit-map, $dividend-unit);\r\n  }\r\n  @return $result;\r\n}\r\n\r\n// Remove px-unit from $rfs-base-value for calculations\r\n@if $rfs-base-value-unit == px {\r\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\r\n}\r\n@else if $rfs-base-value-unit == rem {\r\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\r\n}\r\n\r\n// Cache $rfs-breakpoint unit to prevent multiple calls\r\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\r\n\r\n// Remove unit from $rfs-breakpoint for calculations\r\n@if $rfs-breakpoint-unit-cache == px {\r\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\r\n}\r\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\r\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\r\n}\r\n\r\n// Calculate the media query value\r\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\r\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\r\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\r\n\r\n// Internal mixin used to determine which media query needs to be used\r\n@mixin _rfs-media-query {\r\n  @if $rfs-two-dimensional {\r\n    @if $rfs-mode == max-media-query {\r\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\r\n        @content;\r\n      }\r\n    }\r\n    @else {\r\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\r\n        @content;\r\n      }\r\n    }\r\n  }\r\n  @else {\r\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Internal mixin that adds disable classes to the selector if needed.\r\n@mixin _rfs-rule {\r\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\r\n    // Adding an extra class increases specificity, which prevents the media query to override the property\r\n    &,\r\n    .disable-rfs &,\r\n    &.disable-rfs {\r\n      @content;\r\n    }\r\n  }\r\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\r\n    .enable-rfs &,\r\n    &.enable-rfs {\r\n      @content;\r\n    }\r\n  }\r\n  @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Internal mixin that adds enable classes to the selector if needed.\r\n@mixin _rfs-media-query-rule {\r\n\r\n  @if $rfs-class == enable {\r\n    @if $rfs-mode == min-media-query {\r\n      @content;\r\n    }\r\n\r\n    @include _rfs-media-query {\r\n      .enable-rfs &,\r\n      &.enable-rfs {\r\n        @content;\r\n      }\r\n    }\r\n  }\r\n  @else {\r\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\r\n      .disable-rfs &,\r\n      &.disable-rfs {\r\n        @content;\r\n      }\r\n    }\r\n    @include _rfs-media-query {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Helper function to get the formatted non-responsive value\r\n@function rfs-value($values) {\r\n  // Convert to list\r\n  $values: if(type-of($values) != list, ($values,), $values);\r\n\r\n  $val: '';\r\n\r\n  // Loop over each value and calculate value\r\n  @each $value in $values {\r\n    @if $value == 0 {\r\n      $val: $val + ' 0';\r\n    }\r\n    @else {\r\n      // Cache $value unit\r\n      $unit: if(type-of($value) == \"number\", unit($value), false);\r\n\r\n      @if $unit == px {\r\n        // Convert to rem if needed\r\n        $val: $val + ' ' + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\r\n      }\r\n      @else if $unit == rem {\r\n        // Convert to px if needed\r\n        $val: $val + ' ' + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\r\n      }\r\n      @else {\r\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\r\n        $val: $val + ' ' + $value;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove first space\r\n  @return unquote(str-slice($val, 2));\r\n}\r\n\r\n// Helper function to get the responsive value calculated by RFS\r\n@function rfs-fluid-value($values) {\r\n  // Convert to list\r\n  $values: if(type-of($values) != list, ($values,), $values);\r\n\r\n  $val: '';\r\n\r\n  // Loop over each value and calculate value\r\n  @each $value in $values {\r\n    @if $value == 0 {\r\n      $val: $val + ' 0';\r\n    }\r\n\r\n    @else {\r\n      // Cache $value unit\r\n      $unit: if(type-of($value) == \"number\", unit($value), false);\r\n\r\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\r\n      @if not $unit or $unit != px and $unit != rem {\r\n        $val: $val + ' ' + $value;\r\n      }\r\n\r\n      @else {\r\n        // Remove unit from $value for calculations\r\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\r\n\r\n        // Only add the media query if the value is greater than the minimum value\r\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\r\n          $val: $val + ' ' +  if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\r\n        }\r\n        @else {\r\n          // Calculate the minimum value\r\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\r\n\r\n          // Calculate difference between $value and the minimum value\r\n          $value-diff: abs($value) - $value-min;\r\n\r\n          // Base value formatting\r\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\r\n\r\n          // Use negative value if needed\r\n          $min-width: if($value < 0, -$min-width, $min-width);\r\n\r\n          // Use `vmin` if two-dimensional is enabled\r\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\r\n\r\n          // Calculate the variable width between 0 and $rfs-breakpoint\r\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\r\n\r\n          // Return the calculated value\r\n          $val: $val + ' calc(' + $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove first space\r\n  @return unquote(str-slice($val, 2));\r\n}\r\n\r\n// RFS mixin\r\n@mixin rfs($values, $property: font-size) {\r\n  @if $values != null {\r\n    $val: rfs-value($values);\r\n    $fluidVal: rfs-fluid-value($values);\r\n\r\n    // Do not print the media query if responsive & non-responsive values are the same\r\n    @if $val == $fluidVal {\r\n      #{$property}: $val;\r\n    }\r\n    @else {\r\n      @include _rfs-rule {\r\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluidVal);\r\n\r\n        // Include safari iframe resize fix if needed\r\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\r\n      }\r\n\r\n      @include _rfs-media-query-rule {\r\n        #{$property}: if($rfs-mode == max-media-query, $fluidVal, $val);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Shorthand helper mixins\r\n@mixin font-size($value) {\r\n  @include rfs($value);\r\n}\r\n\r\n@mixin padding($value) {\r\n  @include rfs($value, padding);\r\n}\r\n\r\n@mixin padding-top($value) {\r\n  @include rfs($value, padding-top);\r\n}\r\n\r\n@mixin padding-right($value) {\r\n  @include rfs($value, padding-right);\r\n}\r\n\r\n@mixin padding-bottom($value) {\r\n  @include rfs($value, padding-bottom);\r\n}\r\n\r\n@mixin padding-left($value) {\r\n  @include rfs($value, padding-left);\r\n}\r\n\r\n@mixin margin($value) {\r\n  @include rfs($value, margin);\r\n}\r\n\r\n@mixin margin-top($value) {\r\n  @include rfs($value, margin-top);\r\n}\r\n\r\n@mixin margin-right($value) {\r\n  @include rfs($value, margin-right);\r\n}\r\n\r\n@mixin margin-bottom($value) {\r\n  @include rfs($value, margin-bottom);\r\n}\r\n\r\n@mixin margin-left($value) {\r\n  @include rfs($value, margin-left);\r\n}\r\n", "/*!\n * Bootstrap Utilities v5.3.0-alpha1 (https://getbootstrap.com/)\n * Copyright 2011-2023 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n:root,\n[data-bs-theme=light] {\n  --bs-blue: #1a73b8;\n  --bs-indigo: #6610f2;\n  --bs-purple: #6f42c1;\n  --bs-pink: #d63384;\n  --bs-red: #ea0b16;\n  --bs-orange: #fd7e14;\n  --bs-yellow: #f0a205;\n  --bs-green: #005c29;\n  --bs-teal: #20c997;\n  --bs-cyan: #175cd3;\n  --bs-black: #000;\n  --bs-white: #fff;\n  --bs-gray: #6c757d;\n  --bs-gray-dark: #343a40;\n  --bs-gray-100: #ececec;\n  --bs-gray-200: #e9ecef;\n  --bs-gray-300: #dee2e6;\n  --bs-gray-400: #ced4da;\n  --bs-gray-500: #adb5bd;\n  --bs-gray-600: #6c757d;\n  --bs-gray-700: #495057;\n  --bs-gray-800: #343a40;\n  --bs-gray-900: #393939;\n  --bs-primary: #005c29;\n  --bs-secondary: #d1e7bf;\n  --bs-success: #005c29;\n  --bs-info: #175cd3;\n  --bs-warning: #f0a205;\n  --bs-danger: #ea0b16;\n  --bs-light: #ececec;\n  --bs-dark: #393939;\n  --bs-primary-rgb: 0, 92, 41;\n  --bs-secondary-rgb: 209, 231, 191;\n  --bs-success-rgb: 0, 92, 41;\n  --bs-info-rgb: 23, 92, 211;\n  --bs-warning-rgb: 240, 162, 5;\n  --bs-danger-rgb: 234, 11, 22;\n  --bs-light-rgb: 236, 236, 236;\n  --bs-dark-rgb: 57, 57, 57;\n  --bs-primary-text-emphasis: #002510;\n  --bs-secondary-text-emphasis: #545c4c;\n  --bs-success-text-emphasis: #002510;\n  --bs-info-text-emphasis: #092554;\n  --bs-warning-text-emphasis: #604102;\n  --bs-danger-text-emphasis: #5e0409;\n  --bs-light-text-emphasis: #495057;\n  --bs-dark-text-emphasis: #495057;\n  --bs-primary-bg-subtle: #ccded4;\n  --bs-secondary-bg-subtle: #f6faf2;\n  --bs-success-bg-subtle: #ccded4;\n  --bs-info-bg-subtle: #d1def6;\n  --bs-warning-bg-subtle: #fceccd;\n  --bs-danger-bg-subtle: #fbced0;\n  --bs-light-bg-subtle: #f6f6f6;\n  --bs-dark-bg-subtle: #ced4da;\n  --bs-primary-border-subtle: #99bea9;\n  --bs-secondary-border-subtle: #edf5e5;\n  --bs-success-border-subtle: #99bea9;\n  --bs-info-border-subtle: #a2beed;\n  --bs-warning-border-subtle: #f9da9b;\n  --bs-danger-border-subtle: #f79da2;\n  --bs-light-border-subtle: #e9ecef;\n  --bs-dark-border-subtle: #adb5bd;\n  --bs-white-rgb: 255, 255, 255;\n  --bs-black-rgb: 0, 0, 0;\n  --bs-font-sans-serif: system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));\n  --bs-body-font-family: var(--bs-font-sans-serif);\n  --bs-body-font-size: 1rem;\n  --bs-body-font-weight: 400;\n  --bs-body-line-height: 1.5;\n  --bs-body-color: #393939;\n  --bs-body-color-rgb: 57, 57, 57;\n  --bs-body-bg: #fff;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-emphasis-color: #000;\n  --bs-emphasis-color-rgb: 0, 0, 0;\n  --bs-secondary-color: rgba(57, 57, 57, 0.75);\n  --bs-secondary-color-rgb: 57, 57, 57;\n  --bs-secondary-bg: #e9ecef;\n  --bs-secondary-bg-rgb: 233, 236, 239;\n  --bs-tertiary-color: rgba(57, 57, 57, 0.5);\n  --bs-tertiary-color-rgb: 57, 57, 57;\n  --bs-tertiary-bg: #ececec;\n  --bs-tertiary-bg-rgb: 236, 236, 236;\n  --bs-link-color: #005c29;\n  --bs-link-color-rgb: 0, 92, 41;\n  --bs-link-decoration: underline;\n  --bs-link-hover-color: #0f280b;\n  --bs-link-hover-color-rgb: 15, 40, 11;\n  --bs-code-color: #d63384;\n  --bs-highlight-bg: #fceccd;\n  --bs-border-width: 1px;\n  --bs-border-style: solid;\n  --bs-border-color: #dee2e6;\n  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);\n  --bs-border-radius: 0.375rem;\n  --bs-border-radius-sm: 0.25rem;\n  --bs-border-radius-lg: 0.5rem;\n  --bs-border-radius-xl: 1rem;\n  --bs-border-radius-xxl: 2rem;\n  --bs-border-radius-2xl: var(--bs-border-radius-xxl);\n  --bs-border-radius-pill: 50rem;\n  --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n  --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n  --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);\n  --bs-focus-ring-width: 0.25rem;\n  --bs-focus-ring-opacity: 0.25;\n  --bs-focus-ring-color: rgba(0, 92, 41, 0.25);\n  --bs-form-valid-color: #005c29;\n  --bs-form-valid-border-color: #005c29;\n  --bs-form-invalid-color: #ea0b16;\n  --bs-form-invalid-border-color: #ea0b16;\n}\n\n[data-bs-theme=dark] {\n  color-scheme: dark;\n  --bs-body-color: #adb5bd;\n  --bs-body-color-rgb: 173, 181, 189;\n  --bs-body-bg: #393939;\n  --bs-body-bg-rgb: 57, 57, 57;\n  --bs-emphasis-color: #fff;\n  --bs-emphasis-color-rgb: 255, 255, 255;\n  --bs-secondary-color: rgba(173, 181, 189, 0.75);\n  --bs-secondary-color-rgb: 173, 181, 189;\n  --bs-secondary-bg: #343a40;\n  --bs-secondary-bg-rgb: 52, 58, 64;\n  --bs-tertiary-color: rgba(173, 181, 189, 0.5);\n  --bs-tertiary-color-rgb: 173, 181, 189;\n  --bs-tertiary-bg: #373a3d;\n  --bs-tertiary-bg-rgb: 55, 58, 61;\n  --bs-primary-text-emphasis: #669d7f;\n  --bs-secondary-text-emphasis: #e3f1d9;\n  --bs-success-text-emphasis: #669d7f;\n  --bs-info-text-emphasis: #749de5;\n  --bs-warning-text-emphasis: #f6c769;\n  --bs-danger-text-emphasis: #f26d73;\n  --bs-light-text-emphasis: #ececec;\n  --bs-dark-text-emphasis: #dee2e6;\n  --bs-primary-bg-subtle: #001208;\n  --bs-secondary-bg-subtle: #2a2e26;\n  --bs-success-bg-subtle: #001208;\n  --bs-info-bg-subtle: #05122a;\n  --bs-warning-bg-subtle: #302001;\n  --bs-danger-bg-subtle: #2f0204;\n  --bs-light-bg-subtle: #343a40;\n  --bs-dark-bg-subtle: #1a1d20;\n  --bs-primary-border-subtle: #003719;\n  --bs-secondary-border-subtle: #7d8b73;\n  --bs-success-border-subtle: #003719;\n  --bs-info-border-subtle: #0e377f;\n  --bs-warning-border-subtle: #906103;\n  --bs-danger-border-subtle: #8c070d;\n  --bs-light-border-subtle: #495057;\n  --bs-dark-border-subtle: #343a40;\n  --bs-link-color: #669d7f;\n  --bs-link-hover-color: #85b199;\n  --bs-link-color-rgb: 102, 157, 127;\n  --bs-link-hover-color-rgb: 133, 177, 153;\n  --bs-code-color: #e685b5;\n  --bs-border-color: #495057;\n  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);\n  --bs-form-valid-color: #669d7f;\n  --bs-form-valid-border-color: #669d7f;\n  --bs-form-invalid-color: #f26d73;\n  --bs-form-invalid-border-color: #f26d73;\n}\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.text-bg-primary {\n  color: #fff !important;\n  background-color: RGBA(0, 92, 41, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-secondary {\n  color: #000 !important;\n  background-color: RGBA(209, 231, 191, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-success {\n  color: #fff !important;\n  background-color: RGBA(0, 92, 41, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-info {\n  color: #fff !important;\n  background-color: RGBA(23, 92, 211, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-warning {\n  color: #000 !important;\n  background-color: RGBA(240, 162, 5, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-danger {\n  color: #fff !important;\n  background-color: RGBA(234, 11, 22, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-light {\n  color: #000 !important;\n  background-color: RGBA(236, 236, 236, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-dark {\n  color: #fff !important;\n  background-color: RGBA(57, 57, 57, var(--bs-bg-opacity, 1)) !important;\n}\n\n.link-primary {\n  color: RGBA(var(--bs-primary-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-primary-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-primary:hover, .link-primary:focus {\n  color: RGBA(0, 74, 33, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(0, 74, 33, var(--bs-link-underline-opacity, 1));\n}\n\n.link-secondary {\n  color: RGBA(var(--bs-secondary-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-secondary-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-secondary:hover, .link-secondary:focus {\n  color: RGBA(218, 236, 204, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(218, 236, 204, var(--bs-link-underline-opacity, 1));\n}\n\n.link-success {\n  color: RGBA(var(--bs-success-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-success-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-success:hover, .link-success:focus {\n  color: RGBA(0, 74, 33, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(0, 74, 33, var(--bs-link-underline-opacity, 1));\n}\n\n.link-info {\n  color: RGBA(var(--bs-info-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-info-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-info:hover, .link-info:focus {\n  color: RGBA(18, 74, 169, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(18, 74, 169, var(--bs-link-underline-opacity, 1));\n}\n\n.link-warning {\n  color: RGBA(var(--bs-warning-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-warning-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-warning:hover, .link-warning:focus {\n  color: RGBA(243, 181, 55, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(243, 181, 55, var(--bs-link-underline-opacity, 1));\n}\n\n.link-danger {\n  color: RGBA(var(--bs-danger-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-danger-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-danger:hover, .link-danger:focus {\n  color: RGBA(187, 9, 18, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(187, 9, 18, var(--bs-link-underline-opacity, 1));\n}\n\n.link-light {\n  color: RGBA(var(--bs-light-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-light-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-light:hover, .link-light:focus {\n  color: RGBA(240, 240, 240, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(240, 240, 240, var(--bs-link-underline-opacity, 1));\n}\n\n.link-dark {\n  color: RGBA(var(--bs-dark-rgb, var(--bs-link-opacity, 1)));\n  text-decoration-color: RGBA(var(--bs-dark-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-dark:hover, .link-dark:focus {\n  color: RGBA(46, 46, 46, var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(46, 46, 46, var(--bs-link-underline-opacity, 1));\n}\n\n.link-body-emphasis {\n  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 1));\n  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 1));\n}\n.link-body-emphasis:hover, .link-body-emphasis:focus {\n  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 0.75));\n  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 0.75));\n}\n\n.focus-ring:focus {\n  outline: 0;\n  box-shadow: var(--bs-focus-ring-x, 0) var(--bs-focus-ring-y, 0) var(--bs-focus-ring-blur, 0) var(--bs-focus-ring-width) var(--bs-focus-ring-color);\n}\n\n.icon-link {\n  display: inline-flex;\n  gap: 0.375rem;\n  align-items: center;\n  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));\n  text-underline-offset: 0.25em;\n  backface-visibility: hidden;\n}\n.icon-link > .bi {\n  flex-shrink: 0;\n  width: 1em;\n  height: 1em;\n  transition: 0.2s ease-in-out transform;\n}\n@media (prefers-reduced-motion: reduce) {\n  .icon-link > .bi {\n    transition: none;\n  }\n}\n\n.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi {\n  transform: var(--bs-icon-link-transform, translate3d(0.25em, 0, 0));\n}\n\n.ratio {\n  position: relative;\n  width: 100%;\n}\n.ratio::before {\n  display: block;\n  padding-top: var(--bs-aspect-ratio);\n  content: \"\";\n}\n.ratio > * {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.ratio-1x1 {\n  --bs-aspect-ratio: 100%;\n}\n\n.ratio-4x3 {\n  --bs-aspect-ratio: 75%;\n}\n\n.ratio-16x9 {\n  --bs-aspect-ratio: 56.25%;\n}\n\n.ratio-21x9 {\n  --bs-aspect-ratio: 42.8571428571%;\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.sticky-top {\n  position: sticky;\n  top: 0;\n  z-index: 1020;\n}\n\n.sticky-bottom {\n  position: sticky;\n  bottom: 0;\n  z-index: 1020;\n}\n\n@media (min-width: 576px) {\n  .sticky-sm-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-sm-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 768px) {\n  .sticky-md-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-md-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 992px) {\n  .sticky-lg-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-lg-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1200px) {\n  .sticky-xl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-xl-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1400px) {\n  .sticky-xxl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-xxl-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n.visually-hidden:not(caption),\n.visually-hidden-focusable:not(:focus):not(:focus-within):not(caption) {\n  position: absolute !important;\n}\n\n.stretched-link::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1;\n  content: \"\";\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: 0.25;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.float-start {\n  float: left !important;\n}\n\n.float-end {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n.object-fit-contain {\n  object-fit: contain !important;\n}\n\n.object-fit-cover {\n  object-fit: cover !important;\n}\n\n.object-fit-fill {\n  object-fit: fill !important;\n}\n\n.object-fit-scale {\n  object-fit: scale-down !important;\n}\n\n.object-fit-none {\n  object-fit: none !important;\n}\n\n.opacity-0 {\n  opacity: 0 !important;\n}\n\n.opacity-25 {\n  opacity: 0.25 !important;\n}\n\n.opacity-50 {\n  opacity: 0.5 !important;\n}\n\n.opacity-75 {\n  opacity: 0.75 !important;\n}\n\n.opacity-100 {\n  opacity: 1 !important;\n}\n\n.overflow-auto {\n  overflow: auto !important;\n}\n\n.overflow-hidden {\n  overflow: hidden !important;\n}\n\n.overflow-visible {\n  overflow: visible !important;\n}\n\n.overflow-scroll {\n  overflow: scroll !important;\n}\n\n.overflow-x-auto {\n  overflow-x: auto !important;\n}\n\n.overflow-x-hidden {\n  overflow-x: hidden !important;\n}\n\n.overflow-x-visible {\n  overflow-x: visible !important;\n}\n\n.overflow-x-scroll {\n  overflow-x: scroll !important;\n}\n\n.overflow-y-auto {\n  overflow-y: auto !important;\n}\n\n.overflow-y-hidden {\n  overflow-y: hidden !important;\n}\n\n.overflow-y-visible {\n  overflow-y: visible !important;\n}\n\n.overflow-y-scroll {\n  overflow-y: scroll !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.shadow {\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n}\n\n.shadow-sm {\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;\n}\n\n.shadow-lg {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;\n}\n\n.shadow-none {\n  box-shadow: none !important;\n}\n\n.focus-ring-primary {\n  --bs-focus-ring-color: rgba(var(--bs-primary-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-secondary {\n  --bs-focus-ring-color: rgba(var(--bs-secondary-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-success {\n  --bs-focus-ring-color: rgba(var(--bs-success-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-info {\n  --bs-focus-ring-color: rgba(var(--bs-info-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-warning {\n  --bs-focus-ring-color: rgba(var(--bs-warning-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-danger {\n  --bs-focus-ring-color: rgba(var(--bs-danger-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-light {\n  --bs-focus-ring-color: rgba(var(--bs-light-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-dark {\n  --bs-focus-ring-color: rgba(var(--bs-dark-rgb), var(--bs-focus-ring-opacity));\n}\n\n.position-static {\n  position: static !important;\n}\n\n.position-relative {\n  position: relative !important;\n}\n\n.position-absolute {\n  position: absolute !important;\n}\n\n.position-fixed {\n  position: fixed !important;\n}\n\n.position-sticky {\n  position: sticky !important;\n}\n\n.top-0 {\n  top: 0 !important;\n}\n\n.top-50 {\n  top: 50% !important;\n}\n\n.top-100 {\n  top: 100% !important;\n}\n\n.bottom-0 {\n  bottom: 0 !important;\n}\n\n.bottom-50 {\n  bottom: 50% !important;\n}\n\n.bottom-100 {\n  bottom: 100% !important;\n}\n\n.start-0 {\n  left: 0 !important;\n}\n\n.start-50 {\n  left: 50% !important;\n}\n\n.start-100 {\n  left: 100% !important;\n}\n\n.end-0 {\n  right: 0 !important;\n}\n\n.end-50 {\n  right: 50% !important;\n}\n\n.end-100 {\n  right: 100% !important;\n}\n\n.translate-middle {\n  transform: translate(-50%, -50%) !important;\n}\n\n.translate-middle-x {\n  transform: translateX(-50%) !important;\n}\n\n.translate-middle-y {\n  transform: translateY(-50%) !important;\n}\n\n.border {\n  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top {\n  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-end {\n  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-end-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom {\n  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-start {\n  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-start-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-secondary {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-success {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-info {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-warning {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-danger {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-light {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-dark {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-black {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-black-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-white {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-primary-subtle {\n  border-color: var(--bs-primary-border-subtle) !important;\n}\n\n.border-secondary-subtle {\n  border-color: var(--bs-secondary-border-subtle) !important;\n}\n\n.border-success-subtle {\n  border-color: var(--bs-success-border-subtle) !important;\n}\n\n.border-info-subtle {\n  border-color: var(--bs-info-border-subtle) !important;\n}\n\n.border-warning-subtle {\n  border-color: var(--bs-warning-border-subtle) !important;\n}\n\n.border-danger-subtle {\n  border-color: var(--bs-danger-border-subtle) !important;\n}\n\n.border-light-subtle {\n  border-color: var(--bs-light-border-subtle) !important;\n}\n\n.border-dark-subtle {\n  border-color: var(--bs-dark-border-subtle) !important;\n}\n\n.border-1 {\n  border-width: 1px !important;\n}\n\n.border-2 {\n  border-width: 2px !important;\n}\n\n.border-3 {\n  border-width: 3px !important;\n}\n\n.border-4 {\n  border-width: 4px !important;\n}\n\n.border-5 {\n  border-width: 5px !important;\n}\n\n.border-opacity-10 {\n  --bs-border-opacity: 0.1;\n}\n\n.border-opacity-25 {\n  --bs-border-opacity: 0.25;\n}\n\n.border-opacity-50 {\n  --bs-border-opacity: 0.5;\n}\n\n.border-opacity-75 {\n  --bs-border-opacity: 0.75;\n}\n\n.border-opacity-100 {\n  --bs-border-opacity: 1;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.w-auto {\n  width: auto !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.vw-100 {\n  width: 100vw !important;\n}\n\n.min-vw-100 {\n  min-width: 100vw !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.h-auto {\n  height: auto !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.vh-100 {\n  height: 100vh !important;\n}\n\n.min-vh-100 {\n  min-height: 100vh !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.25rem !important;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n.me-3 {\n  margin-right: 1rem !important;\n}\n\n.me-4 {\n  margin-right: 1.5rem !important;\n}\n\n.me-5 {\n  margin-right: 3rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.25rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.5rem !important;\n}\n\n.ms-3 {\n  margin-left: 1rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.5rem !important;\n}\n\n.ms-5 {\n  margin-left: 3rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pe-3 {\n  padding-right: 1rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pe-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.25rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.5rem !important;\n}\n\n.ps-3 {\n  padding-left: 1rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.5rem !important;\n}\n\n.ps-5 {\n  padding-left: 3rem !important;\n}\n\n.gap-0 {\n  gap: 0 !important;\n}\n\n.gap-1 {\n  gap: 0.25rem !important;\n}\n\n.gap-2 {\n  gap: 0.5rem !important;\n}\n\n.gap-3 {\n  gap: 1rem !important;\n}\n\n.gap-4 {\n  gap: 1.5rem !important;\n}\n\n.gap-5 {\n  gap: 3rem !important;\n}\n\n.row-gap-0 {\n  row-gap: 0 !important;\n}\n\n.row-gap-1 {\n  row-gap: 0.25rem !important;\n}\n\n.row-gap-2 {\n  row-gap: 0.5rem !important;\n}\n\n.row-gap-3 {\n  row-gap: 1rem !important;\n}\n\n.row-gap-4 {\n  row-gap: 1.5rem !important;\n}\n\n.row-gap-5 {\n  row-gap: 3rem !important;\n}\n\n.column-gap-0 {\n  column-gap: 0 !important;\n}\n\n.column-gap-1 {\n  column-gap: 0.25rem !important;\n}\n\n.column-gap-2 {\n  column-gap: 0.5rem !important;\n}\n\n.column-gap-3 {\n  column-gap: 1rem !important;\n}\n\n.column-gap-4 {\n  column-gap: 1.5rem !important;\n}\n\n.column-gap-5 {\n  column-gap: 3rem !important;\n}\n\n.font-monospace {\n  font-family: var(--bs-font-monospace) !important;\n}\n\n.fs-1 {\n  font-size: calc(1.375rem + 1.5vw) !important;\n}\n\n.fs-2 {\n  font-size: calc(1.325rem + 0.9vw) !important;\n}\n\n.fs-3 {\n  font-size: calc(1.3rem + 0.6vw) !important;\n}\n\n.fs-4 {\n  font-size: calc(1.275rem + 0.3vw) !important;\n}\n\n.fs-5 {\n  font-size: 1.25rem !important;\n}\n\n.fs-6 {\n  font-size: 1rem !important;\n}\n\n.fst-italic {\n  font-style: italic !important;\n}\n\n.fst-normal {\n  font-style: normal !important;\n}\n\n.fw-lighter {\n  font-weight: lighter !important;\n}\n\n.fw-light {\n  font-weight: 300 !important;\n}\n\n.fw-normal {\n  font-weight: 400 !important;\n}\n\n.fw-medium {\n  font-weight: 500 !important;\n}\n\n.fw-semibold {\n  font-weight: 600 !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.fw-bolder {\n  font-weight: bolder !important;\n}\n\n.lh-1 {\n  line-height: 1 !important;\n}\n\n.lh-sm {\n  line-height: 1.25 !important;\n}\n\n.lh-base {\n  line-height: 1.5 !important;\n}\n\n.lh-lg {\n  line-height: 2 !important;\n}\n\n.text-start {\n  text-align: left !important;\n}\n\n.text-end {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n.text-decoration-none {\n  text-decoration: none !important;\n}\n\n.text-decoration-underline {\n  text-decoration: underline !important;\n}\n\n.text-decoration-line-through {\n  text-decoration: line-through !important;\n}\n\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.text-wrap {\n  white-space: normal !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n/* rtl:begin:remove */\n.text-break {\n  word-wrap: break-word !important;\n  word-break: break-word !important;\n}\n\n/* rtl:end:remove */\n.text-primary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-secondary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-success {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-info {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-warning {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-danger {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-light {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-dark {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-black {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-white {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-body {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-muted {\n  --bs-text-opacity: 1;\n  color: var(--bs-secondary-color) !important;\n}\n\n.text-black-50 {\n  --bs-text-opacity: 1;\n  color: rgba(0, 0, 0, 0.5) !important;\n}\n\n.text-white-50 {\n  --bs-text-opacity: 1;\n  color: rgba(255, 255, 255, 0.5) !important;\n}\n\n.text-body-secondary {\n  --bs-text-opacity: 1;\n  color: var(--bs-secondary-color) !important;\n}\n\n.text-body-tertiary {\n  --bs-text-opacity: 1;\n  color: var(--bs-tertiary-color) !important;\n}\n\n.text-body-emphasis {\n  --bs-text-opacity: 1;\n  color: var(--bs-emphasis-color) !important;\n}\n\n.text-reset {\n  --bs-text-opacity: 1;\n  color: inherit !important;\n}\n\n.text-opacity-25 {\n  --bs-text-opacity: 0.25;\n}\n\n.text-opacity-50 {\n  --bs-text-opacity: 0.5;\n}\n\n.text-opacity-75 {\n  --bs-text-opacity: 0.75;\n}\n\n.text-opacity-100 {\n  --bs-text-opacity: 1;\n}\n\n.text-primary-emphasis {\n  color: var(--bs-primary-text-emphasis) !important;\n}\n\n.text-secondary-emphasis {\n  color: var(--bs-secondary-text-emphasis) !important;\n}\n\n.text-success-emphasis {\n  color: var(--bs-success-text-emphasis) !important;\n}\n\n.text-info-emphasis {\n  color: var(--bs-info-text-emphasis) !important;\n}\n\n.text-warning-emphasis {\n  color: var(--bs-warning-text-emphasis) !important;\n}\n\n.text-danger-emphasis {\n  color: var(--bs-danger-text-emphasis) !important;\n}\n\n.text-light-emphasis {\n  color: var(--bs-light-text-emphasis) !important;\n}\n\n.text-dark-emphasis {\n  color: var(--bs-dark-text-emphasis) !important;\n}\n\n.link-opacity-10 {\n  --bs-link-opacity: 0.1;\n}\n\n.link-opacity-10-hover:hover {\n  --bs-link-opacity: 0.1;\n}\n\n.link-opacity-25 {\n  --bs-link-opacity: 0.25;\n}\n\n.link-opacity-25-hover:hover {\n  --bs-link-opacity: 0.25;\n}\n\n.link-opacity-50 {\n  --bs-link-opacity: 0.5;\n}\n\n.link-opacity-50-hover:hover {\n  --bs-link-opacity: 0.5;\n}\n\n.link-opacity-75 {\n  --bs-link-opacity: 0.75;\n}\n\n.link-opacity-75-hover:hover {\n  --bs-link-opacity: 0.75;\n}\n\n.link-opacity-100 {\n  --bs-link-opacity: 1;\n}\n\n.link-opacity-100-hover:hover {\n  --bs-link-opacity: 1;\n}\n\n.link-offset-1 {\n  text-underline-offset: 0.125em !important;\n}\n\n.link-offset-1-hover:hover {\n  text-underline-offset: 0.125em !important;\n}\n\n.link-offset-2 {\n  text-underline-offset: 0.25em !important;\n}\n\n.link-offset-2-hover:hover {\n  text-underline-offset: 0.25em !important;\n}\n\n.link-offset-3 {\n  text-underline-offset: 0.375em !important;\n}\n\n.link-offset-3-hover:hover {\n  text-underline-offset: 0.375em !important;\n}\n\n.link-underline-primary {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-primary-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-secondary {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-secondary-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-success {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-success-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-info {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-info-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-warning {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-warning-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-danger {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-danger-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-light {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-light-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-dark {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-dark-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-underline-opacity-10 {\n  --bs-link-underline-opacity: 0.1;\n}\n\n.link-underline-opacity-10-hover:hover {\n  --bs-link-underline-opacity: 0.1;\n}\n\n.link-underline-opacity-25 {\n  --bs-link-underline-opacity: 0.25;\n}\n\n.link-underline-opacity-25-hover:hover {\n  --bs-link-underline-opacity: 0.25;\n}\n\n.link-underline-opacity-50 {\n  --bs-link-underline-opacity: 0.5;\n}\n\n.link-underline-opacity-50-hover:hover {\n  --bs-link-underline-opacity: 0.5;\n}\n\n.link-underline-opacity-75 {\n  --bs-link-underline-opacity: 0.75;\n}\n\n.link-underline-opacity-75-hover:hover {\n  --bs-link-underline-opacity: 0.75;\n}\n\n.link-underline-opacity-100 {\n  --bs-link-underline-opacity: 1;\n}\n\n.link-underline-opacity-100-hover:hover {\n  --bs-link-underline-opacity: 1;\n}\n\n.bg-primary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-success {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-info {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-warning {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-danger {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-light {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-dark {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-black {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-white {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-transparent {\n  --bs-bg-opacity: 1;\n  background-color: transparent !important;\n}\n\n.bg-body-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body-tertiary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-opacity-10 {\n  --bs-bg-opacity: 0.1;\n}\n\n.bg-opacity-25 {\n  --bs-bg-opacity: 0.25;\n}\n\n.bg-opacity-50 {\n  --bs-bg-opacity: 0.5;\n}\n\n.bg-opacity-75 {\n  --bs-bg-opacity: 0.75;\n}\n\n.bg-opacity-100 {\n  --bs-bg-opacity: 1;\n}\n\n.bg-primary-subtle {\n  background-color: var(--bs-primary-bg-subtle) !important;\n}\n\n.bg-secondary-subtle {\n  background-color: var(--bs-secondary-bg-subtle) !important;\n}\n\n.bg-success-subtle {\n  background-color: var(--bs-success-bg-subtle) !important;\n}\n\n.bg-info-subtle {\n  background-color: var(--bs-info-bg-subtle) !important;\n}\n\n.bg-warning-subtle {\n  background-color: var(--bs-warning-bg-subtle) !important;\n}\n\n.bg-danger-subtle {\n  background-color: var(--bs-danger-bg-subtle) !important;\n}\n\n.bg-light-subtle {\n  background-color: var(--bs-light-bg-subtle) !important;\n}\n\n.bg-dark-subtle {\n  background-color: var(--bs-dark-bg-subtle) !important;\n}\n\n.bg-gradient {\n  background-image: var(--bs-gradient) !important;\n}\n\n.user-select-all {\n  user-select: all !important;\n}\n\n.user-select-auto {\n  user-select: auto !important;\n}\n\n.user-select-none {\n  user-select: none !important;\n}\n\n.pe-none {\n  pointer-events: none !important;\n}\n\n.pe-auto {\n  pointer-events: auto !important;\n}\n\n.rounded {\n  border-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n\n.rounded-1 {\n  border-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-2 {\n  border-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-3 {\n  border-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-4 {\n  border-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-5 {\n  border-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-top {\n  border-top-left-radius: var(--bs-border-radius) !important;\n  border-top-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-top-0 {\n  border-top-left-radius: 0 !important;\n  border-top-right-radius: 0 !important;\n}\n\n.rounded-top-1 {\n  border-top-left-radius: var(--bs-border-radius-sm) !important;\n  border-top-right-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-top-2 {\n  border-top-left-radius: var(--bs-border-radius) !important;\n  border-top-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-top-3 {\n  border-top-left-radius: var(--bs-border-radius-lg) !important;\n  border-top-right-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-top-4 {\n  border-top-left-radius: var(--bs-border-radius-xl) !important;\n  border-top-right-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-top-5 {\n  border-top-left-radius: var(--bs-border-radius-xxl) !important;\n  border-top-right-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-top-circle {\n  border-top-left-radius: 50% !important;\n  border-top-right-radius: 50% !important;\n}\n\n.rounded-top-pill {\n  border-top-left-radius: var(--bs-border-radius-pill) !important;\n  border-top-right-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-end {\n  border-top-right-radius: var(--bs-border-radius) !important;\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-end-0 {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n}\n\n.rounded-end-1 {\n  border-top-right-radius: var(--bs-border-radius-sm) !important;\n  border-bottom-right-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-end-2 {\n  border-top-right-radius: var(--bs-border-radius) !important;\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-end-3 {\n  border-top-right-radius: var(--bs-border-radius-lg) !important;\n  border-bottom-right-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-end-4 {\n  border-top-right-radius: var(--bs-border-radius-xl) !important;\n  border-bottom-right-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-end-5 {\n  border-top-right-radius: var(--bs-border-radius-xxl) !important;\n  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-end-circle {\n  border-top-right-radius: 50% !important;\n  border-bottom-right-radius: 50% !important;\n}\n\n.rounded-end-pill {\n  border-top-right-radius: var(--bs-border-radius-pill) !important;\n  border-bottom-right-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-bottom-0 {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n\n.rounded-bottom-1 {\n  border-bottom-right-radius: var(--bs-border-radius-sm) !important;\n  border-bottom-left-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-bottom-2 {\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-bottom-3 {\n  border-bottom-right-radius: var(--bs-border-radius-lg) !important;\n  border-bottom-left-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-bottom-4 {\n  border-bottom-right-radius: var(--bs-border-radius-xl) !important;\n  border-bottom-left-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-bottom-5 {\n  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;\n  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-bottom-circle {\n  border-bottom-right-radius: 50% !important;\n  border-bottom-left-radius: 50% !important;\n}\n\n.rounded-bottom-pill {\n  border-bottom-right-radius: var(--bs-border-radius-pill) !important;\n  border-bottom-left-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-start {\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n  border-top-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-start-0 {\n  border-bottom-left-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n\n.rounded-start-1 {\n  border-bottom-left-radius: var(--bs-border-radius-sm) !important;\n  border-top-left-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-start-2 {\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n  border-top-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-start-3 {\n  border-bottom-left-radius: var(--bs-border-radius-lg) !important;\n  border-top-left-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-start-4 {\n  border-bottom-left-radius: var(--bs-border-radius-xl) !important;\n  border-top-left-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-start-5 {\n  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;\n  border-top-left-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-start-circle {\n  border-bottom-left-radius: 50% !important;\n  border-top-left-radius: 50% !important;\n}\n\n.rounded-start-pill {\n  border-bottom-left-radius: var(--bs-border-radius-pill) !important;\n  border-top-left-radius: var(--bs-border-radius-pill) !important;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n.z-n1 {\n  z-index: -1 !important;\n}\n\n.z-0 {\n  z-index: 0 !important;\n}\n\n.z-1 {\n  z-index: 1 !important;\n}\n\n.z-2 {\n  z-index: 2 !important;\n}\n\n.z-3 {\n  z-index: 3 !important;\n}\n\n@media (min-width: 576px) {\n  .float-sm-start {\n    float: left !important;\n  }\n  .float-sm-end {\n    float: right !important;\n  }\n  .float-sm-none {\n    float: none !important;\n  }\n  .object-fit-sm-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-sm-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-sm-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-sm-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-sm-none {\n    object-fit: none !important;\n  }\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-grid {\n    display: grid !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-sm-none {\n    display: none !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n  .order-sm-first {\n    order: -1 !important;\n  }\n  .order-sm-0 {\n    order: 0 !important;\n  }\n  .order-sm-1 {\n    order: 1 !important;\n  }\n  .order-sm-2 {\n    order: 2 !important;\n  }\n  .order-sm-3 {\n    order: 3 !important;\n  }\n  .order-sm-4 {\n    order: 4 !important;\n  }\n  .order-sm-5 {\n    order: 5 !important;\n  }\n  .order-sm-last {\n    order: 6 !important;\n  }\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n  .me-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-sm-3 {\n    margin-right: 1rem !important;\n  }\n  .me-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-sm-5 {\n    margin-right: 3rem !important;\n  }\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n  .ms-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-sm-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-sm-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pe-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-sm-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-sm-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n  .ps-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-sm-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-sm-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-sm-0 {\n    gap: 0 !important;\n  }\n  .gap-sm-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-sm-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-sm-3 {\n    gap: 1rem !important;\n  }\n  .gap-sm-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-sm-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-sm-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-sm-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-sm-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-sm-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-sm-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-sm-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-sm-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-sm-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-sm-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-sm-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-sm-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-sm-5 {\n    column-gap: 3rem !important;\n  }\n  .text-sm-start {\n    text-align: left !important;\n  }\n  .text-sm-end {\n    text-align: right !important;\n  }\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 768px) {\n  .float-md-start {\n    float: left !important;\n  }\n  .float-md-end {\n    float: right !important;\n  }\n  .float-md-none {\n    float: none !important;\n  }\n  .object-fit-md-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-md-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-md-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-md-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-md-none {\n    object-fit: none !important;\n  }\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-grid {\n    display: grid !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-md-none {\n    display: none !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n  .order-md-first {\n    order: -1 !important;\n  }\n  .order-md-0 {\n    order: 0 !important;\n  }\n  .order-md-1 {\n    order: 1 !important;\n  }\n  .order-md-2 {\n    order: 2 !important;\n  }\n  .order-md-3 {\n    order: 3 !important;\n  }\n  .order-md-4 {\n    order: 4 !important;\n  }\n  .order-md-5 {\n    order: 5 !important;\n  }\n  .order-md-last {\n    order: 6 !important;\n  }\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n  .me-md-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-md-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-md-3 {\n    margin-right: 1rem !important;\n  }\n  .me-md-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-md-5 {\n    margin-right: 3rem !important;\n  }\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n  .ms-md-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-md-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-md-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-md-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-md-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n  .pe-md-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-md-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-md-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-md-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-md-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n  .ps-md-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-md-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-md-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-md-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-md-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-md-0 {\n    gap: 0 !important;\n  }\n  .gap-md-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-md-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-md-3 {\n    gap: 1rem !important;\n  }\n  .gap-md-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-md-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-md-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-md-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-md-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-md-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-md-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-md-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-md-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-md-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-md-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-md-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-md-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-md-5 {\n    column-gap: 3rem !important;\n  }\n  .text-md-start {\n    text-align: left !important;\n  }\n  .text-md-end {\n    text-align: right !important;\n  }\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 992px) {\n  .float-lg-start {\n    float: left !important;\n  }\n  .float-lg-end {\n    float: right !important;\n  }\n  .float-lg-none {\n    float: none !important;\n  }\n  .object-fit-lg-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-lg-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-lg-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-lg-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-lg-none {\n    object-fit: none !important;\n  }\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-grid {\n    display: grid !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-lg-none {\n    display: none !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n  .order-lg-first {\n    order: -1 !important;\n  }\n  .order-lg-0 {\n    order: 0 !important;\n  }\n  .order-lg-1 {\n    order: 1 !important;\n  }\n  .order-lg-2 {\n    order: 2 !important;\n  }\n  .order-lg-3 {\n    order: 3 !important;\n  }\n  .order-lg-4 {\n    order: 4 !important;\n  }\n  .order-lg-5 {\n    order: 5 !important;\n  }\n  .order-lg-last {\n    order: 6 !important;\n  }\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n  .me-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-lg-3 {\n    margin-right: 1rem !important;\n  }\n  .me-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-lg-5 {\n    margin-right: 3rem !important;\n  }\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n  .ms-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-lg-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-lg-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pe-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-lg-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-lg-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n  .ps-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-lg-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-lg-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-lg-0 {\n    gap: 0 !important;\n  }\n  .gap-lg-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-lg-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-lg-3 {\n    gap: 1rem !important;\n  }\n  .gap-lg-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-lg-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-lg-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-lg-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-lg-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-lg-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-lg-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-lg-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-lg-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-lg-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-lg-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-lg-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-lg-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-lg-5 {\n    column-gap: 3rem !important;\n  }\n  .text-lg-start {\n    text-align: left !important;\n  }\n  .text-lg-end {\n    text-align: right !important;\n  }\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .float-xl-start {\n    float: left !important;\n  }\n  .float-xl-end {\n    float: right !important;\n  }\n  .float-xl-none {\n    float: none !important;\n  }\n  .object-fit-xl-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-xl-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-xl-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-xl-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-xl-none {\n    object-fit: none !important;\n  }\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-grid {\n    display: grid !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xl-none {\n    display: none !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xl-first {\n    order: -1 !important;\n  }\n  .order-xl-0 {\n    order: 0 !important;\n  }\n  .order-xl-1 {\n    order: 1 !important;\n  }\n  .order-xl-2 {\n    order: 2 !important;\n  }\n  .order-xl-3 {\n    order: 3 !important;\n  }\n  .order-xl-4 {\n    order: 4 !important;\n  }\n  .order-xl-5 {\n    order: 5 !important;\n  }\n  .order-xl-last {\n    order: 6 !important;\n  }\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-xl-3 {\n    margin-right: 1rem !important;\n  }\n  .me-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-xl-5 {\n    margin-right: 3rem !important;\n  }\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-xl-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-xl-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-xl-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-xl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-xl-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-xl-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-xl-0 {\n    gap: 0 !important;\n  }\n  .gap-xl-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-xl-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-xl-3 {\n    gap: 1rem !important;\n  }\n  .gap-xl-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-xl-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-xl-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-xl-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-xl-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-xl-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-xl-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-xl-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-xl-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-xl-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-xl-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-xl-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-xl-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-xl-5 {\n    column-gap: 3rem !important;\n  }\n  .text-xl-start {\n    text-align: left !important;\n  }\n  .text-xl-end {\n    text-align: right !important;\n  }\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1400px) {\n  .float-xxl-start {\n    float: left !important;\n  }\n  .float-xxl-end {\n    float: right !important;\n  }\n  .float-xxl-none {\n    float: none !important;\n  }\n  .object-fit-xxl-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-xxl-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-xxl-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-xxl-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-xxl-none {\n    object-fit: none !important;\n  }\n  .d-xxl-inline {\n    display: inline !important;\n  }\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xxl-block {\n    display: block !important;\n  }\n  .d-xxl-grid {\n    display: grid !important;\n  }\n  .d-xxl-table {\n    display: table !important;\n  }\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xxl-flex {\n    display: flex !important;\n  }\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xxl-none {\n    display: none !important;\n  }\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xxl-first {\n    order: -1 !important;\n  }\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n  .order-xxl-last {\n    order: 6 !important;\n  }\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n  .m-xxl-1 {\n    margin: 0.25rem !important;\n  }\n  .m-xxl-2 {\n    margin: 0.5rem !important;\n  }\n  .m-xxl-3 {\n    margin: 1rem !important;\n  }\n  .m-xxl-4 {\n    margin: 1.5rem !important;\n  }\n  .m-xxl-5 {\n    margin: 3rem !important;\n  }\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xxl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-xxl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-xxl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-xxl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-xxl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xxl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-xxl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-xxl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-xxl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-xxl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xxl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-xxl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-xxl-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-xxl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-xxl-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xxl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-xxl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-xxl-3 {\n    margin-right: 1rem !important;\n  }\n  .me-xxl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-xxl-5 {\n    margin-right: 3rem !important;\n  }\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xxl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-xxl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-xxl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-xxl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-xxl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xxl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-xxl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-xxl-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-xxl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-xxl-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n  .p-xxl-1 {\n    padding: 0.25rem !important;\n  }\n  .p-xxl-2 {\n    padding: 0.5rem !important;\n  }\n  .p-xxl-3 {\n    padding: 1rem !important;\n  }\n  .p-xxl-4 {\n    padding: 1.5rem !important;\n  }\n  .p-xxl-5 {\n    padding: 3rem !important;\n  }\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xxl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-xxl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-xxl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-xxl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-xxl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xxl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-xxl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-xxl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-xxl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-xxl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xxl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-xxl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-xxl-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-xxl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-xxl-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xxl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-xxl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-xxl-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-xxl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-xxl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xxl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-xxl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-xxl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-xxl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-xxl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xxl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-xxl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-xxl-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-xxl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-xxl-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-xxl-0 {\n    gap: 0 !important;\n  }\n  .gap-xxl-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-xxl-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-xxl-3 {\n    gap: 1rem !important;\n  }\n  .gap-xxl-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-xxl-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-xxl-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-xxl-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-xxl-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-xxl-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-xxl-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-xxl-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-xxl-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-xxl-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-xxl-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-xxl-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-xxl-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-xxl-5 {\n    column-gap: 3rem !important;\n  }\n  .text-xxl-start {\n    text-align: left !important;\n  }\n  .text-xxl-end {\n    text-align: right !important;\n  }\n  .text-xxl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .fs-1 {\n    font-size: 2.5rem !important;\n  }\n  .fs-2 {\n    font-size: 2rem !important;\n  }\n  .fs-3 {\n    font-size: 1.75rem !important;\n  }\n  .fs-4 {\n    font-size: 1.5rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-grid {\n    display: grid !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=bootstrap-utilities.css.map */\n", "// scss-docs-start color-mode-mixin\r\n@mixin color-mode($mode: light, $root: false) {\r\n  @if $color-mode-type == \"media-query\" {\r\n    @if $root == true {\r\n      @media (prefers-color-scheme: $mode) {\r\n        :root {\r\n          @content;\r\n        }\r\n      }\r\n    } @else {\r\n      @media (prefers-color-scheme: $mode) {\r\n        @content;\r\n      }\r\n    }\r\n  } @else {\r\n    [data-bs-theme=\"#{$mode}\"] {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n// scss-docs-end color-mode-mixin\r\n", "// scss-docs-start clearfix\r\n@mixin clearfix() {\r\n  &::after {\r\n    display: block;\r\n    clear: both;\r\n    content: \"\";\r\n  }\r\n}\r\n// scss-docs-end clearfix\r\n", "// stylelint-disable function-name-case\r\n\r\n// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\r\n@each $color, $value in $theme-colors {\r\n  $color-rgb: to-rgb($value);\r\n  .text-bg-#{$color} {\r\n    color: color-contrast($value) if($enable-important-utilities, !important, null);\r\n    background-color: RGBA($color-rgb, var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);\r\n  }\r\n}\r\n", "// stylelint-disable function-name-case\r\n\r\n// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\r\n@each $color, $value in $theme-colors {\r\n  .link-#{$color} {\r\n    color: RGBA(var(--#{$prefix}#{$color}-rgb, var(--#{$prefix}link-opacity, 1)));\r\n    text-decoration-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-underline-opacity, 1));\r\n\r\n    @if $link-shade-percentage != 0 {\r\n      &:hover,\r\n      &:focus {\r\n        $hover-color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\r\n        color: RGBA(#{to-rgb($hover-color)}, var(--#{$prefix}link-opacity, 1));\r\n        text-decoration-color: RGBA(to-rgb($hover-color), var(--#{$prefix}link-underline-opacity, 1));\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// One-off special link helper as a bridge until v6\r\n.link-body-emphasis {\r\n  color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, 1));\r\n  text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, 1));\r\n\r\n  @if $link-shade-percentage != 0 {\r\n    &:hover,\r\n    &:focus {\r\n      color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, .75));\r\n      text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, .75));\r\n    }\r\n  }\r\n}\r\n", ".focus-ring:focus {\r\n  outline: 0;\r\n  // By default, there is no `--bs-focus-ring-x`, `--bs-focus-ring-y`, or `--bs-focus-ring-blur`, but we provide CSS variables with fallbacks to initial `0` values\r\n  box-shadow: var(--#{$prefix}focus-ring-x, 0) var(--#{$prefix}focus-ring-y, 0) var(--#{$prefix}focus-ring-blur, 0) var(--#{$prefix}focus-ring-width) var(--#{$prefix}focus-ring-color);\r\n}\r\n", ".icon-link {\r\n  display: inline-flex;\r\n  gap: .375rem;\r\n  align-items: center;\r\n  text-decoration-color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, .5));\r\n  text-underline-offset: .25em;\r\n  backface-visibility: hidden;\r\n\r\n  > .bi {\r\n    flex-shrink: 0;\r\n    width: 1em;\r\n    height: 1em;\r\n    @include transition(.2s ease-in-out transform);\r\n  }\r\n}\r\n\r\n.icon-link-hover {\r\n  &:hover,\r\n  &:focus-visible {\r\n    > .bi {\r\n      transform: var(--#{$prefix}icon-link-transform, translate3d(.25em, 0, 0));\r\n    }\r\n  }\r\n}\r\n", "// stylelint-disable property-disallowed-list\r\n@mixin transition($transition...) {\r\n  @if length($transition) == 0 {\r\n    $transition: $transition-base;\r\n  }\r\n\r\n  @if length($transition) > 1 {\r\n    @each $value in $transition {\r\n      @if $value == null or $value == none {\r\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\r\n      }\r\n    }\r\n  }\r\n\r\n  @if $enable-transitions {\r\n    @if nth($transition, 1) != null {\r\n      transition: $transition;\r\n    }\r\n\r\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\r\n      @media (prefers-reduced-motion: reduce) {\r\n        transition: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\r\n\r\n.ratio {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  &::before {\r\n    display: block;\r\n    padding-top: var(--#{$prefix}aspect-ratio);\r\n    content: \"\";\r\n  }\r\n\r\n  > * {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n@each $key, $ratio in $aspect-ratios {\r\n  .ratio-#{$key} {\r\n    --#{$prefix}aspect-ratio: #{$ratio};\r\n  }\r\n}\r\n", "// Shorthand\r\n\r\n.fixed-top {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  left: 0;\r\n  z-index: $zindex-fixed;\r\n}\r\n\r\n.fixed-bottom {\r\n  position: fixed;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: $zindex-fixed;\r\n}\r\n\r\n// Responsive sticky top and bottom\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    .sticky#{$infix}-top {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: $zindex-sticky;\r\n    }\r\n\r\n    .sticky#{$infix}-bottom {\r\n      position: sticky;\r\n      bottom: 0;\r\n      z-index: $zindex-sticky;\r\n    }\r\n  }\r\n}\r\n", "// Variables\r\n//\r\n// Variables should follow the `$component-state-property-size` formula for\r\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\r\n\r\n// Color system\r\n\r\n// scss-docs-start gray-color-variables\r\n$white:    #fff !default;\r\n$gray-base: #3c4b64 !default;\r\n$gray-100: #ececec !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #393939 !default;\r\n$black:    #000 !default;\r\n// scss-docs-end gray-color-variables\r\n\r\n// fusv-disable\r\n// scss-docs-start gray-colors-map\r\n$grays: (\r\n  \"100\": $gray-100,\r\n  \"200\": $gray-200,\r\n  \"300\": $gray-300,\r\n  \"400\": $gray-400,\r\n  \"500\": $gray-500,\r\n  \"600\": $gray-600,\r\n  \"700\": $gray-700,\r\n  \"800\": $gray-800,\r\n  \"900\": $gray-900\r\n) !default;\r\n// scss-docs-end gray-colors-map\r\n// fusv-enable\r\n\r\n$high-emphasis:            rgba(shift-color($gray-base, +26%), .95) !default;\r\n$medium-emphasis:          rgba(shift-color($gray-base, +26%), .681) !default;\r\n$disabled:                 rgba(shift-color($gray-base, +26%), .38) !default;\r\n\r\n$high-emphasis-inverse-new:    rgba($white, .87) !default;\r\n$medium-emphasis-inverse-new:  rgba($white, .6) !default;\r\n$disabled-inverse-new:         rgba($white, .38) !default;\r\n\r\n// scss-docs-start color-variables\r\n$blue:    #1a73b8 !default;\r\n$indigo:  #6610f2 !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #d63384 !default;\r\n$red:     #ea0b16 !default;\r\n$orange:  #fd7e14 !default;\r\n$yellow:  #f0a205 !default;\r\n$green:   #005c29 !default; // buymed color\r\n$green-bold:  #00B453 !default; // buymed color\r\n$green-light: #d1e7bf !default; // buymed color\r\n$green-btn-hover: #0f280b !default; // buymed color\r\n$teal:    #20c997 !default;\r\n$cyan:    #175cd3 !default;\r\n\r\n// scss-docs-end color-variables\r\n\r\n// scss-docs-start colors-map\r\n$colors: (\r\n  \"blue\":       $blue,\r\n  \"indigo\":     $indigo,\r\n  \"purple\":     $purple,\r\n  \"pink\":       $pink,\r\n  \"red\":        $red,\r\n  \"orange\":     $orange,\r\n  \"yellow\":     $yellow,\r\n  \"green\":      $green,\r\n  \"teal\":       $teal,\r\n  \"cyan\":       $cyan,\r\n  \"black\":      $black,\r\n  \"white\":      $white,\r\n  \"gray\":       $gray-600,\r\n  \"gray-dark\":  $gray-800\r\n) !default;\r\n// scss-docs-end colors-map\r\n\r\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\r\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\r\n$min-contrast-ratio:   4.5 !default;\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark:      $black !default;\r\n$color-contrast-light:     $white !default;\r\n\r\n// fusv-disable\r\n$blue-100: tint-color($blue, 80%) !default;\r\n$blue-200: tint-color($blue, 60%) !default;\r\n$blue-300: tint-color($blue, 40%) !default;\r\n$blue-400: tint-color($blue, 20%) !default;\r\n$blue-500: $blue !default;\r\n$blue-600: shade-color($blue, 20%) !default;\r\n$blue-700: shade-color($blue, 40%) !default;\r\n$blue-800: shade-color($blue, 60%) !default;\r\n$blue-900: shade-color($blue, 80%) !default;\r\n\r\n$indigo-100: tint-color($indigo, 80%) !default;\r\n$indigo-200: tint-color($indigo, 60%) !default;\r\n$indigo-300: tint-color($indigo, 40%) !default;\r\n$indigo-400: tint-color($indigo, 20%) !default;\r\n$indigo-500: $indigo !default;\r\n$indigo-600: shade-color($indigo, 20%) !default;\r\n$indigo-700: shade-color($indigo, 40%) !default;\r\n$indigo-800: shade-color($indigo, 60%) !default;\r\n$indigo-900: shade-color($indigo, 80%) !default;\r\n\r\n$purple-100: tint-color($purple, 80%) !default;\r\n$purple-200: tint-color($purple, 60%) !default;\r\n$purple-300: tint-color($purple, 40%) !default;\r\n$purple-400: tint-color($purple, 20%) !default;\r\n$purple-500: $purple !default;\r\n$purple-600: shade-color($purple, 20%) !default;\r\n$purple-700: shade-color($purple, 40%) !default;\r\n$purple-800: shade-color($purple, 60%) !default;\r\n$purple-900: shade-color($purple, 80%) !default;\r\n\r\n$pink-100: tint-color($pink, 80%) !default;\r\n$pink-200: tint-color($pink, 60%) !default;\r\n$pink-300: tint-color($pink, 40%) !default;\r\n$pink-400: tint-color($pink, 20%) !default;\r\n$pink-500: $pink !default;\r\n$pink-600: shade-color($pink, 20%) !default;\r\n$pink-700: shade-color($pink, 40%) !default;\r\n$pink-800: shade-color($pink, 60%) !default;\r\n$pink-900: shade-color($pink, 80%) !default;\r\n\r\n$red-100: tint-color($red, 80%) !default;\r\n$red-200: tint-color($red, 60%) !default;\r\n$red-300: tint-color($red, 40%) !default;\r\n$red-400: tint-color($red, 20%) !default;\r\n$red-500: $red !default;\r\n$red-600: shade-color($red, 20%) !default;\r\n$red-700: shade-color($red, 40%) !default;\r\n$red-800: shade-color($red, 60%) !default;\r\n$red-900: shade-color($red, 80%) !default;\r\n\r\n$orange-100: tint-color($orange, 80%) !default;\r\n$orange-200: tint-color($orange, 60%) !default;\r\n$orange-300: tint-color($orange, 40%) !default;\r\n$orange-400: tint-color($orange, 20%) !default;\r\n$orange-500: $orange !default;\r\n$orange-600: shade-color($orange, 20%) !default;\r\n$orange-700: shade-color($orange, 40%) !default;\r\n$orange-800: shade-color($orange, 60%) !default;\r\n$orange-900: shade-color($orange, 80%) !default;\r\n\r\n$yellow-100: tint-color($yellow, 80%) !default;\r\n$yellow-200: tint-color($yellow, 60%) !default;\r\n$yellow-300: tint-color($yellow, 40%) !default;\r\n$yellow-400: tint-color($yellow, 20%) !default;\r\n$yellow-500: $yellow !default;\r\n$yellow-600: shade-color($yellow, 20%) !default;\r\n$yellow-700: shade-color($yellow, 40%) !default;\r\n$yellow-800: shade-color($yellow, 60%) !default;\r\n$yellow-900: shade-color($yellow, 80%) !default;\r\n\r\n$green-100: tint-color($green, 80%) !default;\r\n$green-200: tint-color($green, 60%) !default;\r\n$green-300: tint-color($green, 40%) !default;\r\n$green-400: tint-color($green, 20%) !default;\r\n$green-500: $green !default;\r\n$green-600: shade-color($green, 20%) !default;\r\n$green-700: shade-color($green, 40%) !default;\r\n$green-800: shade-color($green, 60%) !default;\r\n$green-900: shade-color($green, 80%) !default;\r\n\r\n$teal-100: tint-color($teal, 80%) !default;\r\n$teal-200: tint-color($teal, 60%) !default;\r\n$teal-300: tint-color($teal, 40%) !default;\r\n$teal-400: tint-color($teal, 20%) !default;\r\n$teal-500: $teal !default;\r\n$teal-600: shade-color($teal, 20%) !default;\r\n$teal-700: shade-color($teal, 40%) !default;\r\n$teal-800: shade-color($teal, 60%) !default;\r\n$teal-900: shade-color($teal, 80%) !default;\r\n\r\n$cyan-100: tint-color($cyan, 80%) !default;\r\n$cyan-200: tint-color($cyan, 60%) !default;\r\n$cyan-300: tint-color($cyan, 40%) !default;\r\n$cyan-400: tint-color($cyan, 20%) !default;\r\n$cyan-500: $cyan !default;\r\n$cyan-600: shade-color($cyan, 20%) !default;\r\n$cyan-700: shade-color($cyan, 40%) !default;\r\n$cyan-800: shade-color($cyan, 60%) !default;\r\n$cyan-900: shade-color($cyan, 80%) !default;\r\n\r\n$blues: (\r\n  \"blue-100\": $blue-100,\r\n  \"blue-200\": $blue-200,\r\n  \"blue-300\": $blue-300,\r\n  \"blue-400\": $blue-400,\r\n  \"blue-500\": $blue-500,\r\n  \"blue-600\": $blue-600,\r\n  \"blue-700\": $blue-700,\r\n  \"blue-800\": $blue-800,\r\n  \"blue-900\": $blue-900\r\n) !default;\r\n\r\n$indigos: (\r\n  \"indigo-100\": $indigo-100,\r\n  \"indigo-200\": $indigo-200,\r\n  \"indigo-300\": $indigo-300,\r\n  \"indigo-400\": $indigo-400,\r\n  \"indigo-500\": $indigo-500,\r\n  \"indigo-600\": $indigo-600,\r\n  \"indigo-700\": $indigo-700,\r\n  \"indigo-800\": $indigo-800,\r\n  \"indigo-900\": $indigo-900\r\n) !default;\r\n\r\n$purples: (\r\n  \"purple-100\": $purple-100,\r\n  \"purple-200\": $purple-200,\r\n  \"purple-300\": $purple-300,\r\n  \"purple-400\": $purple-400,\r\n  \"purple-500\": $purple-500,\r\n  \"purple-600\": $purple-600,\r\n  \"purple-700\": $purple-700,\r\n  \"purple-800\": $purple-800,\r\n  \"purple-900\": $purple-900\r\n) !default;\r\n\r\n$pinks: (\r\n  \"pink-100\": $pink-100,\r\n  \"pink-200\": $pink-200,\r\n  \"pink-300\": $pink-300,\r\n  \"pink-400\": $pink-400,\r\n  \"pink-500\": $pink-500,\r\n  \"pink-600\": $pink-600,\r\n  \"pink-700\": $pink-700,\r\n  \"pink-800\": $pink-800,\r\n  \"pink-900\": $pink-900\r\n) !default;\r\n\r\n$reds: (\r\n  \"red-100\": $red-100,\r\n  \"red-200\": $red-200,\r\n  \"red-300\": $red-300,\r\n  \"red-400\": $red-400,\r\n  \"red-500\": $red-500,\r\n  \"red-600\": $red-600,\r\n  \"red-700\": $red-700,\r\n  \"red-800\": $red-800,\r\n  \"red-900\": $red-900\r\n) !default;\r\n\r\n$oranges: (\r\n  \"orange-100\": $orange-100,\r\n  \"orange-200\": $orange-200,\r\n  \"orange-300\": $orange-300,\r\n  \"orange-400\": $orange-400,\r\n  \"orange-500\": $orange-500,\r\n  \"orange-600\": $orange-600,\r\n  \"orange-700\": $orange-700,\r\n  \"orange-800\": $orange-800,\r\n  \"orange-900\": $orange-900\r\n) !default;\r\n\r\n$yellows: (\r\n  \"yellow-100\": $yellow-100,\r\n  \"yellow-200\": $yellow-200,\r\n  \"yellow-300\": $yellow-300,\r\n  \"yellow-400\": $yellow-400,\r\n  \"yellow-500\": $yellow-500,\r\n  \"yellow-600\": $yellow-600,\r\n  \"yellow-700\": $yellow-700,\r\n  \"yellow-800\": $yellow-800,\r\n  \"yellow-900\": $yellow-900\r\n) !default;\r\n\r\n$greens: (\r\n  \"green-100\": $green-100,\r\n  \"green-200\": $green-200,\r\n  \"green-300\": $green-300,\r\n  \"green-400\": $green-400,\r\n  \"green-500\": $green-500,\r\n  \"green-600\": $green-600,\r\n  \"green-700\": $green-700,\r\n  \"green-800\": $green-800,\r\n  \"green-900\": $green-900\r\n) !default;\r\n\r\n$teals: (\r\n  \"teal-100\": $teal-100,\r\n  \"teal-200\": $teal-200,\r\n  \"teal-300\": $teal-300,\r\n  \"teal-400\": $teal-400,\r\n  \"teal-500\": $teal-500,\r\n  \"teal-600\": $teal-600,\r\n  \"teal-700\": $teal-700,\r\n  \"teal-800\": $teal-800,\r\n  \"teal-900\": $teal-900\r\n) !default;\r\n\r\n$cyans: (\r\n  \"cyan-100\": $cyan-100,\r\n  \"cyan-200\": $cyan-200,\r\n  \"cyan-300\": $cyan-300,\r\n  \"cyan-400\": $cyan-400,\r\n  \"cyan-500\": $cyan-500,\r\n  \"cyan-600\": $cyan-600,\r\n  \"cyan-700\": $cyan-700,\r\n  \"cyan-800\": $cyan-800,\r\n  \"cyan-900\": $cyan-900\r\n) !default;\r\n// fusv-enable\r\n\r\n// scss-docs-start theme-color-variables\r\n$primary:       $green !default;\r\n$secondary:     $green-light !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         $gray-100 !default;\r\n$dark:          $gray-900 !default;\r\n// scss-docs-end theme-color-variables\r\n\r\n// hover variables\r\n$primary-bg-hover:       #0f280b !default;\r\n$secondary-bg-hover:     #00563f !default;\r\n$success-bg-hover:       #0f280b !default;\r\n$info-bg-hover:          #1b458d !default;\r\n$warning-bg-hover:       #dd8002 !default;\r\n$danger-bg-hover:        #9a1e12 !default;\r\n$light-bg-hover:         #dcdbdb !default;\r\n$dark-bg-hover:          black !default;\r\n\r\n// scss-docs-start theme-colors-map\r\n$theme-colors: (\r\n  \"primary\":    $primary,\r\n  \"secondary\":  $secondary,\r\n  \"success\":    $success,\r\n  \"info\":       $info,\r\n  \"warning\":    $warning,\r\n  \"danger\":     $danger,\r\n  \"light\":      $light,\r\n  \"dark\":       $dark\r\n) !default;\r\n// scss-docs-end theme-colors-map\r\n\r\n// scss-docs-start theme-text-variables\r\n$primary-text-emphasis:   shade-color($primary, 60%) !default;\r\n$secondary-text-emphasis: shade-color($secondary, 60%) !default;\r\n$success-text-emphasis:   shade-color($success, 60%) !default;\r\n$info-text-emphasis:      shade-color($info, 60%) !default;\r\n$warning-text-emphasis:   shade-color($warning, 60%) !default;\r\n$danger-text-emphasis:    shade-color($danger, 60%) !default;\r\n$light-text-emphasis:     $gray-700 !default;\r\n$dark-text-emphasis:      $gray-700 !default;\r\n// scss-docs-end theme-text-variables\r\n\r\n// scss-docs-start theme-bg-subtle-variables\r\n$primary-bg-subtle:       tint-color($primary, 80%) !default;\r\n$secondary-bg-subtle:     tint-color($secondary, 80%) !default;\r\n$success-bg-subtle:       tint-color($success, 80%) !default;\r\n$info-bg-subtle:          tint-color($info, 80%) !default;\r\n$warning-bg-subtle:       tint-color($warning, 80%) !default;\r\n$danger-bg-subtle:        tint-color($danger, 80%) !default;\r\n$light-bg-subtle:         mix($gray-100, $white) !default;\r\n$dark-bg-subtle:          $gray-400 !default;\r\n// scss-docs-end theme-bg-subtle-variables\r\n\r\n// scss-docs-start theme-border-subtle-variables\r\n$primary-border-subtle:   tint-color($primary, 60%) !default;\r\n$secondary-border-subtle: tint-color($secondary, 60%) !default;\r\n$success-border-subtle:   tint-color($success, 60%) !default;\r\n$info-border-subtle:      tint-color($info, 60%) !default;\r\n$warning-border-subtle:   tint-color($warning, 60%) !default;\r\n$danger-border-subtle:    tint-color($danger, 60%) !default;\r\n$light-border-subtle:     $gray-200 !default;\r\n$dark-border-subtle:      $gray-500 !default;\r\n// scss-docs-end theme-border-subtle-variables\r\n\r\n// Characters which are escaped by the escape-svg function\r\n$escaped-characters: (\r\n  (\"<\", \"%3c\"),\r\n  (\">\", \"%3e\"),\r\n  (\"#\", \"%23\"),\r\n  (\"(\", \"%28\"),\r\n  (\")\", \"%29\"),\r\n) !default;\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-caret:                true !default;\r\n$enable-rounded:              true !default;\r\n$enable-shadows:              false !default;\r\n$enable-gradients:            false !default;\r\n$enable-transitions:          true !default;\r\n$enable-reduced-motion:       true !default;\r\n$enable-smooth-scroll:        true !default;\r\n$enable-grid-classes:         true !default;\r\n$enable-container-classes:    true !default;\r\n$enable-cssgrid:              false !default;\r\n$enable-button-pointers:      true !default;\r\n$enable-rfs:                  true !default;\r\n$enable-validation-icons:     true !default;\r\n$enable-negative-margins:     false !default;\r\n$enable-deprecation-messages: true !default;\r\n$enable-important-utilities:  true !default;\r\n\r\n$enable-dark-mode:            true !default;\r\n$color-mode-type:             data !default; // `data` or `media-query`\r\n\r\n// Prefix for :root CSS variables\r\n\r\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\r\n$prefix:                      $variable-prefix !default;\r\n\r\n// Gradient\r\n//\r\n// The gradient which is added to components if `$enable-gradients` is `true`\r\n// This gradient is also added to elements with `.bg-gradient`\r\n// scss-docs-start variable-gradient\r\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\r\n// scss-docs-end variable-gradient\r\n\r\n// Spacing\r\n//\r\n// Control the default styling of most Bootstrap elements by modifying these\r\n// variables. Mostly focused on spacing.\r\n// You can add more entries to the $spacers map, should you need more variation.\r\n\r\n// scss-docs-start spacer-variables-maps\r\n$spacer: 1rem !default;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * .25,\r\n  2: $spacer * .5,\r\n  3: $spacer,\r\n  4: $spacer * 1.5,\r\n  5: $spacer * 3,\r\n) !default;\r\n// scss-docs-end spacer-variables-maps\r\n\r\n// Position\r\n//\r\n// Define the edge positioning anchors of the position utilities.\r\n\r\n// scss-docs-start position-map\r\n$position-values: (\r\n  0: 0,\r\n  50: 50%,\r\n  100: 100%\r\n) !default;\r\n// scss-docs-end position-map\r\n\r\n// Body\r\n//\r\n// Settings for the `<body>` element.\r\n\r\n$body-text-align:           null !default;\r\n$body-color:                $gray-900 !default;\r\n$body-bg:                   $white !default;\r\n\r\n$body-secondary-color:      rgba($body-color, .75) !default;\r\n$body-secondary-bg:         $gray-200 !default;\r\n\r\n$body-tertiary-color:       rgba($body-color, .5) !default;\r\n$body-tertiary-bg:          $gray-100 !default;\r\n\r\n$body-emphasis-color:       $black !default;\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n\r\n$link-color:                              $primary !default;\r\n$link-decoration:                         underline !default;\r\n$link-shade-percentage:                   20% !default;\r\n$link-hover-color:                        shift-color-hard($link-color) !default;\r\n$link-hover-decoration:                   null !default;\r\n\r\n$stretched-link-pseudo-element:           after !default;\r\n$stretched-link-z-index:                  1 !default;\r\n\r\n// Paragraphs\r\n//\r\n// Style p element.\r\n\r\n$paragraph-margin-bottom:   1rem !default;\r\n\r\n\r\n// Grid breakpoints\r\n//\r\n// Define the minimum dimensions at which your layout will change,\r\n// adapting to different screen sizes, for use in media queries.\r\n\r\n// scss-docs-start grid-breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1400px\r\n) !default;\r\n// scss-docs-end grid-breakpoints\r\n\r\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\r\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\r\n\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px,\r\n  xxl: 1320px\r\n) !default;\r\n// scss-docs-end container-max-widths\r\n\r\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\r\n\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n\r\n$grid-columns:                12 !default;\r\n$grid-gutter-width:           1.5rem !default;\r\n$grid-row-columns:            6 !default;\r\n\r\n// Container padding\r\n\r\n$container-padding-x: $grid-gutter-width !default;\r\n\r\n\r\n// Components\r\n//\r\n// Define common padding and border radius sizes and more.\r\n\r\n// scss-docs-start border-variables\r\n$border-width:                1px !default;\r\n$border-widths: (\r\n  1: 1px,\r\n  2: 2px,\r\n  3: 3px,\r\n  4: 4px,\r\n  5: 5px\r\n) !default;\r\n$border-style:                solid !default;\r\n$border-color:                $gray-300 !default;\r\n$border-color-translucent:    rgba($black, .175) !default;\r\n// scss-docs-end border-variables\r\n\r\n// scss-docs-start border-radius-variables\r\n$border-radius:               .375rem !default;\r\n$border-radius-sm:            .25rem !default;\r\n$border-radius-lg:            .5rem !default;\r\n$border-radius-xl:            1rem !default;\r\n$border-radius-xxl:           2rem !default;\r\n$border-radius-pill:          50rem !default;\r\n// scss-docs-end border-radius-variables\r\n// fusv-disable\r\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\r\n// fusv-enable\r\n\r\n// scss-docs-start box-shadow-variables\r\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\r\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\r\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\r\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\r\n// scss-docs-end box-shadow-variables\r\n\r\n$component-active-color:      $white !default;\r\n$component-active-bg:         $success !default;\r\n\r\n// scss-docs-start focus-ring-variables\r\n$focus-ring-width:      .25rem !default;\r\n$focus-ring-opacity:    .25 !default;\r\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\r\n$focus-ring-blur:       0 !default;\r\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color !default;\r\n// scss-docs-end focus-ring-variables\r\n\r\n// scss-docs-start caret-variables\r\n$caret-width:                 .3em !default;\r\n$caret-vertical-align:        $caret-width * .85 !default;\r\n$caret-spacing:               $caret-width * .85 !default;\r\n// scss-docs-end caret-variables\r\n\r\n$transition-base:             all .2s ease-in-out !default;\r\n$transition-fade:             opacity .15s linear !default;\r\n// scss-docs-start collapse-transition\r\n$transition-collapse:         height .35s ease !default;\r\n$transition-collapse-width:   width .35s ease !default;\r\n// scss-docs-end collapse-transition\r\n\r\n// stylelint-disable function-disallowed-list\r\n// scss-docs-start aspect-ratios\r\n$aspect-ratios: (\r\n  \"1x1\": 100%,\r\n  \"4x3\": calc(3 / 4 * 100%),\r\n  \"16x9\": calc(9 / 16 * 100%),\r\n  \"21x9\": calc(9 / 21 * 100%)\r\n) !default;\r\n// scss-docs-end aspect-ratios\r\n// stylelint-enable function-disallowed-list\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// scss-docs-start font-variables\r\n// stylelint-disable value-keyword-case\r\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\r\n// stylelint-enable value-keyword-case\r\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\r\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\r\n\r\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\r\n// $font-size-base affects the font size of the body text\r\n$font-size-root:              null !default;\r\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\r\n$font-size-sm:                $font-size-base * .875 !default;\r\n$font-size-lg:                $font-size-base * 1.25 !default;\r\n\r\n$font-weight-lighter:         lighter !default;\r\n$font-weight-light:           300 !default;\r\n$font-weight-normal:          400 !default;\r\n$font-weight-medium:          500 !default;\r\n$font-weight-semibold:        600 !default;\r\n$font-weight-bold:            700 !default;\r\n$font-weight-bolder:          bolder !default;\r\n\r\n$font-weight-base:            $font-weight-normal !default;\r\n\r\n$line-height-base:            1.5 !default;\r\n$line-height-sm:              1.25 !default;\r\n$line-height-lg:              2 !default;\r\n\r\n$h1-font-size:                $font-size-base * 2.5 !default;\r\n$h2-font-size:                $font-size-base * 2 !default;\r\n$h3-font-size:                $font-size-base * 1.75 !default;\r\n$h4-font-size:                $font-size-base * 1.5 !default;\r\n$h5-font-size:                $font-size-base * 1.25 !default;\r\n$h6-font-size:                $font-size-base !default;\r\n// scss-docs-end font-variables\r\n\r\n// scss-docs-start font-sizes\r\n$font-sizes: (\r\n  1: $h1-font-size,\r\n  2: $h2-font-size,\r\n  3: $h3-font-size,\r\n  4: $h4-font-size,\r\n  5: $h5-font-size,\r\n  6: $h6-font-size\r\n) !default;\r\n// scss-docs-end font-sizes\r\n\r\n// scss-docs-start headings-variables\r\n$headings-margin-bottom:      $spacer * .5 !default;\r\n$headings-font-family:        null !default;\r\n$headings-font-style:         null !default;\r\n$headings-font-weight:        500 !default;\r\n$headings-line-height:        1.2 !default;\r\n$headings-color:              null !default;\r\n// scss-docs-end headings-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: 5rem,\r\n  2: 4.5rem,\r\n  3: 4rem,\r\n  4: 3.5rem,\r\n  5: 3rem,\r\n  6: 2.5rem\r\n) !default;\r\n\r\n$display-font-family: null !default;\r\n$display-font-style:  null !default;\r\n$display-font-weight: 300 !default;\r\n$display-line-height: $headings-line-height !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size:              $font-size-base * 1.25 !default;\r\n$lead-font-weight:            300 !default;\r\n\r\n$small-font-size:             .875em !default;\r\n\r\n$sub-sup-font-size:           .75em !default;\r\n\r\n$text-high-emphasis:            $high-emphasis !default;\r\n$text-medium-emphasis:          $medium-emphasis !default;\r\n$text-disabled:                 $disabled !default;\r\n\r\n$text-high-emphasis-inverse:    $high-emphasis-inverse-new !default;\r\n$text-medium-emphasis-inverse:  $medium-emphasis-inverse-new !default;\r\n$text-disabled-inverse:         $disabled-inverse-new !default;\r\n\r\n// fusv-disable\r\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\r\n// fusv-enable\r\n\r\n$initialism-font-size:        $small-font-size !default;\r\n\r\n$blockquote-margin-y:         $spacer !default;\r\n$blockquote-font-size:        $font-size-base * 1.25 !default;\r\n$blockquote-footer-color:     $gray-600 !default;\r\n$blockquote-footer-font-size: $small-font-size !default;\r\n\r\n$hr-margin-y:                 $spacer !default;\r\n$hr-color:                    inherit !default;\r\n\r\n// fusv-disable\r\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\r\n$hr-height:                   null !default; // Deprecated in v5.2.0\r\n// fusv-enable\r\n\r\n$hr-border-color:             null !default; // Allows for inherited colors\r\n$hr-border-width:             var(--#{$prefix}border-width) !default;\r\n$hr-opacity:                  .25 !default;\r\n\r\n$legend-margin-bottom:        .5rem !default;\r\n$legend-font-size:            1.5rem !default;\r\n$legend-font-weight:          null !default;\r\n\r\n$dt-font-weight:              $font-weight-bold !default;\r\n\r\n$list-inline-padding:         .5rem !default;\r\n\r\n$mark-padding:                .1875em !default;\r\n$mark-bg:                     $yellow-100 !default;\r\n// scss-docs-end type-variables\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n// scss-docs-start table-variables\r\n$table-cell-padding-y:        .5rem !default;\r\n$table-cell-padding-x:        .5rem !default;\r\n$table-cell-padding-y-sm:     .25rem !default;\r\n$table-cell-padding-x-sm:     .25rem !default;\r\n\r\n$table-cell-vertical-align:   top !default;\r\n\r\n$table-color:                 var(--#{$prefix}body-color) !default;\r\n$table-bg:                    transparent !default;\r\n$table-accent-bg:             transparent !default;\r\n\r\n$table-th-font-weight:        null !default;\r\n\r\n$table-striped-color:         $table-color !default;\r\n$table-striped-bg-factor:     .05 !default;\r\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\r\n\r\n$table-active-color:          $table-color !default;\r\n$table-active-bg-factor:      .1 !default;\r\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\r\n\r\n$table-hover-color:           $table-color !default;\r\n$table-hover-bg-factor:       .075 !default;\r\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\r\n\r\n$table-border-factor:         .1 !default;\r\n$table-border-width:          var(--#{$prefix}border-width) !default;\r\n$table-border-color:          var(--#{$prefix}border-color) !default;\r\n\r\n$table-striped-order:         odd !default;\r\n$table-striped-columns-order: even !default;\r\n\r\n$table-group-separator-color: currentcolor !default;\r\n\r\n$table-caption-color:         var(--#{$prefix}secondary-color) !default;\r\n\r\n$table-bg-scale:              -80% !default;\r\n// scss-docs-end table-variables\r\n\r\n// scss-docs-start table-loop\r\n$table-variants: (\r\n  \"primary\":    shift-color($primary, $table-bg-scale),\r\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\r\n  \"success\":    shift-color($success, $table-bg-scale),\r\n  \"info\":       shift-color($info, $table-bg-scale),\r\n  \"warning\":    shift-color($warning, $table-bg-scale),\r\n  \"danger\":     shift-color($danger, $table-bg-scale),\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n) !default;\r\n// scss-docs-end table-loop\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y:         .375rem !default;\r\n$input-btn-padding-x:         .75rem !default;\r\n$input-btn-font-family:       null !default;\r\n$input-btn-font-size:         $font-size-base !default;\r\n$input-btn-line-height:       $line-height-base !default;\r\n\r\n$input-btn-focus-width:         $focus-ring-width !default;\r\n$input-btn-focus-color-opacity: $focus-ring-opacity !default;\r\n$input-btn-focus-color:         $focus-ring-color !default;\r\n$input-btn-focus-blur:          $focus-ring-blur !default;\r\n$input-btn-focus-box-shadow:    $focus-ring-box-shadow !default;\r\n\r\n$input-btn-padding-y-sm:      .25rem !default;\r\n$input-btn-padding-x-sm:      .5rem !default;\r\n$input-btn-font-size-sm:      $font-size-sm !default;\r\n\r\n$input-btn-padding-y-lg:      .5rem !default;\r\n$input-btn-padding-x-lg:      1rem !default;\r\n$input-btn-font-size-lg:      $font-size-lg !default;\r\n\r\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n\r\n// Buttons\r\n//\r\n// For each of Bootstrap's buttons, define text, background, and border color.\r\n\r\n// scss-docs-start btn-variables\r\n$btn-padding-y:               $input-btn-padding-y !default;\r\n$btn-padding-x:               $input-btn-padding-x !default;\r\n$btn-font-family:             $input-btn-font-family !default;\r\n$btn-font-size:               $input-btn-font-size !default;\r\n$btn-line-height:             $input-btn-line-height !default;\r\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\r\n\r\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\r\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\r\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\r\n\r\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\r\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\r\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\r\n\r\n$btn-border-width:            $input-btn-border-width !default;\r\n\r\n$btn-font-weight:             $font-weight-normal !default;\r\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\r\n$btn-focus-width:             $input-btn-focus-width !default;\r\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\r\n$btn-disabled-opacity:        .65 !default;\r\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\r\n\r\n$btn-link-color:              var(--#{$prefix}link-color) !default;\r\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\r\n$btn-link-disabled-color:     $gray-600 !default;\r\n\r\n// Allows for customizing button radius independently from global border radius\r\n$btn-border-radius:           $border-radius !default;\r\n$btn-border-radius-sm:        $border-radius-sm !default;\r\n$btn-border-radius-lg:        $border-radius-lg !default;\r\n\r\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$btn-hover-bg-shade-amount:       15% !default;\r\n$btn-hover-bg-tint-amount:        15% !default;\r\n$btn-hover-border-shade-amount:   20% !default;\r\n$btn-hover-border-tint-amount:    10% !default;\r\n$btn-active-bg-shade-amount:      20% !default;\r\n$btn-active-bg-tint-amount:       20% !default;\r\n$btn-active-border-shade-amount:  25% !default;\r\n$btn-active-border-tint-amount:   10% !default;\r\n// scss-docs-end btn-variables\r\n\r\n\r\n// Forms\r\n\r\n// scss-docs-start form-text-variables\r\n$form-text-margin-top:                  .25rem !default;\r\n$form-text-font-size:                   $small-font-size !default;\r\n$form-text-font-style:                  null !default;\r\n$form-text-font-weight:                 null !default;\r\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end form-text-variables\r\n\r\n// scss-docs-start form-label-variables\r\n$form-label-margin-bottom:              .25rem !default;\r\n$form-label-font-size:                  null !default;\r\n$form-label-font-style:                 null !default;\r\n$form-label-font-weight:                null !default;\r\n$form-label-color:                      null !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-input-variables\r\n$input-padding-y:                       $input-btn-padding-y !default;\r\n$input-padding-x:                       $input-btn-padding-x !default;\r\n$input-font-family:                     $input-btn-font-family !default;\r\n$input-font-size:                       $input-btn-font-size !default;\r\n$input-font-weight:                     $font-weight-base !default;\r\n$input-line-height:                     $input-btn-line-height !default;\r\n\r\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\r\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\r\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\r\n\r\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\r\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\r\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\r\n\r\n$input-bg:                              var(--#{$prefix}body-bg) !default;\r\n$input-disabled-color:                  null !default;\r\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\r\n$input-disabled-border-color:           null !default;\r\n\r\n$input-color:                           var(--#{$prefix}body-color) !default;\r\n$input-border-color:                    var(--#{$prefix}border-color) !default;\r\n$input-border-width:                    $input-btn-border-width !default;\r\n$input-box-shadow:                      $box-shadow-inset !default;\r\n\r\n$input-border-radius:                   $border-radius !default;\r\n$input-border-radius-sm:                $border-radius-sm !default;\r\n$input-border-radius-lg:                $border-radius-lg !default;\r\n\r\n$input-focus-bg:                        $input-bg !default;\r\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\r\n$input-focus-color:                     $input-color !default;\r\n$input-focus-width:                     $input-btn-focus-width !default;\r\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\r\n\r\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\r\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\r\n\r\n$input-height-border:                   calc($input-border-width * 2) !default; // stylelint-disable-line function-disallowed-list\r\n\r\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\r\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\r\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\r\n\r\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\r\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\r\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\r\n\r\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$form-color-width:                      3rem !default;\r\n// scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-input-width:                  1em !default;\r\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\r\n$form-check-padding-start:                $form-check-input-width + .5em !default;\r\n$form-check-margin-bottom:                .125rem !default;\r\n$form-check-label-color:                  null !default;\r\n$form-check-label-cursor:                 null !default;\r\n$form-check-transition:                   null !default;\r\n\r\n$form-check-input-active-filter:          brightness(90%) !default;\r\n\r\n$form-check-input-bg:                     $input-bg !default;\r\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\r\n$form-check-input-border-radius:          .25em !default;\r\n$form-check-radio-border-radius:          50% !default;\r\n$form-check-input-focus-border:           $input-focus-border-color !default;\r\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\r\n\r\n$form-check-input-checked-color:          $component-active-color !default;\r\n$form-check-input-checked-bg-color:       $component-active-bg !default;\r\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\r\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\r\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\r\n\r\n$form-check-input-indeterminate-color:          $component-active-color !default;\r\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\r\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\r\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\r\n\r\n$form-check-input-disabled-opacity:        .5 !default;\r\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\r\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\r\n\r\n$form-check-inline-margin-end:    1rem !default;\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n$form-switch-color:               rgba($black, .25) !default;\r\n$form-switch-width:               2em !default;\r\n$form-switch-padding-start:       $form-switch-width + .5em !default;\r\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\r\n$form-switch-border-radius:       $form-switch-width !default;\r\n$form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n$form-switch-focus-color:         $input-focus-border-color !default;\r\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\r\n\r\n$form-switch-checked-color:       $component-active-color !default;\r\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\r\n$form-switch-checked-bg-position: right center !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start input-group-variables\r\n$input-group-addon-padding-y:           $input-padding-y !default;\r\n$input-group-addon-padding-x:           $input-padding-x !default;\r\n$input-group-addon-font-weight:         $input-font-weight !default;\r\n$input-group-addon-color:               $input-color !default;\r\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\r\n$input-group-addon-border-color:        $input-border-color !default;\r\n// scss-docs-end input-group-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-padding-y:             $input-padding-y !default;\r\n$form-select-padding-x:             $input-padding-x !default;\r\n$form-select-font-family:           $input-font-family !default;\r\n$form-select-font-size:             $input-font-size !default;\r\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\r\n$form-select-font-weight:           $input-font-weight !default;\r\n$form-select-line-height:           $input-line-height !default;\r\n$form-select-color:                 $input-color !default;\r\n$form-select-bg:                    $input-bg !default;\r\n$form-select-disabled-color:        null !default;\r\n$form-select-disabled-bg:           $input-disabled-bg !default;\r\n$form-select-disabled-border-color: $input-disabled-border-color !default;\r\n$form-select-bg-position:           right $form-select-padding-x center !default;\r\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\r\n$form-select-indicator-color:       $gray-800 !default;\r\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\r\n\r\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\r\n\r\n$form-select-border-width:        $input-border-width !default;\r\n$form-select-border-color:        $input-border-color !default;\r\n$form-select-border-radius:       $input-border-radius !default;\r\n$form-select-box-shadow:          $box-shadow-inset !default;\r\n\r\n$form-select-focus-border-color:  $input-focus-border-color !default;\r\n$form-select-focus-width:         $input-focus-width !default;\r\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\r\n\r\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\r\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\r\n$form-select-font-size-sm:        $input-font-size-sm !default;\r\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\r\n\r\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\r\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\r\n$form-select-font-size-lg:        $input-font-size-lg !default;\r\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\r\n\r\n$form-select-transition:          $input-transition !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-width:          100% !default;\r\n$form-range-track-height:         .5rem !default;\r\n$form-range-track-cursor:         pointer !default;\r\n$form-range-track-bg:             var(--#{$prefix}tertiary-bg) !default;\r\n$form-range-track-border-radius:  1rem !default;\r\n$form-range-track-box-shadow:     $box-shadow-inset !default;\r\n\r\n$form-range-thumb-width:                   1rem !default;\r\n$form-range-thumb-height:                  $form-range-thumb-width !default;\r\n$form-range-thumb-bg:                      $component-active-bg !default;\r\n$form-range-thumb-border:                  0 !default;\r\n$form-range-thumb-border-radius:           1rem !default;\r\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\r\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\r\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\r\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\r\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\r\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-range-variables\r\n\r\n// scss-docs-start form-file-variables\r\n$form-file-button-color:          $input-color !default;\r\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\r\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\r\n// scss-docs-end form-file-variables\r\n\r\n// scss-docs-start form-floating-variables\r\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\r\n$form-floating-line-height:             1.25 !default;\r\n$form-floating-padding-x:               $input-padding-x !default;\r\n$form-floating-padding-y:               1rem !default;\r\n$form-floating-input-padding-t:         1.625rem !default;\r\n$form-floating-input-padding-b:         .625rem !default;\r\n$form-floating-label-height:            1.875em !default;\r\n$form-floating-label-opacity:           .65 !default;\r\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\r\n$form-floating-label-disabled-color:    $gray-600 !default;\r\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\r\n// scss-docs-end form-floating-variables\r\n\r\n// Form validation\r\n\r\n// scss-docs-start form-feedback-variables\r\n$form-feedback-margin-top:          $form-text-margin-top !default;\r\n$form-feedback-font-size:           $form-text-font-size !default;\r\n$form-feedback-font-style:          $form-text-font-style !default;\r\n$form-feedback-valid-color:         $success !default;\r\n$form-feedback-invalid-color:       $danger !default;\r\n\r\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\r\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\r\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\r\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\r\n// scss-docs-end form-feedback-variables\r\n\r\n// scss-docs-start form-validation-colors\r\n$form-valid-color:                  $form-feedback-valid-color !default;\r\n$form-valid-border-color:           $form-feedback-valid-color !default;\r\n$form-invalid-color:                $form-feedback-invalid-color !default;\r\n$form-invalid-border-color:         $form-feedback-invalid-color !default;\r\n// scss-docs-end form-validation-colors\r\n\r\n// scss-docs-start form-validation-states\r\n$form-validation-states: (\r\n  \"valid\": (\r\n    \"color\": var(--#{$prefix}form-valid-color),\r\n    \"icon\": $form-feedback-icon-valid,\r\n    \"tooltip-color\": #fff,\r\n    \"tooltip-bg-color\": var(--#{$prefix}success),\r\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\r\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\r\n  ),\r\n  \"invalid\": (\r\n    \"color\": var(--#{$prefix}form-invalid-color),\r\n    \"icon\": $form-feedback-icon-invalid,\r\n    \"tooltip-color\": #fff,\r\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\r\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\r\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\r\n  )\r\n) !default;\r\n// scss-docs-end form-validation-states\r\n\r\n// Z-index master list\r\n//\r\n// Warning: Avoid customizing these values. They're used for a bird's eye view\r\n// of components dependent on the z-axis and are designed to all work together.\r\n\r\n// scss-docs-start zindex-stack\r\n$zindex-dropdown:                   1000 !default;\r\n$zindex-sticky:                     1020 !default;\r\n$zindex-fixed:                      1030 !default;\r\n$zindex-offcanvas-backdrop:         1040 !default;\r\n$zindex-offcanvas:                  1045 !default;\r\n$zindex-modal-backdrop:             1050 !default;\r\n$zindex-modal:                      1055 !default;\r\n$zindex-popover:                    1070 !default;\r\n$zindex-tooltip:                    1080 !default;\r\n$zindex-toast:                      1090 !default;\r\n// scss-docs-end zindex-stack\r\n\r\n// scss-docs-start zindex-levels-map\r\n$zindex-levels: (\r\n  n1: -1,\r\n  0: 0,\r\n  1: 1,\r\n  2: 2,\r\n  3: 3\r\n) !default;\r\n// scss-docs-end zindex-levels-map\r\n\r\n\r\n// Navs\r\n\r\n// scss-docs-start nav-variables\r\n$nav-link-padding-y:                .5rem !default;\r\n$nav-link-padding-x:                1rem !default;\r\n$nav-link-font-size:                null !default;\r\n$nav-link-font-weight:              null !default;\r\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\r\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\r\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\r\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\r\n$nav-link-focus-box-shadow:         $focus-ring-box-shadow !default;\r\n\r\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\r\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\r\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\r\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\r\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\r\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\r\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\r\n\r\n$nav-pills-border-radius:           $border-radius !default;\r\n$nav-pills-link-active-color:       $component-active-color !default;\r\n$nav-pills-link-active-bg:          $component-active-bg !default;\r\n\r\n$nav-underline-gap:                 1rem !default;\r\n$nav-underline-border-width:        .125rem !default;\r\n$nav-underline-link-active-color:   var(--#{$prefix}emphasis-color) !default;\r\n// scss-docs-end nav-variables\r\n\r\n\r\n// Navbar\r\n\r\n// scss-docs-start navbar-variables\r\n$navbar-padding-y:                  $spacer * .5 !default;\r\n$navbar-padding-x:                  null !default;\r\n\r\n$navbar-nav-link-padding-x:         .5rem !default;\r\n\r\n$navbar-brand-font-size:            $font-size-lg !default;\r\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\r\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\r\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\r\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\r\n$navbar-brand-margin-end:           1rem !default;\r\n\r\n$navbar-toggler-padding-y:          .25rem !default;\r\n$navbar-toggler-padding-x:          .75rem !default;\r\n$navbar-toggler-font-size:          $font-size-lg !default;\r\n$navbar-toggler-border-radius:      $btn-border-radius !default;\r\n$navbar-toggler-focus-width:        $btn-focus-width !default;\r\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\r\n\r\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\r\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\r\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\r\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\r\n$navbar-light-icon-color:           rgba($body-color, .75) !default;\r\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\r\n$navbar-light-brand-color:          $navbar-light-active-color !default;\r\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\r\n// scss-docs-end navbar-variables\r\n\r\n// scss-docs-start navbar-dark-variables\r\n$navbar-dark-color:                 rgba($white, .55) !default;\r\n$navbar-dark-hover-color:           rgba($white, .75) !default;\r\n$navbar-dark-active-color:          $white !default;\r\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\r\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\r\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\r\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\r\n// scss-docs-end navbar-dark-variables\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n// scss-docs-start dropdown-variables\r\n$dropdown-min-width:                10rem !default;\r\n$dropdown-padding-x:                0 !default;\r\n$dropdown-padding-y:                .5rem !default;\r\n$dropdown-spacer:                   .125rem !default;\r\n$dropdown-font-size:                $font-size-base !default;\r\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\r\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\r\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\r\n$dropdown-border-radius:            $border-radius !default;\r\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\r\n$dropdown-inner-border-radius:      calc($dropdown-border-radius - $dropdown-border-width) !default; // stylelint-disable-line function-disallowed-list\r\n$dropdown-divider-bg:               $dropdown-border-color !default;\r\n$dropdown-divider-margin-y:         $spacer * .5 !default;\r\n$dropdown-box-shadow:               $box-shadow !default;\r\n\r\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\r\n$dropdown-link-hover-color:         $dropdown-link-color !default;\r\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\r\n\r\n$dropdown-link-active-color:        $component-active-color !default;\r\n$dropdown-link-active-bg:           $component-active-bg !default;\r\n\r\n$dropdown-link-disabled-color:      var(--#{$prefix}tertiary-color) !default;\r\n\r\n$dropdown-item-padding-y:           $spacer * .25 !default;\r\n$dropdown-item-padding-x:           $spacer !default;\r\n\r\n$dropdown-header-color:             $gray-600 !default;\r\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\r\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\r\n// fusv-disable\r\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\r\n// fusv-enable\r\n// scss-docs-end dropdown-variables\r\n\r\n// scss-docs-start dropdown-dark-variables\r\n$dropdown-dark-color:               $gray-300 !default;\r\n$dropdown-dark-bg:                  $gray-800 !default;\r\n$dropdown-dark-border-color:        $dropdown-border-color !default;\r\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\r\n$dropdown-dark-box-shadow:          null !default;\r\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\r\n$dropdown-dark-link-hover-color:    $white !default;\r\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\r\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\r\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\r\n$dropdown-dark-link-disabled-color: $gray-500 !default;\r\n$dropdown-dark-header-color:        $gray-500 !default;\r\n// scss-docs-end dropdown-dark-variables\r\n\r\n\r\n// Pagination\r\n\r\n// scss-docs-start pagination-variables\r\n$pagination-padding-y:              .375rem !default;\r\n$pagination-padding-x:              .75rem !default;\r\n$pagination-padding-y-sm:           .25rem !default;\r\n$pagination-padding-x-sm:           .5rem !default;\r\n$pagination-padding-y-lg:           .75rem !default;\r\n$pagination-padding-x-lg:           1.5rem !default;\r\n\r\n$pagination-font-size:              $font-size-base !default;\r\n\r\n$pagination-color:                  var(--#{$prefix}link-color) !default;\r\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\r\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\r\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\r\n$pagination-margin-start:           calc($pagination-border-width * -1) !default; // stylelint-disable-line function-disallowed-list\r\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\r\n\r\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\r\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\r\n$pagination-focus-box-shadow:       $focus-ring-box-shadow !default;\r\n$pagination-focus-outline:          0 !default;\r\n\r\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\r\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\r\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\r\n\r\n$pagination-active-color:           $component-active-color !default;\r\n$pagination-active-bg:              $component-active-bg !default;\r\n$pagination-active-border-color:    $component-active-bg !default;\r\n\r\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\r\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\r\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\r\n\r\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$pagination-border-radius-sm:       $border-radius-sm !default;\r\n$pagination-border-radius-lg:       $border-radius-lg !default;\r\n// scss-docs-end pagination-variables\r\n\r\n\r\n// Placeholders\r\n\r\n// scss-docs-start placeholders\r\n$placeholder-opacity-max:           .5 !default;\r\n$placeholder-opacity-min:           .2 !default;\r\n// scss-docs-end placeholders\r\n\r\n// Cards\r\n\r\n// scss-docs-start card-variables\r\n$card-spacer-y:                     $spacer !default;\r\n$card-spacer-x:                     $spacer !default;\r\n$card-title-spacer-y:               $spacer * .5 !default;\r\n$card-title-color:                  null !default;\r\n$card-subtitle-color:               null !default;\r\n$card-border-width:                 var(--#{$prefix}border-width) !default;\r\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\r\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\r\n$card-box-shadow:                   null !default;\r\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\r\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\r\n$card-cap-padding-x:                $card-spacer-x !default;\r\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\r\n$card-cap-color:                    null !default;\r\n$card-height:                       null !default;\r\n$card-color:                        null !default;\r\n$card-bg:                           var(--#{$prefix}body-bg) !default;\r\n$card-img-overlay-padding:          $spacer !default;\r\n$card-group-margin:                 $grid-gutter-width * .5 !default;\r\n// scss-docs-end card-variables\r\n\r\n// Accordion\r\n\r\n// scss-docs-start accordion-variables\r\n$accordion-padding-y:                     1rem !default;\r\n$accordion-padding-x:                     1.25rem !default;\r\n$accordion-color:                         var(--#{$prefix}body-color) !default;\r\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\r\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\r\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\r\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\r\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\r\n\r\n$accordion-body-padding-y:                $accordion-padding-y !default;\r\n$accordion-body-padding-x:                $accordion-padding-x !default;\r\n\r\n$accordion-button-padding-y:              $accordion-padding-y !default;\r\n$accordion-button-padding-x:              $accordion-padding-x !default;\r\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\r\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\r\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\r\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\r\n$accordion-button-active-color:           var(--#{$prefix}primary-text) !default;\r\n\r\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\r\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\r\n\r\n$accordion-icon-width:                    1.25rem !default;\r\n$accordion-icon-color:                    $body-color !default;\r\n$accordion-icon-active-color:             $primary-text-emphasis !default;\r\n$accordion-icon-transition:               transform .2s ease-in-out !default;\r\n$accordion-icon-transform:                rotate(-180deg) !default;\r\n\r\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n// scss-docs-end accordion-variables\r\n\r\n// Tooltips\r\n\r\n// scss-docs-start tooltip-variables\r\n$tooltip-font-size:                 $font-size-sm !default;\r\n$tooltip-max-width:                 200px !default;\r\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\r\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\r\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\r\n$tooltip-opacity:                   .9 !default;\r\n$tooltip-padding-y:                 $spacer * .25 !default;\r\n$tooltip-padding-x:                 $spacer * .5 !default;\r\n$tooltip-margin:                    null !default; // TODO: remove this in v6\r\n\r\n$tooltip-arrow-width:               .8rem !default;\r\n$tooltip-arrow-height:              .4rem !default;\r\n// fusv-disable\r\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\r\n// fusv-enable\r\n// scss-docs-end tooltip-variables\r\n\r\n// Form tooltips must come after regular tooltips\r\n// scss-docs-start tooltip-feedback-variables\r\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\r\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\r\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\r\n$form-feedback-tooltip-line-height:   null !default;\r\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\r\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\r\n// scss-docs-end tooltip-feedback-variables\r\n\r\n\r\n// Popovers\r\n\r\n// scss-docs-start popover-variables\r\n$popover-font-size:                 $font-size-sm !default;\r\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\r\n$popover-max-width:                 276px !default;\r\n$popover-border-width:              var(--#{$prefix}border-width) !default;\r\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\r\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\r\n$popover-inner-border-radius:       calc($popover-border-radius - $popover-border-width) !default; // stylelint-disable-line function-disallowed-list\r\n$popover-box-shadow:                $box-shadow !default;\r\n\r\n$popover-header-font-size:          $font-size-base !default;\r\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\r\n$popover-header-color:              $headings-color !default;\r\n$popover-header-padding-y:          .5rem !default;\r\n$popover-header-padding-x:          $spacer !default;\r\n\r\n$popover-body-color:                var(--#{$prefix}body-color) !default;\r\n$popover-body-padding-y:            $spacer !default;\r\n$popover-body-padding-x:            $spacer !default;\r\n\r\n$popover-arrow-width:               1rem !default;\r\n$popover-arrow-height:              .5rem !default;\r\n// scss-docs-end popover-variables\r\n\r\n// fusv-disable\r\n// Deprecated in Bootstrap 5.2.0 for CSS variables\r\n$popover-arrow-color:               $popover-bg !default;\r\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\r\n// fusv-enable\r\n\r\n\r\n// Toasts\r\n\r\n// scss-docs-start toast-variables\r\n$toast-max-width:                   350px !default;\r\n$toast-padding-x:                   .75rem !default;\r\n$toast-padding-y:                   .5rem !default;\r\n$toast-font-size:                   .875rem !default;\r\n$toast-color:                       null !default;\r\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\r\n$toast-border-width:                var(--#{$prefix}border-width) !default;\r\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\r\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\r\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\r\n$toast-spacing:                     $container-padding-x !default;\r\n\r\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\r\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\r\n$toast-header-border-color:         $toast-border-color !default;\r\n// scss-docs-end toast-variables\r\n\r\n\r\n// Badges\r\n\r\n// scss-docs-start badge-variables\r\n$badge-font-size:                   .75em !default;\r\n$badge-font-weight:                 $font-weight-bold !default;\r\n$badge-color:                       $white !default;\r\n$badge-padding-y:                   .35em !default;\r\n$badge-padding-x:                   .65em !default;\r\n$badge-border-radius:               $border-radius !default;\r\n// scss-docs-end badge-variables\r\n\r\n\r\n// Modals\r\n\r\n// scss-docs-start modal-variables\r\n$modal-inner-padding:               $spacer !default;\r\n\r\n$modal-footer-margin-between:       .5rem !default;\r\n\r\n$modal-dialog-margin:               .5rem !default;\r\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\r\n\r\n$modal-title-line-height:           $line-height-base !default;\r\n\r\n$modal-content-color:               null !default;\r\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\r\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\r\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\r\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\r\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\r\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\r\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\r\n\r\n$modal-backdrop-bg:                 $black !default;\r\n$modal-backdrop-opacity:            .5 !default;\r\n\r\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\r\n$modal-header-border-width:         $modal-content-border-width !default;\r\n$modal-header-padding-y:            $modal-inner-padding !default;\r\n$modal-header-padding-x:            $modal-inner-padding !default;\r\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n\r\n$modal-footer-bg:                   null !default;\r\n$modal-footer-border-color:         $modal-header-border-color !default;\r\n$modal-footer-border-width:         $modal-header-border-width !default;\r\n\r\n$modal-sm:                          300px !default;\r\n$modal-md:                          500px !default;\r\n$modal-lg:                          800px !default;\r\n$modal-xl:                          1140px !default;\r\n\r\n$modal-fade-transform:              translate(0, -50px) !default;\r\n$modal-show-transform:              none !default;\r\n$modal-transition:                  transform .3s ease-out !default;\r\n$modal-scale-transform:             scale(1.02) !default;\r\n// scss-docs-end modal-variables\r\n\r\n\r\n// Alerts\r\n//\r\n// Define alert colors, border radius, and padding.\r\n\r\n// scss-docs-start alert-variables\r\n$alert-padding-y:               $spacer !default;\r\n$alert-padding-x:               $spacer !default;\r\n$alert-margin-bottom:           1rem !default;\r\n$alert-border-radius:           $border-radius !default;\r\n$alert-link-font-weight:        $font-weight-bold !default;\r\n$alert-border-width:            var(--#{$prefix}border-width) !default;\r\n$alert-bg-scale:                -80% !default;\r\n$alert-border-scale:            -70% !default;\r\n$alert-color-scale:             40% !default;\r\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\r\n// scss-docs-end alert-variables\r\n\r\n// fusv-disable\r\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\r\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\r\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\r\n// fusv-enable\r\n\r\n// Progress bars\r\n\r\n// scss-docs-start progress-variables\r\n$progress-height:                   1rem !default;\r\n$progress-font-size:                $font-size-base * .75 !default;\r\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\r\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\r\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\r\n$progress-bar-color:                $white !default;\r\n$progress-bar-bg:                   $primary !default;\r\n$progress-bar-animation-timing:     1s linear infinite !default;\r\n$progress-bar-transition:           width .6s ease !default;\r\n// scss-docs-end progress-variables\r\n\r\n\r\n// List group\r\n\r\n// scss-docs-start list-group-variables\r\n$list-group-color:                  var(--#{$prefix}body-color) !default;\r\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\r\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\r\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\r\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\r\n\r\n$list-group-item-padding-y:         $spacer * .5 !default;\r\n$list-group-item-padding-x:         $spacer !default;\r\n// fusv-disable\r\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\r\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\r\n// fusv-enable\r\n\r\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\r\n$list-group-active-color:           $component-active-color !default;\r\n$list-group-active-bg:              $component-active-bg !default;\r\n$list-group-active-border-color:    $list-group-active-bg !default;\r\n\r\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\r\n$list-group-disabled-bg:            $list-group-bg !default;\r\n\r\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\r\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\r\n\r\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\r\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\r\n// scss-docs-end list-group-variables\r\n\r\n\r\n// Image thumbnails\r\n\r\n// scss-docs-start thumbnail-variables\r\n$thumbnail-padding:                 .25rem !default;\r\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\r\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\r\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\r\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\r\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\r\n// scss-docs-end thumbnail-variables\r\n\r\n\r\n// Figures\r\n\r\n// scss-docs-start figure-variables\r\n$figure-caption-font-size:          $small-font-size !default;\r\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\r\n// scss-docs-end figure-variables\r\n\r\n\r\n// Breadcrumbs\r\n\r\n// scss-docs-start breadcrumb-variables\r\n$breadcrumb-font-size:              null !default;\r\n$breadcrumb-padding-y:              0 !default;\r\n$breadcrumb-padding-x:              0 !default;\r\n$breadcrumb-item-padding-x:         .5rem !default;\r\n$breadcrumb-margin-bottom:          1rem !default;\r\n$breadcrumb-bg:                     null !default;\r\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\r\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\r\n$breadcrumb-divider:                quote(\"/\") !default;\r\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\r\n$breadcrumb-border-radius:          null !default;\r\n// scss-docs-end breadcrumb-variables\r\n\r\n// Carousel\r\n\r\n// scss-docs-start carousel-variables\r\n$carousel-control-color:             $white !default;\r\n$carousel-control-width:             15% !default;\r\n$carousel-control-opacity:           .5 !default;\r\n$carousel-control-hover-opacity:     .9 !default;\r\n$carousel-control-transition:        opacity .15s ease !default;\r\n\r\n$carousel-indicator-width:           30px !default;\r\n$carousel-indicator-height:          3px !default;\r\n$carousel-indicator-hit-area-height: 10px !default;\r\n$carousel-indicator-spacer:          3px !default;\r\n$carousel-indicator-opacity:         .5 !default;\r\n$carousel-indicator-active-bg:       $white !default;\r\n$carousel-indicator-active-opacity:  1 !default;\r\n$carousel-indicator-transition:      opacity .6s ease !default;\r\n\r\n$carousel-caption-width:             70% !default;\r\n$carousel-caption-color:             $white !default;\r\n$carousel-caption-padding-y:         1.25rem !default;\r\n$carousel-caption-spacer:            1.25rem !default;\r\n\r\n$carousel-control-icon-width:        2rem !default;\r\n\r\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\r\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n\r\n$carousel-transition-duration:       .6s !default;\r\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\r\n// scss-docs-end carousel-variables\r\n\r\n// scss-docs-start carousel-dark-variables\r\n$carousel-dark-indicator-active-bg:  $black !default;\r\n$carousel-dark-caption-color:        $black !default;\r\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\r\n// scss-docs-end carousel-dark-variables\r\n\r\n\r\n// Spinners\r\n\r\n// scss-docs-start spinner-variables\r\n$spinner-width:           2rem !default;\r\n$spinner-height:          $spinner-width !default;\r\n$spinner-vertical-align:  -.125em !default;\r\n$spinner-border-width:    .25em !default;\r\n$spinner-animation-speed: .75s !default;\r\n\r\n$spinner-width-sm:        1rem !default;\r\n$spinner-height-sm:       $spinner-width-sm !default;\r\n$spinner-border-width-sm: .2em !default;\r\n// scss-docs-end spinner-variables\r\n\r\n\r\n// Close\r\n\r\n// scss-docs-start close-variables\r\n$btn-close-width:            1em !default;\r\n$btn-close-height:           $btn-close-width !default;\r\n$btn-close-padding-x:        .25em !default;\r\n$btn-close-padding-y:        $btn-close-padding-x !default;\r\n$btn-close-color:            $black !default;\r\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\r\n$btn-close-focus-shadow:     $focus-ring-box-shadow !default;\r\n$btn-close-opacity:          .5 !default;\r\n$btn-close-hover-opacity:    .75 !default;\r\n$btn-close-focus-opacity:    1 !default;\r\n$btn-close-disabled-opacity: .25 !default;\r\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\r\n// scss-docs-end close-variables\r\n\r\n\r\n// Offcanvas\r\n\r\n// scss-docs-start offcanvas-variables\r\n$offcanvas-padding-y:               $modal-inner-padding !default;\r\n$offcanvas-padding-x:               $modal-inner-padding !default;\r\n$offcanvas-horizontal-width:        400px !default;\r\n$offcanvas-vertical-height:         30vh !default;\r\n$offcanvas-transition-duration:     .3s !default;\r\n$offcanvas-border-color:            $modal-content-border-color !default;\r\n$offcanvas-border-width:            $modal-content-border-width !default;\r\n$offcanvas-title-line-height:       $modal-title-line-height !default;\r\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\r\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\r\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\r\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\r\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\r\n// scss-docs-end offcanvas-variables\r\n\r\n// Code\r\n\r\n$code-font-size:                    $small-font-size !default;\r\n$code-color:                        $pink !default;\r\n\r\n$kbd-padding-y:                     .1875rem !default;\r\n$kbd-padding-x:                     .375rem !default;\r\n$kbd-font-size:                     $code-font-size !default;\r\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\r\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\r\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\r\n\r\n$pre-color:                         null !default;\r\n\r\n\r\n// Calendar & Date & Time Pickers\r\n\r\n// Calendar\r\n// scss-docs-start calendar-variables\r\n$calendar-table-margin:                     .5rem !default;\r\n$calendar-table-cell-size:                  2.75rem !default;\r\n\r\n$calendar-nav-padding:                      .5rem !default;\r\n$calendar-nav-border-width:                 1px !default;\r\n$calendar-nav-border-color:                 $border-color !default;\r\n$calendar-nav-date-color:                   $body-color !default;\r\n$calendar-nav-date-hover-color:             $primary !default;\r\n$calendar-nav-icon-width:                   1rem !default;\r\n$calendar-nav-icon-height:                  1rem !default;\r\n\r\n$calendar-nav-icon-double-next-color:       $gray-600 !default;\r\n$calendar-nav-icon-double-next:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-next-color}' points='95.314 447.313 72.686 424.687 245.373 252 72.686 79.313 95.314 56.687 290.627 252 95.314 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-next-color}' points='255.314 447.313 232.686 424.687 405.373 252 232.686 79.313 255.314 56.687 450.627 252 255.314 447.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-double-next-hover-color: $body-color !default;\r\n$calendar-nav-icon-double-next-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-next-hover-color}' points='95.314 447.313 72.686 424.687 245.373 252 72.686 79.313 95.314 56.687 290.627 252 95.314 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-next-hover-color}' points='255.314 447.313 232.686 424.687 405.373 252 232.686 79.313 255.314 56.687 450.627 252 255.314 447.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-double-prev-color:       $gray-600 !default;\r\n$calendar-nav-icon-double-prev:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-prev-color}' points='416.686 447.313 221.373 252 416.686 56.687 439.314 79.313 266.627 252 439.314 424.687 416.686 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-prev-color}' points='256.686 447.313 61.373 252 256.686 56.687 279.314 79.313 106.627 252 279.314 424.687 256.686 447.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-double-prev-hover-color: $body-color !default;\r\n$calendar-nav-icon-double-prev-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-double-prev-hover-color}' points='416.686 447.313 221.373 252 416.686 56.687 439.314 79.313 266.627 252 439.314 424.687 416.686 447.313'></polygon><polygon fill='#{$calendar-nav-icon-double-prev-hover-color}' points='256.686 447.313 61.373 252 256.686 56.687 279.314 79.313 106.627 252 279.314 424.687 256.686 447.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-next-color:              $gray-600 !default;\r\n$calendar-nav-icon-next:                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-next-color}' points='179.313 451.313 156.687 428.687 329.372 256 156.687 83.313 179.313 60.687 374.627 256 179.313 451.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-next-hover-color:        $body-color !default;\r\n$calendar-nav-icon-next-hover:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-next-hover-color}' points='179.313 451.313 156.687 428.687 329.372 256 156.687 83.313 179.313 60.687 374.627 256 179.313 451.313'></polygon></svg>\") !default;\r\n\r\n$calendar-nav-icon-prev-color:              $gray-600 !default;\r\n$calendar-nav-icon-prev:                    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-prev-color}' points='324.687 451.313 129.373 256 324.687 60.687 347.313 83.313 174.628 256 347.313 428.687 324.687 451.313'></polygon></svg>\") !default;\r\n$calendar-nav-icon-prev-hover-color:        $body-color !default;\r\n$calendar-nav-icon-prev-hover:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$calendar-nav-icon-prev-hover-color}' points='324.687 451.313 129.373 256 324.687 60.687 347.313 83.313 174.628 256 347.313 428.687 324.687 451.313'></polygon></svg>\") !default;\r\n\r\n$calendar-cell-header-inner-color:          $text-medium-emphasis !default;\r\n\r\n$calendar-cell-hover-bg:                    $gray-200 !default;\r\n$calendar-cell-disabled-color:              $text-disabled !default;\r\n\r\n$calendar-cell-selected-color:              $white !default;\r\n$calendar-cell-selected-bg:                 $success !default;\r\n\r\n$calendar-cell-range-bg:                    rgba($success, .125) !default;\r\n$calendar-cell-range-hover-bg:              rgba($success, .25) !default;\r\n$calendar-cell-range-hover-border-color:    $success !default;\r\n\r\n$calendar-cell-today-color:                 $danger !default;\r\n// scss-docs-end calendar-variables\r\n\r\n// Picker\r\n$picker-footer-border-width:  1px !default;\r\n$picker-footer-border-color:  $border-color !default;\r\n$picker-footer-padding:       .5rem !default;\r\n\r\n// Date Picker\r\n// scss-docs-start date-picker-variables\r\n$date-picker-default-icon-color:       $gray-600 !default;\r\n$date-picker-default-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$date-picker-default-icon-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$date-picker-default-icon-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$date-picker-default-icon-color}'></rect></svg>\") !default;\r\n$date-picker-invalid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$form-feedback-invalid-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$form-feedback-invalid-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$form-feedback-invalid-color}'></rect></svg>\") !default;\r\n$date-picker-valid-icon:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><path fill='#{$form-feedback-valid-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path><rect width='32' height='32' x='112' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='224' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='112' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='296' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='112' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='200' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='280' y='368' fill='#{$form-feedback-valid-color}'></rect><rect width='32' height='32' x='368' y='368' fill='#{$form-feedback-valid-color}'></rect></svg>\") !default;\r\n\r\n$date-picker-cleaner-icon-color:       $gray-600 !default;\r\n$date-picker-cleaner-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-cleaner-icon-color}' points='306.912 214.461 256 265.373 205.088 214.461 182.461 237.088 233.373 288 182.461 338.912 205.088 361.539 256 310.627 306.912 361.539 329.539 338.912 278.627 288 329.539 237.088 306.912 214.461'></polygon><path fill='#{$date-picker-cleaner-icon-color}' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path></svg>\") !default;\r\n$date-picker-cleaner-icon-hover-color: $body-color !default;\r\n$date-picker-cleaner-icon-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-cleaner-icon-hover-color}' points='306.912 214.461 256 265.373 205.088 214.461 182.461 237.088 233.373 288 182.461 338.912 205.088 361.539 256 310.627 306.912 361.539 329.539 338.912 278.627 288 329.539 237.088 306.912 214.461'></polygon><path fill='#{$date-picker-cleaner-icon-hover-color})' d='M472,96H384V40H352V96H160V40H128V96H40a24.028,24.028,0,0,0-24,24V456a24.028,24.028,0,0,0,24,24H472a24.028,24.028,0,0,0,24-24V120A24.028,24.028,0,0,0,472,96Zm-8,352H48V128h80v40h32V128H352v40h32V128h80Z'></path></svg>\") !default;\r\n\r\n$date-picker-separator-icon-color:     $gray-600 !default;\r\n$date-picker-separator-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-separator-icon-color}' points='359.873 121.377 337.246 144.004 433.243 240.001 16 240.001 16 240.002 16 272.001 16 272.002 433.24 272.002 337.246 367.996 359.873 390.623 494.498 256 359.873 121.377'></polygon></svg>\") !default;\r\n$date-picker-separator-icon-rtl:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$date-picker-separator-icon-color}' points='497.333 239.999 80.092 239.999 176.087 144.004 153.46 121.377 18.837 256 153.46 390.623 176.087 367.996 80.09 271.999 497.333 271.999 497.333 239.999'></polygon></svg>\") !default;\r\n\r\n$date-picker-ranges-width:             10rem !default;\r\n$date-picker-ranges-padding:           $spacer * .5 !default;\r\n$date-picker-ranges-border-width:      1px !default;\r\n$date-picker-ranges-border-color:      $border-color !default;\r\n\r\n$date-picker-timepicker-width:         (7 * $calendar-table-cell-size) + (2 * $calendar-table-margin) !default;\r\n$date-picker-timepicker-border-width:  1px !default;\r\n$date-picker-timepicker-border-color:  $border-color !default;\r\n// scss-docs-end date-picker-variables\r\n\r\n// Time Picker\r\n// scss-docs-start time-picker-variables\r\n$time-picker-default-icon-color:       $gray-600 !default;\r\n$time-picker-default-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-default-icon-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$time-picker-default-icon-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-invalid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$form-feedback-invalid-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$form-feedback-invalid-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-valid-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$form-feedback-valid-color}' points='271.514 95.5 239.514 95.5 239.514 273.611 355.127 328.559 368.864 299.657 271.514 253.389 271.514 95.5' class='ci-primary'></polygon><path fill='#{$form-feedback-valid-color}' d='M256,16C123.452,16,16,123.452,16,256S123.452,496,256,496,496,388.548,496,256,388.548,16,256,16Zm0,448C141.125,464,48,370.875,48,256S141.125,48,256,48s208,93.125,208,208S370.875,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n\r\n$time-picker-cleaner-icon-color:       $gray-600 !default;\r\n$time-picker-cleaner-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-cleaner-icon-color}' points='348.071 141.302 260.308 229.065 172.545 141.302 149.917 163.929 237.681 251.692 149.917 339.456 172.545 362.083 260.308 274.32 348.071 362.083 370.699 339.456 282.935 251.692 370.699 163.929 348.071 141.302' class='ci-primary'></polygon><path fill='#{$time-picker-cleaner-icon-color}' d='M425.706,86.294A240,240,0,0,0,86.294,425.706,240,240,0,0,0,425.706,86.294ZM256,464C141.309,464,48,370.691,48,256S141.309,48,256,48s208,93.309,208,208S370.691,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n$time-picker-cleaner-icon-hover-color: $body-color !default;\r\n$time-picker-cleaner-icon-hover:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' role='img'><polygon fill='#{$time-picker-cleaner-icon-hover-color}' points='348.071 141.302 260.308 229.065 172.545 141.302 149.917 163.929 237.681 251.692 149.917 339.456 172.545 362.083 260.308 274.32 348.071 362.083 370.699 339.456 282.935 251.692 370.699 163.929 348.071 141.302' class='ci-primary'></polygon><path fill='#{$time-picker-cleaner-icon-hover-color}' d='M425.706,86.294A240,240,0,0,0,86.294,425.706,240,240,0,0,0,425.706,86.294ZM256,464C141.309,464,48,370.691,48,256S141.309,48,256,48s208,93.309,208,208S370.691,464,256,464Z' class='ci-primary'></path></svg>\") !default;\r\n\r\n$time-picker-body-padding:             $spacer * .5 !default;\r\n$time-picker-roll-col-border-width:    1px !default;\r\n$time-picker-roll-col-border-color:    $border-color !default;\r\n// scss-docs-end time-picker-variables\r\n", "// Breakpoint viewport sizes and media queries.\r\n//\r\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\r\n//\r\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\r\n//\r\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\r\n\r\n// Name of the next breakpoint, or null for the last breakpoint.\r\n//\r\n//    >> breakpoint-next(sm)\r\n//    md\r\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    md\r\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\r\n//    md\r\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\r\n  $n: index($breakpoint-names, $name);\r\n  @if not $n {\r\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\r\n  }\r\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\r\n}\r\n\r\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\r\n//\r\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    576px\r\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\r\n  $min: map-get($breakpoints, $name);\r\n  @return if($min != 0, $min, null);\r\n}\r\n\r\n// Maximum breakpoint width.\r\n// The maximum value is reduced by 0.02px to work around the limitations of\r\n// `min-` and `max-` prefixes and viewports with fractional widths.\r\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\r\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\r\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\r\n//\r\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    767.98px\r\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\r\n  $max: map-get($breakpoints, $name);\r\n  @return if($max and $max > 0, $max - .02, null);\r\n}\r\n\r\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\r\n// Useful for making responsive utilities.\r\n//\r\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"\"  (Returns a blank string)\r\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"-sm\"\r\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\r\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\r\n}\r\n\r\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\r\n// Makes the @content apply to the given breakpoint and wider.\r\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($name, $breakpoints);\r\n  @if $min {\r\n    @media (min-width: $min) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\r\n// Makes the @content apply to the given breakpoint and narrower.\r\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\r\n  $max: breakpoint-max($name, $breakpoints);\r\n  @if $max {\r\n    @media (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media that spans multiple breakpoint widths.\r\n// Makes the @content apply between the min and max breakpoints\r\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($lower, $breakpoints);\r\n  $max: breakpoint-max($upper, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($lower, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($upper, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Media between the breakpoint's minimum and maximum widths.\r\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\r\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\r\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\r\n  $min:  breakpoint-min($name, $breakpoints);\r\n  $next: breakpoint-next($name, $breakpoints);\r\n  $max:  breakpoint-max($next, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($name, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($next, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n", "// scss-docs-start stacks\r\n.hstack {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  align-self: stretch;\r\n}\r\n\r\n.vstack {\r\n  display: flex;\r\n  flex: 1 1 auto;\r\n  flex-direction: column;\r\n  align-self: stretch;\r\n}\r\n// scss-docs-end stacks\r\n", "//\r\n// Visually hidden\r\n//\r\n\r\n.visually-hidden,\r\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\r\n  @include visually-hidden();\r\n}\r\n", "// stylelint-disable declaration-no-important\r\n\r\n// Hide content visually while keeping it accessible to assistive technologies\r\n//\r\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\r\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\r\n\r\n@mixin visually-hidden() {\r\n  width: 1px !important;\r\n  height: 1px !important;\r\n  padding: 0 !important;\r\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\r\n  overflow: hidden !important;\r\n  clip: rect(0, 0, 0, 0) !important;\r\n  white-space: nowrap !important;\r\n  border: 0 !important;\r\n\r\n  // Fix for positioned table caption that could become anonymous cells\r\n  &:not(caption) {\r\n    position: absolute !important;\r\n  }\r\n}\r\n\r\n// Use to only display content when it's focused, or one of its child elements is focused\r\n// (i.e. when focus is within the element/container that the class was applied to)\r\n//\r\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\r\n\r\n@mixin visually-hidden-focusable() {\r\n  &:not(:focus):not(:focus-within) {\r\n    @include visually-hidden();\r\n  }\r\n}\r\n", "//\r\n// Stretched link\r\n//\r\n\r\n.stretched-link {\r\n  &::#{$stretched-link-pseudo-element} {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: $stretched-link-z-index;\r\n    content: \"\";\r\n  }\r\n}\r\n", "//\r\n// Text truncation\r\n//\r\n\r\n.text-truncate {\r\n  @include text-truncate();\r\n}\r\n", "// Text truncate\r\n// Requires inline-block or block for proper styling\r\n\r\n@mixin text-truncate() {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n", ".vr {\r\n  display: inline-block;\r\n  align-self: stretch;\r\n  width: 1px;\r\n  min-height: 1em;\r\n  background-color: currentcolor;\r\n  opacity: $hr-opacity;\r\n}\r\n", "// Utility generator\r\n// Used to generate utilities & print utilities\r\n@mixin generate-utility($utility, $infix: \"\", $is-rfs-media-query: false) {\r\n  $values: map-get($utility, values);\r\n\r\n  // If the values are a list or string, convert it into a map\r\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\r\n    $values: zip($values, $values);\r\n  }\r\n\r\n  @each $key, $value in $values {\r\n    $properties: map-get($utility, property);\r\n\r\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\r\n    @if type-of($properties) == \"string\" {\r\n      $properties: append((), $properties);\r\n    }\r\n\r\n    // Use custom class if present\r\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\r\n    $property-class: if($property-class == null, \"\", $property-class);\r\n\r\n    // Use custom CSS variable name if present, otherwise default to `class`\r\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\r\n\r\n    // State params to generate pseudo-classes\r\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\r\n\r\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\r\n\r\n    // Don't prefix if value key is null (e.g. with shadow class)\r\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\r\n\r\n    @if map-get($utility, rfs) {\r\n      // Inside the media query\r\n      @if $is-rfs-media-query {\r\n        $val: rfs-value($value);\r\n\r\n        // Do not render anything if fluid and non fluid values are the same\r\n        $value: if($val == rfs-fluid-value($value), null, $val);\r\n      }\r\n      @else {\r\n        $value: rfs-fluid-value($value);\r\n      }\r\n    }\r\n\r\n    $is-css-var: map-get($utility, css-var);\r\n    $is-local-vars: map-get($utility, local-vars);\r\n    $is-rtl: map-get($utility, rtl);\r\n\r\n    @if $value != null {\r\n      @if $is-rtl == false {\r\n        /* rtl:begin:remove */\r\n      }\r\n\r\n      @if $is-css-var {\r\n        .#{$property-class + $infix + $property-class-modifier} {\r\n          --#{$prefix}#{$css-variable-name}: #{$value};\r\n        }\r\n\r\n        @each $pseudo in $state {\r\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\r\n            --#{$prefix}#{$css-variable-name}: #{$value};\r\n          }\r\n        }\r\n      } @else {\r\n        .#{$property-class + $infix + $property-class-modifier} {\r\n          @each $property in $properties {\r\n            @if $is-local-vars {\r\n              @each $local-var, $variable in $is-local-vars {\r\n                --#{$prefix}#{$local-var}: #{$variable};\r\n              }\r\n            }\r\n            #{$property}: $value if($enable-important-utilities, !important, null);\r\n          }\r\n        }\r\n\r\n        @each $pseudo in $state {\r\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\r\n            @each $property in $properties {\r\n              @if $is-local-vars {\r\n                @each $local-var, $variable in $is-local-vars {\r\n                  --#{$prefix}#{$local-var}: #{$variable};\r\n                }\r\n              }\r\n              #{$property}: $value if($enable-important-utilities, !important, null);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      @if $is-rtl == false {\r\n        /* rtl:end:remove */\r\n      }\r\n    }\r\n  }\r\n}\r\n", "// Loop over each breakpoint\r\n@each $breakpoint in map-keys($grid-breakpoints) {\r\n\r\n  // Generate media query if needed\r\n  @include media-breakpoint-up($breakpoint) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    // Loop over each utility property\r\n    @each $key, $utility in $utilities {\r\n      // The utility can be disabled with `false`, thus check if the utility is a map first\r\n      // Only proceed if responsive media queries are enabled or if it's the base media query\r\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\r\n        @include generate-utility($utility, $infix);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// RFS rescaling\r\n@media (min-width: $rfs-mq-value) {\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\r\n      // Loop over each utility property\r\n      @each $key, $utility in $utilities {\r\n        // The utility can be disabled with `false`, thus check if the utility is a map first\r\n        // Only proceed if responsive media queries are enabled or if it's the base media query\r\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\r\n          @include generate-utility($utility, $infix, true);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Print utilities\r\n@media print {\r\n  @each $key, $utility in $utilities {\r\n    // The utility can be disabled with `false`, thus check if the utility is a map first\r\n    // Then check if the utility needs print styles\r\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\r\n      @include generate-utility($utility, \"-print\");\r\n    }\r\n  }\r\n}\r\n"]}