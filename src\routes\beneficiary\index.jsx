import { <PERSON><PERSON>, Col, DEFAULT_LIMIT, DEFAULT_PAGE, Row, useTranslate } from "@buymed/solidjs-component/components";
import { toQueryObject } from "@buymed/solidjs-component/utils";
import { ErrorBoundary, createResource } from "solid-js";
import { ErrorMessage, createRouteData, useRouteData, useSearchParams } from "solid-start";
import AuthContent, { PRIVILEGE } from "~/components/AuthContent";
import AppLayout from "~/components/Layout/AppLayout";
import { PageTabs } from "~/components/PageTabs";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { BeneficiaryTable } from "~/containers/BeneficiaryTable";
import { ContractFormFilter } from "~/containers/ContractFormFilter";
import { ContractTable } from "~/containers/ContractTable";
import { getBeneficiaryList } from "~/services/tender/beneficiary.client";
import { getBidList } from "~/services/tender/bid.client";
import { getContractList } from "~/services/tender/contract.client";
import { CONTRACT_STATUS } from "~/services/tender/contract.model";
import AddIcon from "~icons/mdi/plus";

export function routeData({ location }) {
    return createRouteData(
		async ([query]) => {
			const page = +query.page || DEFAULT_PAGE;
			const limit = +query.limit || DEFAULT_LIMIT;
			const offset = (page - 1) * limit;
			const q = query?.q ? JSON.parse(query.q) : {};

			// Prepare query data

			// TODO: Fix <DateRangePicker> to allow customizing name, so we can use createdFrom, not createdTimeStartDate
			if (q.createdTimeStartDate) {
				q.createdFrom = q.createdTimeStartDate;
			}
			if (q.createdTimeEndDate) {
				q.createdTo = q.createdTimeEndDate;
			}

			switch (query.tab) {
                // TODO
			}

			const resBeneficiary = await getBeneficiaryList({
				q,
				offset,
				limit,
				option: {
					total: true,
				},
			});

			
			return {
				data: resBeneficiary.data,
				total: resBeneficiary.total,
			};
		},
		{
			key: () => {
				return [toQueryObject(location.query)];
			},
		}
	);
}

export default () => {
	return (
		<AppLayout namespaces={["beneficiary"]} pageTitle="beneficiary:beneficiary_list" breadcrumbs={[BREADCRUMB.BENEFICIARY]}>
			<PageContainer />
		</AppLayout>
	);
};


function PageContainer() {
	const pageData = useRouteData();
	const [searchParams] = useSearchParams();
	const { t } = useTranslate();

	const [tabs] = createResource(
		// Fetch the count for tabs
		// We only need re-fetch when q change (i.e. submit filter), when tab change
		// no need to re-fetch
		() => toQueryObject(searchParams).q || "{}",
		async (qString) => {
			const q = JSON.parse(qString);
			
			if (q.createdTimeStartDate) {
				q.createdFrom = q.createdTimeStartDate;
			}
			if (q.createdTimeEndDate) {
				q.createdTo = q.createdTimeEndDate;
			}

			const totalRes = await Promise.all([
				getBeneficiaryList({
					q,
					limit: 1,
					option: { total: true },
				}),
			]);
			const totals = totalRes.map((res) => res.total || 0);

			return [
				t`beneficiary:status.all` + ` (${totals[0]})`,
			];
		}
	);

	return (
		<Row class="gap-3">
			<Col xs={12}>
				<div class="d-flex justify-content-start">
					<h1 class="page-title">{t`beneficiary:beneficiary_list`}</h1>
				</div>
				<div class="d-flex justify-content-end">
				</div>
			</Col>
			{/* <Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<ContractFormFilter />
				</ErrorBoundary>
			</Col> */}
			<Col xs={12}>
				<PageTabs tabs={tabs()} />
			</Col>
			<Col xs={12}>
				<ErrorBoundary fallback={ErrorMessage}>
					<BeneficiaryTable 
						beneficiaries={pageData()?.data} 
						total={pageData()?.total}
					/>
				</ErrorBoundary>
			</Col>
		</Row>
	);
}