import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { BenaficiaryForm } from "~/containers/Benaficiary/form";
import { FormProvider } from "~/contexts/FormContext";
import { postBeneficiary } from "~/services/tender/beneficiary.client";
import { BENEFICIARY_STATUS } from "~/services/tender/beneficiary.model";

export default () => {
	return (
		<AppLayout
			pageTitle="beneficiary:add_new"
			namespaces={["beneficiary"]}
			breadcrumbs={[BREADCRUMB.BENEFICIARY, BREADCRUMB.ADD_BENEFICIARY]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Thêm mới khách hàng</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddContract />
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddContract() {
	const { t } = useTranslate();
	const toast = useToast();
	const navigate = useNavigate();

	const hookForm = createForm({
		initialValues: {
			name: "",
			address: "",
			phoneNumber: "",
			taxCode: "",
			status: BENEFICIARY_STATUS.ACTIVE,
			email: "",
		},
		validate: (values) => {
			const err = {};
			const emptyMessage = "Không được bỏ trống";
			if (!values.name) {
				err.name = emptyMessage;
			}
			if (!values.address) {
				err.address = emptyMessage;
			}
			if (!values.phoneNumber) {
				err.phoneNumber = emptyMessage;
			}
			if (!values.taxCode) {
				err.taxCode = emptyMessage;
			}
			return err;
		},
		onSubmit: async (values) => {
			const res = await postBeneficiary({ ...values });
			if (res.status !== API_STATUS.OK) {
				console.error("[Error] Create beneficiary:", res);
				toast.error(t("common:notify.action_fail", { error: res.message }));
			} else {
				toast.success(t`common:notify.create_success`);
				navigate("/beneficiary", { replace: false });
			}

			return;
		},
	});

	return (
		<FormProvider form={hookForm}>
			<BenaficiaryForm />
		</FormProvider>
	);
}
