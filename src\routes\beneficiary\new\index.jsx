import {
	Col,
	ErrorMessage,
	Row,
	useToast,
	useTranslate,
} from "@buymed/solidjs-component/components";
import { API_STATUS } from "@buymed/solidjs-component/utils";
import { createForm } from "@felte/solid";
import { ErrorBoundary } from "solid-js";
import { useNavigate } from "solid-start";
import AppLayout from "~/components/Layout/AppLayout";
import { BREADCRUMB } from "~/constants/breadcrumb";
import { BenaficiaryForm } from "~/containers/Benaficiary/form";
import { FormProvider } from "~/contexts/FormContext";
import { postBeneficiary } from "~/services/tender/beneficiary.client";
import { BENEFICIARY_STATUS } from "~/services/tender/beneficiary.model";
import { ERROR_MESSAGE } from "~/utils/error-code";

export default () => {
	return (
		<AppLayout
			pageTitle="beneficiary:add_new"
			namespaces={["beneficiary"]}
			breadcrumbs={[BREADCRUMB.BENEFICIARY, BREADCRUMB.ADD_BENEFICIARY]}
		>
			<PageContainer />
		</AppLayout>
	);
};

function PageContainer() {
	return (
		<div class="mt-2">
			<Row class="gap-3">
				<Col xs={12}>
					<h1 class="page-title">Thêm mới khách hàng</h1>
				</Col>
				<Col xs={12}>
					<ErrorBoundary fallback={ErrorMessage}>
						<AddContract />
					</ErrorBoundary>
				</Col>
			</Row>
		</div>
	);
}

function AddContract() {
	const { t } = useTranslate();
	const toast = useToast();
	const navigate = useNavigate();

	const hookForm = createForm({
		initialValues: {
			name: "",
			address: "",
			phoneNumber: "",
			taxCode: "",
			status: BENEFICIARY_STATUS.ACTIVE,
			email: "",
		},
		validate: (values) => {
			const err = {};
			const emptyMessage = "Không được bỏ trống";
			const numberMessage = "Chỉ được nhập số";
			const phoneMessage = "Số điện thoại không hợp lệ";

			// Regex: chỉ chứa số (ít nhất 1 số)
			const numberRegex = /^[0-9]+$/;

			// Regex: số điện thoại VN (10 số, bắt đầu 0)
			const phoneRegex = /^0\d{9}$/;

			if (!values.name) {
				err.name = emptyMessage;
			}

			if (!values.address) {
				err.address = emptyMessage;
			}

			if (!values.phoneNumber) {
				err.phoneNumber = emptyMessage;
			} else if (!phoneRegex.test(values.phoneNumber)) {
				err.phoneNumber = phoneMessage;
			}

			if (!values.taxCode) {
				err.taxCode = emptyMessage;
			} else if (!numberRegex.test(values.taxCode)) {
				err.taxCode = numberMessage;
			}

			return err;
		},
		onSubmit: async (values) => {
			console.log(values, "values");
			try {
				const data = JSON.parse(JSON.stringify(values));
				const res = await postBeneficiary({ ...data });
				if (res.status !== API_STATUS.OK) {
					const errorCode = res?.errorCode;
					const message = ERROR_MESSAGE?.[errorCode] || "";
					return toast.error(
						message
							? message
							: t("common:notify.action_fail", { error: "Lỗi không xác định" })
					);
				}
				toast.success(t`common:notify.create_success`);
				// navigate("/beneficiary/new");
			} catch (error) {
				console.error("[Error] Create beneficiary:", error);
				toast.error(t("common:notify.action_fail", { error: error.message }));
			}
		},
	});

	return (
		<FormProvider form={hookForm}>
			<BenaficiaryForm />
		</FormProvider>
	);
}
