import { FormAutocomplete } from "@buymed/solidjs-component/components";
import { debounce } from "@buymed/solidjs-component/utils";
import { createResource, createSignal, splitProps } from "solid-js";

export const AutoCompleteTender = (props) => {
	const [local, other] = splitProps(props, ["defaultValue", "name", "initialOptions"]);
	const [search, setSearch] = createSignal(local.defaultValue || "");
	const [lastOption, setLastOption] = createSignal(local.initialOptions?.[0] || null);
	const renderOption =
		typeof props.renderOption === "function"
			? props.renderOption
			: (props, { data }) => (
					<li {...props}>
						[{data.id}]&nbsp;-&nbsp;
						<b>{props.fieldValue ? data[props.fieldValue] : data.name}</b>
					</li>
				);

	const [fetchData] = createResource(
		search,
		async (search) => {
			if (!search) {
				return [];
			}
			const _res = await props.handleFetchData({ search });

			if (!_res.length) {
				return [];
			}
			const options = _res.map((item) => {
				const id = props.fieldKey ? item[props.fieldKey] : item.id;
				const name = props.fieldValue ? item[props.fieldValue] : item.name;
				const label = props.isRenderID ? `${id} - ${name}` : name;
				return {
					value: id.toString(),
					label,
					data: { id: item[props.fieldKey], ...item },
				};
			});
			if (!search && lastOption()) {
				var exist = false;
				for (var i = 0; i < options.length; i++) {
					if (options[i].value === lastOption().value) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					options.push(lastOption());
				}
			}
			return options;
		},
		{ initialValue: lastOption() ? [lastOption()] : [] }
	);

	function onInputChange(e) {
		setSearch(e.target.value);
	}
	const debouncedOnInputChange = debounce(onInputChange, 500);

	return (
		<FormAutocomplete
			name={local.name}
			options={fetchData()}
			label={props.label || ""}
			placeholder={props.placeholder || ""}
			onInputChange={debouncedOnInputChange}
			isLoading={fetchData.loading}
			renderOption={renderOption}
			onChange={(e) => {
				setLastOption(e);
			}}
			{...other}
		/>
	);
};
