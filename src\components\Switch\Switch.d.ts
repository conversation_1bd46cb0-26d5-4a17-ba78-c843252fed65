import { JSX } from "solid-js";

export interface SwitchProps {
    /** Form field name */
    name?: string;
    
    /** Element ID */
    id?: string;
    
    /** Switch label text */
    label?: string;
    
    /** Optional description text */
    description?: string;
    
    /** Controlled checked state */
    checked?: boolean;
    
    /** De<PERSON>ult checked state for uncontrolled component */
    defaultChecked?: boolean;
    
    /** Disabled state */
    disabled?: boolean;
    
    /** Layout direction */
    direction?: "row" | "column";
    
    /** Label position relative to switch */
    labelPosition?: "left" | "right" | "top" | "bottom";
    
    /** Switch size */
    size?: "sm" | "md" | "lg";
    
    /** Switch color theme */
    color?: "primary" | "success" | "warning" | "danger" | "info";
    
    /** Change handler */
    onChange?: (event: Event, checked: boolean) => void;
    
    /** Focus handler */
    onFocus?: (event: FocusEvent) => void;
    
    /** Blur handler */
    onBlur?: (event: FocusEvent) => void;
    
    /** Additional CSS classes */
    class?: string;
    
    /** Required field indicator */
    required?: boolean;
    
    /** Error message */
    feedbackInvalid?: string;
    
    /** Invalid state */
    invalid?: boolean;
    
    /** Additional HTML attributes */
    [key: string]: any;
}

export declare function Switch(props: SwitchProps): JSX.Element;

export default Switch;
