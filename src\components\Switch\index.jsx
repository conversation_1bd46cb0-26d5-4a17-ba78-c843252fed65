import { createSignal, createEffect, splitProps } from "solid-js";
import styles from "./Switch.module.scss";
/**
 * @typedef {Object} SwitchProps
 * @property {string=} name
 * @property {string=} id
 * @property {string=} label
 * @property {string=} description
 * @property {boolean=} checked
 * @property {boolean=} defaultChecked
 * @property {boolean=} disabled
 * @property {"row" | "column"=} direction
 * @property {"left" | "right" | "top" | "bottom"=} labelPosition
 * @property {"sm" | "md" | "lg"=} size
 * @property {string=} color
 * @property {(event: Event, checked: boolean)=>void=} onChange
 * @property {Function=} onFocus
 * @property {Function=} onBlur
 * @property {string=} class
 * @property {boolean=} required
 * @property {string=} feedbackInvalid
 * @property {boolean=} invalid
 */

/**
 * @param {SwitchProps} props
 */
export function Switch(props) {
	const [local, others] = splitProps(props, [
		"name",
		"id",
		"label",
		"description",
		"checked",
		"defaultChecked",
		"disabled",
		"direction",
		"labelPosition",
		"size",
		"color",
		"onChange",
		"onFocus",
		"onBlur",
		"class",
		"required",
		"feedbackInvalid",
		"invalid",
	]);

	const [isChecked, setIsChecked] = createSignal(local.defaultChecked || false);
	const [isFocused, setIsFocused] = createSignal(false);

	// Handle controlled vs uncontrolled
	createEffect(() => {
		if (local.checked !== undefined) {
			setIsChecked(local.checked);
		}
	});

	const direction = () => local.direction || "row";
	const labelPosition = () => local.labelPosition || (direction() === "row" ? "right" : "bottom");
	const size = () => local.size || "md";
	const color = () => local.color || "success";
	const switchId = () => local.id || `switch-${Math.random().toString(36).substring(2, 9)}`;

	function handleChange(event) {
		if (local.disabled) return;

		const newValue = event?.target?.checked ?? !isChecked();
		setIsChecked(newValue);

		// Call onChange with both event and value for flexibility
		if (typeof local.onChange === "function") {
			local.onChange(event, newValue);
		}
	}

	function handleFocus(event) {
		setIsFocused(true);
		if (typeof local.onFocus === "function") {
			local.onFocus(event);
		}
	}

	function handleBlur(event) {
		setIsFocused(false);
		if (typeof local.onBlur === "function") {
			local.onBlur(event);
		}
	}

	const containerClasses = () =>
		[
			styles.switchContainer,
			styles[`direction-${direction()}`],
			styles[`label-${labelPosition()}`],
			styles[`size-${size()}`],
			local.disabled && styles.disabled,
			local.invalid && styles.invalid,
			isFocused() && styles.focused,
			local.class,
		]
			.filter(Boolean)
			.join(" ");

	const switchClasses = () =>
		[
			styles.switch,
			styles[`color-${color()}`],
			isChecked() && styles.checked,
			local.disabled && styles.disabled,
			isFocused() && styles.focused,
		]
			.filter(Boolean)
			.join(" ");

	const labelContent = () => (
		<div class={styles.labelContent}>
			<label for={switchId()} class={styles.label}>
				{local.label}
				{local.required && <span class={styles.required}>*</span>}
			</label>
			{local.description && <div class={styles.description}>{local.description}</div>}
		</div>
	);

	return (
		<div class={containerClasses()}>
			<div class={styles.switchWrapper}>
				{(labelPosition() === "left" || labelPosition() === "top") && labelContent()}

				<div class={switchClasses()}>
					<input
						type="checkbox"
						id={switchId()}
						name={local.name}
						checked={isChecked()}
						disabled={local.disabled}
						onInput={handleChange}
						onFocus={handleFocus}
						onBlur={handleBlur}
						class={styles.input}
						{...others}
					/>
					<span class={styles.slider}>
						<span class={styles.thumb} />
					</span>
				</div>

				{(labelPosition() === "right" || labelPosition() === "bottom") && labelContent()}
			</div>

			{/* Error message always appears below the switch */}
			{local.invalid && local.feedbackInvalid && (
				<div class={styles.feedback}>{local.feedbackInvalid}</div>
			)}
		</div>
	);
}

export default Switch;
