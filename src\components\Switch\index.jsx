import { createSignal, createEffect, splitProps } from "solid-js";
import styles from "./Switch.module.scss";

export function Switch(props) {
	const [local, others] = splitProps(props, [
		"name",
		"id",
		"label",
		"description",
		"checked",
		"defaultChecked",
		"disabled",
		"direction",
		"labelPosition",
		"size",
		"color",
		"onChange",
		"onFocus",
		"onBlur",
		"class",
		"required",
		"feedbackInvalid",
		"invalid",
	]);

	const [isChecked, setIsChecked] = createSignal(local.defaultChecked || false);
	const [isFocused, setIsFocused] = createSignal(false);

	// Handle controlled vs uncontrolled
	createEffect(() => {
		if (local.checked !== undefined) {
			setIsChecked(local.checked);
		}
	});

	const direction = () => local.direction || "row";
	const labelPosition = () => local.labelPosition || (direction() === "row" ? "right" : "bottom");
	const size = () => local.size || "md";
	const color = () => local.color || "primary";
	const switchId = () => local.id || `switch-${Math.random().toString(36).substr(2, 9)}`;

	function handleChange(event) {
		console.log(event.target, "event");
		const newValue = event.target.checked;
		setIsChecked(newValue);
		local.onChange?.(event, newValue);
	}

	function handleFocus(event) {
		setIsFocused(true);
		local.onFocus?.(event);
	}

	function handleBlur(event) {
		setIsFocused(false);
		local.onBlur?.(event);
	}

	const containerClasses = () =>
		[
			styles.switchContainer,
			styles[`direction-${direction()}`],
			styles[`label-${labelPosition()}`],
			styles[`size-${size()}`],
			local.disabled && styles.disabled,
			local.invalid && styles.invalid,
			isFocused() && styles.focused,
			local.class,
		]
			.filter(Boolean)
			.join(" ");

	const switchClasses = () =>
		[
			styles.switch,
			styles[`color-${color()}`],
			isChecked() && styles.checked,
			local.disabled && styles.disabled,
			isFocused() && styles.focused,
		]
			.filter(Boolean)
			.join(" ");

	const labelContent = () => (
		<div class={styles.labelContent}>
			<label for={switchId()} class={styles.label}>
				{local.label}
				{local.required && <span class={styles.required}>*</span>}
			</label>
			{local.description && <div class={styles.description}>{local.description}</div>}
		</div>
	);

	return (
		<div class={containerClasses()}>
			{(labelPosition() === "left" || labelPosition() === "top") && labelContent()}

			<div class={switchClasses()}>
				<input
					type="checkbox"
					id={switchId()}
					name={local.name}
					checked={isChecked()}
					disabled={local.disabled}
					onChange={handleChange}
					onFocus={handleFocus}
					onBlur={handleBlur}
					class={styles.input}
					{...others}
				/>
				<span class={styles.slider}>
					<span class={styles.thumb}></span>
				</span>
			</div>

			{(labelPosition() === "right" || labelPosition() === "bottom") && labelContent()}

			{local.invalid && local.feedbackInvalid && (
				<div class={styles.feedback}>{local.feedbackInvalid}</div>
			)}
		</div>
	);
}

export default Switch;
