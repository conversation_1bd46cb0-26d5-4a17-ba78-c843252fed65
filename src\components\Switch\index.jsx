import { createSignal, createEffect, splitProps } from "solid-js";
import styles from "./Switch.module.scss";

/**
 * Custom Switch Component with beautiful UI/UX
 * @param {Object} props
 * @param {string} props.name - Form field name
 * @param {string} props.id - Element ID
 * @param {string} props.label - Switch label text
 * @param {string} props.description - Optional description text
 * @param {boolean} props.checked - Controlled checked state
 * @param {boolean} props.defaultChecked - Default checked state
 * @param {boolean} props.disabled - Disabled state
 * @param {"row" | "column"} props.direction - Layout direction (default: "row")
 * @param {"left" | "right" | "top" | "bottom"} props.labelPosition - Label position relative to switch
 * @param {"sm" | "md" | "lg"} props.size - Switch size (default: "md")
 * @param {string} props.color - Switch color theme (default: "primary")
 * @param {Function} props.onChange - Change handler (event, checked) => void
 * @param {Function} props.onFocus - Focus handler
 * @param {Function} props.onBlur - Blur handler
 * @param {string} props.class - Additional CSS classes
 * @param {boolean} props.required - Required field indicator
 * @param {string} props.feedbackInvalid - Error message
 * @param {boolean} props.invalid - Invalid state
 */
export function Switch(props) {
	const [local, others] = splitProps(props, [
		"name",
		"id",
		"label",
		"description",
		"checked",
		"defaultChecked",
		"disabled",
		"direction",
		"labelPosition",
		"size",
		"color",
		"onChange",
		"onFocus",
		"onBlur",
		"class",
		"required",
		"feedbackInvalid",
		"invalid",
	]);

	const [isChecked, setIsChecked] = createSignal(local.defaultChecked || false);
	const [isFocused, setIsFocused] = createSignal(false);

	// Handle controlled vs uncontrolled
	createEffect(() => {
		if (local.checked !== undefined) {
			setIsChecked(local.checked);
		}
	});

	const direction = () => local.direction || "row";
	const labelPosition = () => local.labelPosition || (direction() === "row" ? "right" : "bottom");
	const size = () => local.size || "md";
	const color = () => local.color || "primary";
	const switchId = () => local.id || `switch-${Math.random().toString(36).substring(2, 9)}`;

	function handleChange(event) {
		console.log("handleChange called", event);
		if (local.disabled) return;

		const newValue = event?.target?.checked ?? !isChecked();
		console.log("newValue:", newValue);
		setIsChecked(newValue);

		// Call onChange with both event and value for flexibility
		if (typeof local.onChange === "function") {
			local.onChange(event, newValue);
		}
	}

	function handleClick(event) {
		console.log("handleClick called", event);
		if (local.disabled) return;

		const newValue = !isChecked();
		console.log("click newValue:", newValue);
		setIsChecked(newValue);

		// Call onChange with both event and value for flexibility
		if (typeof local.onChange === "function") {
			local.onChange(event, newValue);
		}
	}

	function handleFocus(event) {
		setIsFocused(true);
		if (typeof local.onFocus === "function") {
			local.onFocus(event);
		}
	}

	function handleBlur(event) {
		setIsFocused(false);
		if (typeof local.onBlur === "function") {
			local.onBlur(event);
		}
	}

	const containerClasses = () =>
		[
			styles.switchContainer,
			styles[`direction-${direction()}`],
			styles[`label-${labelPosition()}`],
			styles[`size-${size()}`],
			local.disabled && styles.disabled,
			local.invalid && styles.invalid,
			isFocused() && styles.focused,
			local.class,
		]
			.filter(Boolean)
			.join(" ");

	const switchClasses = () =>
		[
			styles.switch,
			styles[`color-${color()}`],
			isChecked() && styles.checked,
			local.disabled && styles.disabled,
			isFocused() && styles.focused,
		]
			.filter(Boolean)
			.join(" ");

	const labelContent = () => (
		<div class={styles.labelContent}>
			<label for={switchId()} class={styles.label}>
				{local.label}
				{local.required && <span class={styles.required}>*</span>}
			</label>
			{local.description && <div class={styles.description}>{local.description}</div>}
		</div>
	);

	return (
		<div class={containerClasses()}>
			<div class={styles.switchWrapper}>
				{(labelPosition() === "left" || labelPosition() === "top") && labelContent()}

				<div class={switchClasses()}>
					<input
						type="checkbox"
						id={switchId()}
						name={local.name}
						checked={isChecked()}
						disabled={local.disabled}
						onChange={handleChange}
						onClick={handleClick}
						onFocus={handleFocus}
						onBlur={handleBlur}
						class={styles.input}
						{...others}
					/>
					<span class={styles.slider}>
						<span class={styles.thumb} />
					</span>
				</div>

				{(labelPosition() === "right" || labelPosition() === "bottom") && labelContent()}
			</div>

			{/* Error message always appears below the switch */}
			{local.invalid && local.feedbackInvalid && (
				<div class={styles.feedback}>{local.feedbackInvalid}</div>
			)}
		</div>
	);
}

export default Switch;
