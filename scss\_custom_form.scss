.buymed-form-control {
  box-shadow: none;
  transition: 0.1s;

  &:focus,
  &.focus {
    border-color: #{$green};
    box-shadow: 0 0 0 1px #{$green};
  }

  &.is-valid {
    border-color: rgba(var(--bs-success-rgb));
    box-shadow: 0 0 0 1px rgba(var(--bs-success-rgb));
    &:focus,
    &.focus {
      border-color: rgba(var(--bs-success-rgb));
      box-shadow: 0 0 0 1px rgba(var(--bs-success-rgb));
    }
  }

  &.is-invalid {
    background-image: none;
    border-color: rgba(var(--bs-danger-rgb));
    box-shadow: 0 0 0 1px rgba(var(--bs-danger-rgb));
    &:focus,
    &.focus {
      border-color: rgba(var(--bs-danger-rgb));
      box-shadow: 0 0 0 1px rgba(var(--bs-danger-rgb));
    }
  }

  &.form-control-standard {
    border: none;
    border-bottom: 1px solid;
    border-radius: 0;

    &:focus,
    &.focus {
      border-color: #{$green};
      box-shadow: 0 1px 0 0 #{$green};
    }

    &.is-valid {
      border-color: rgba(var(--bs-success-rgb));
      box-shadow: 0 1px 0 0 rgba(var(--bs-success-rgb));
      &:focus,
      &.focus {
        border-color: rgba(var(--bs-success-rgb));
        box-shadow: 0 1px 0 0 rgba(var(--bs-success-rgb));
      }
    }

    &.is-invalid {
      border-color: rgba(var(--bs-danger-rgb));
      box-shadow: 0 1px 0 0 rgba(var(--bs-danger-rgb));
      &:focus,
      &.focus {
        border-color: rgba(var(--bs-danger-rgb));
        box-shadow: 0 1px 0 0 rgba(var(--bs-danger-rgb));
      }
    }
  }
}
