// Switch Component Styles
.switchContainer {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    position: relative;
    
    // Direction variants
    &.direction-row {
        flex-direction: row;
        align-items: center;
        
        &.label-left {
            flex-direction: row-reverse;
        }
    }
    
    &.direction-column {
        flex-direction: column;
        align-items: flex-start;
        
        &.label-bottom {
            flex-direction: column-reverse;
        }
    }
    
    // Disabled state
    &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        
        .labelContent {
            cursor: not-allowed;
        }
    }
    
    // Invalid state
    &.invalid {
        .switch {
            border-color: var(--bs-danger, #dc3545);
        }
    }
    
    // Focused state
    &.focused {
        .switch {
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb, 13, 110, 253), 0.25);
        }
    }
}

.labelContent {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    cursor: pointer;
    user-select: none;
}

.label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-bold, #1e1e1e);
    margin: 0;
    cursor: pointer;
    line-height: 1.4;
    
    .switchContainer.disabled & {
        color: var(--color-button-disabled, #919191);
    }
}

.required {
    color: var(--bs-danger, #dc3545);
    margin-left: 0.25rem;
}

.description {
    font-size: 0.75rem;
    color: var(--color-text-normal, #4c4c4c);
    line-height: 1.3;
    
    .switchContainer.disabled & {
        color: var(--color-button-disabled, #919191);
    }
}

.switch {
    position: relative;
    display: inline-block;
    border-radius: 1.5rem;
    background-color: var(--color-border, #e0e0e0);
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    
    // Size variants
    .switchContainer.size-sm & {
        width: 2.5rem;
        height: 1.25rem;
    }
    
    .switchContainer.size-md & {
        width: 3rem;
        height: 1.5rem;
    }
    
    .switchContainer.size-lg & {
        width: 3.5rem;
        height: 1.75rem;
    }
    
    // Color variants
    &.color-primary.checked {
        background-color: var(--bs-primary, #0d6efd);
    }
    
    &.color-success.checked {
        background-color: var(--bs-success, #198754);
    }
    
    &.color-warning.checked {
        background-color: var(--bs-warning, #ffc107);
    }
    
    &.color-danger.checked {
        background-color: var(--bs-danger, #dc3545);
    }
    
    &.color-info.checked {
        background-color: var(--bs-info, #0dcaf0);
    }
    
    // Hover effects
    &:hover:not(.disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    // Focus effects
    &.focused {
        outline: none;
        border-color: var(--bs-primary, #0d6efd);
    }
    
    // Disabled state
    &.disabled {
        cursor: not-allowed;
        background-color: var(--color-border-dark, #dadada);
        
        &:hover {
            transform: none;
            box-shadow: none;
        }
    }
}

.input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    pointer-events: none;
}

.thumb {
    position: absolute;
    
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    
    // Size variants
    .switchContainer.size-sm & {
        width: 1rem;
        height: 1rem;
    }
    
    .switchContainer.size-md & {
        width: 1.25rem;
        height: 1.25rem;
    }
    
    .switchContainer.size-lg & {
        width: 1.5rem;
        height: 1.5rem;
    }
    
    // Checked state - move thumb to right
    .switch.checked & {
        .switchContainer.size-sm & {
            transform: translateX(1.25rem);
        }
        
        .switchContainer.size-md & {
            transform: translateX(1.5rem);
        }
        
        .switchContainer.size-lg & {
            transform: translateX(1.75rem);
        }
    }
    
    // Hover effects
    .switch:hover:not(.disabled) & {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    // Active/pressed effect
    .switch:active:not(.disabled) & {
        transform: scale(0.95);
        
        &.checked {
            .switchContainer.size-sm & {
                transform: translateX(1.25rem) scale(0.95);
            }
            
            .switchContainer.size-md & {
                transform: translateX(1.5rem) scale(0.95);
            }
            
            .switchContainer.size-lg & {
                transform: translateX(1.75rem) scale(0.95);
            }
        }
    }
}

.feedback {
    font-size: 0.75rem;
    color: var(--bs-danger, #dc3545);
    margin-top: 0.25rem;
    line-height: 1.3;
}

// Animation for smooth transitions
@keyframes switchOn {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(1.5rem);
    }
}

@keyframes switchOff {
    0% {
        transform: translateX(1.5rem);
    }
    100% {
        transform: translateX(0);
    }
}

// Responsive design
@media (max-width: 576px) {
    .switchContainer.direction-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        
        &.label-left {
            flex-direction: column;
        }
    }
    
    .label {
        font-size: 0.8rem;
    }
    
    .description {
        font-size: 0.7rem;
    }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
    .switch {
        background-color: #374151;
        
        &.checked {
            &.color-primary {
                background-color: #3b82f6;
            }
        }
    }
    
    .label {
        color: #f9fafb;
    }
    
    .description {
        color: #d1d5db;
    }
}
