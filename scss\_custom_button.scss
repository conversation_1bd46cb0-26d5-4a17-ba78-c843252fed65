// .btn-success {
//     background-color: #{$green};
//     border-color: #{$green};
//     &:hover {
//         background-color: #{$green-btn-hover};
//         border-color: #{$green-btn-hover};
//     }
//     &:active {
//         box-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12);
//     }
// }

// .btn-outline-success {
//     background-color: white;
//     color: #{$green};
//     &:hover {
//         background-color: #{$green};
//         border-color: #{$green};
//         color: white;
//     }
//     &:active {
//         background-color: #{$green-btn-hover};
//         border-color: #{$green-btn-hover};
//         color: white !important;
//     }
// }

// .btn {
//     &:disabled,
//     &.disabled,
//     fieldset:disabled & {
//       color: var(--#{$prefix}btn-disabled-color);
//       pointer-events: none;
//       background-color: var(--#{$prefix}btn-disabled-bg);
//       background-image: if($enable-gradients, none, null); 
//       opacity: var(--#{$prefix}btn-disabled-opacity);
//       font-weight: var(--#{$prefix}btn-disabled-font-weight);
//       @include box-shadow(none);
//     }
// }

.btn-success, .btn-info {
    color: $white;
}