# Switch Component

A beautiful and flexible Switch component for SolidJS applications with customizable layouts, sizes, colors, and animations.

## Features

- ✨ Beautiful UI/UX with smooth animations
- 🎨 Multiple color themes (primary, success, warning, danger, info)
- 📏 Three size variants (sm, md, lg)
- 📱 Responsive design
- ♿ Accessibility support
- 🎯 Flexible layout options (row/column, label positioning)
- 🔧 Form integration ready
- 💪 TypeScript support
- 🌙 Dark mode support

## Basic Usage

```jsx
import Switch from "~/components/Switch";

function MyComponent() {
    const [isEnabled, setIsEnabled] = createSignal(false);
    
    return (
        <Switch
            label="Enable notifications"
            checked={isEnabled()}
            onChange={(e, value) => setIsEnabled(value)}
        />
    );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | `string` | - | Form field name |
| `id` | `string` | auto-generated | Element ID |
| `label` | `string` | - | Switch label text |
| `description` | `string` | - | Optional description text |
| `checked` | `boolean` | - | Controlled checked state |
| `defaultChecked` | `boolean` | `false` | Default checked state |
| `disabled` | `boolean` | `false` | Disabled state |
| `direction` | `"row" \| "column"` | `"row"` | Layout direction |
| `labelPosition` | `"left" \| "right" \| "top" \| "bottom"` | `"right"` | Label position |
| `size` | `"sm" \| "md" \| "lg"` | `"md"` | Switch size |
| `color` | `"primary" \| "success" \| "warning" \| "danger" \| "info"` | `"primary"` | Color theme |
| `onChange` | `(event, checked) => void` | - | Change handler |
| `onFocus` | `(event) => void` | - | Focus handler |
| `onBlur` | `(event) => void` | - | Blur handler |
| `class` | `string` | - | Additional CSS classes |
| `required` | `boolean` | `false` | Required field indicator |
| `feedbackInvalid` | `string` | - | Error message |
| `invalid` | `boolean` | `false` | Invalid state |

## Examples

### Basic Switch
```jsx
<Switch
    label="Enable feature"
    defaultChecked={true}
/>
```

### With Description
```jsx
<Switch
    label="Privacy Mode"
    description="Hide your online status from other users"
    color="success"
/>
```

### Different Sizes
```jsx
<Switch label="Small" size="sm" />
<Switch label="Medium" size="md" />
<Switch label="Large" size="lg" />
```

### Different Colors
```jsx
<Switch label="Primary" color="primary" defaultChecked />
<Switch label="Success" color="success" defaultChecked />
<Switch label="Warning" color="warning" defaultChecked />
<Switch label="Danger" color="danger" defaultChecked />
<Switch label="Info" color="info" defaultChecked />
```

### Layout Variations
```jsx
{/* Row layout with label on the right (default) */}
<Switch
    label="Auto-save"
    direction="row"
    labelPosition="right"
/>

{/* Row layout with label on the left */}
<Switch
    label="Dark Mode"
    direction="row"
    labelPosition="left"
/>

{/* Column layout with label on top */}
<Switch
    label="Sync Data"
    direction="column"
    labelPosition="top"
/>

{/* Column layout with label on bottom */}
<Switch
    label="Backup"
    direction="column"
    labelPosition="bottom"
/>
```

### Form Integration
```jsx
import { createForm } from "@felte/solid";

function MyForm() {
    const { form } = createForm({
        initialValues: {
            notifications: true,
            autoSave: false
        },
        onSubmit: (values) => {
            console.log(values);
        }
    });
    
    return (
        <form use:form>
            <Switch
                name="notifications"
                label="Email Notifications"
                description="Receive updates via email"
            />
            
            <Switch
                name="autoSave"
                label="Auto Save"
                description="Automatically save your work"
                color="success"
            />
            
            <button type="submit">Save Settings</button>
        </form>
    );
}
```

### Error State
```jsx
<Switch
    label="Terms and Conditions"
    description="You must agree to continue"
    required
    invalid={true}
    feedbackInvalid="Please accept the terms and conditions"
    color="danger"
/>
```

### Controlled Component
```jsx
function ControlledSwitch() {
    const [isChecked, setIsChecked] = createSignal(false);
    
    return (
        <div>
            <Switch
                label="Controlled Switch"
                checked={isChecked()}
                onChange={(e, value) => setIsChecked(value)}
            />
            
            <p>Current state: {isChecked() ? 'ON' : 'OFF'}</p>
            
            <button onClick={() => setIsChecked(!isChecked())}>
                Toggle Programmatically
            </button>
        </div>
    );
}
```

### Disabled State
```jsx
<Switch
    label="Disabled Switch"
    disabled={true}
    defaultChecked={true}
/>
```

## Styling

The component uses CSS modules for styling. You can override styles by:

1. **Using the `class` prop:**
```jsx
<Switch
    label="Custom styled"
    class="my-custom-switch"
/>
```

2. **CSS Custom Properties:**
```css
:root {
    --bs-primary: #your-primary-color;
    --bs-success: #your-success-color;
    /* etc... */
}
```

3. **SCSS Variables (if using SCSS):**
```scss
.my-custom-switch {
    --switch-width: 4rem;
    --switch-height: 2rem;
}
```

## Accessibility

The component includes proper accessibility features:
- Proper ARIA attributes
- Keyboard navigation support
- Focus indicators
- Screen reader support
- Semantic HTML structure

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers
- Supports CSS Grid and Flexbox

## Dependencies

- SolidJS
- @buymed/solidjs-component (for useTranslate)
- SCSS support for styling
