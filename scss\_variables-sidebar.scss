
// zIndex
$zindex-sidebar-backdrop: 1030 !default;

// Set mobile breakpoint
$mobile-breakpoint: md !default;

// $gray-base: #3c4b64 !default;
$high-emphasis-inverse: rgba($black, .87) !default;
$medium-emphasis-inverse: rgba($black, .6) !default;
$disabled-inverse: rgba($black, .38) !default;

// scss-docs-start sidebar-variables
$sidebar-width: 16rem !default;
$sidebar-widths: (
  sm: 12rem,
  lg: 20rem,
  xl: 24rem) !default;
$sidebar-padding-y: 0 !default;
$sidebar-padding-x: 0 !default;
$sidebar-color: $high-emphasis-inverse !default;
$sidebar-bg: $gray-100 !default; // set default color
$sidebar-border-width: 0 !default;
$sidebar-border-color: transparent !default;
$sidebar-transition: margin-left .15s,
  margin-right .15s,
  box-shadow .075s,
  transform .15s,
  width .15s,
  z-index 0s ease .15s !default;

$sidebar-brand-height: 4rem !default;
$sidebar-brand-color: $high-emphasis-inverse !default;
$sidebar-brand-bg: rgba($gray-600, .2) !default;

$sidebar-header-height: 4rem !default;
$sidebar-header-padding-y: .75rem !default;
$sidebar-header-padding-x: 1rem !default;
$sidebar-header-bg: rgba($gray-100, .2) !default;
$sidebar-header-height-transition: height .15s,
  padding .15s !default;

$sidebar-narrow-width: 4rem !default;

$sidebar-backdrop-bg: $gray-100 !default;
$sidebar-backdrop-opacity: .5 !default;

$sidebar-nav-title-padding-y: .75rem !default;
$sidebar-nav-title-padding-x: 1rem !default;
$sidebar-nav-title-margin-top: 1rem !default;
$sidebar-nav-title-color: $medium-emphasis-inverse !default;
$sidebar-nav-title-transition: height .15s,
  margin .15s !default;

$sidebar-nav-link-padding-y: .8445rem !default;
$sidebar-nav-link-padding-x: 1rem !default;
$sidebar-nav-link-color: $medium-emphasis-inverse !default;
$sidebar-nav-link-bg: transparent !default;
$sidebar-nav-link-border-width: 0 !default;
$sidebar-nav-link-border-color: transparent !default;
$sidebar-nav-link-border-radius: 0 !default;
$sidebar-nav-link-transition: background .15s ease,
  color .15s ease !default;
$sidebar-nav-link-icon-color: $medium-emphasis-inverse !default;

$sidebar-nav-link-hover-color: $high-emphasis-inverse !default;
$sidebar-nav-link-hover-bg: rgba($black, .05) !default;
$sidebar-nav-link-hover-icon-color: $high-emphasis-inverse !default;

$sidebar-nav-link-active-color: $high-emphasis-inverse !default;
$sidebar-nav-link-active-bg: rgba($black, .05) !default;
$sidebar-nav-link-active-icon-color: $high-emphasis-inverse !default;

$sidebar-nav-link-disabled-color: $disabled-inverse !default;
$sidebar-nav-link-disabled-icon-color: $sidebar-nav-link-icon-color !default;

$sidebar-nav-icon-width: 4rem !default;
$sidebar-nav-icon-height: 1.25rem !default;
$sidebar-nav-icon-font-size: $sidebar-nav-icon-height !default;

$sidebar-nav-group-bg: rgba(0, 0, 0, .2) !default;
$sidebar-nav-group-transition: background .15s ease-in-out !default;
$sidebar-nav-group-toggle-show-color: $sidebar-nav-link-color !default;

$sidebar-nav-group-items-padding-y: 0 !default;
$sidebar-nav-group-items-padding-x: 0 !default;
$sidebar-nav-group-items-transition: height .15s ease !default;

$sidebar-nav-group-indicator-color: $medium-emphasis-inverse !default;
$sidebar-nav-group-indicator-icon: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$sidebar-nav-group-indicator-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>") !default;
$sidebar-nav-group-indicator-hover-color: $sidebar-nav-link-hover-color !default;
$sidebar-nav-group-indicator-hover-icon: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$sidebar-nav-group-indicator-hover-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>") !default;
$sidebar-nav-group-indicator-transition: transform .15s !default;

$sidebar-footer-height: auto !default;
$sidebar-footer-padding-y: .75rem !default;
$sidebar-footer-padding-x: 1rem !default;
$sidebar-footer-bg: rgba($black, .2) !default;
$sidebar-footer-height-transition: height .15s,
  padding .15s !default;

$sidebar-toggler-height: 3rem !default;
$sidebar-toggler-bg: rgba($black, .2) !default;
$sidebar-toggler-transition: transform .15s !default;

$sidebar-toggler-indicator-width: 4rem !default;
$sidebar-toggler-indicator-height: 3rem !default;
$sidebar-toggler-indicator-color: $gray-600 !default;
$sidebar-toggler-indicator-icon: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='#{$sidebar-toggler-indicator-color}' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E") !default;
$sidebar-toggler-hover-bg: rgba(0, 0, 0, .3) !default;
$sidebar-toggler-indicator-hover-color: $sidebar-nav-link-hover-color !default;
$sidebar-toggler-indicator-hover-icon: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='#{$sidebar-toggler-indicator-hover-color}' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E") !default;